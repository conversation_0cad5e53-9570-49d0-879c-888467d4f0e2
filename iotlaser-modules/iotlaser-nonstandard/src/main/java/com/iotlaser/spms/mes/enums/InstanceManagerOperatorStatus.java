package com.iotlaser.spms.mes.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生产设备管理执行状态枚举
 *
 * <AUTHOR>
 * @date 2025/06/03
 */
@Getter
@AllArgsConstructor
public enum InstanceManagerOperatorStatus {

    WAITING("waiting", "待开始"),
    START("start", "开始"),
    PAUSE("pause", "暂停"),
    OVER("over", "结束"),
    ;
    /**
     * 状态
     */
    private final String status;

    /**
     * 描述
     */
    private final String desc;
}
