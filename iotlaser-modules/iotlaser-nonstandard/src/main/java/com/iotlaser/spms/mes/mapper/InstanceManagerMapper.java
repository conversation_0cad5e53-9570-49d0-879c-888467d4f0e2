package com.iotlaser.spms.mes.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.mes.domain.InstanceManager;
import com.iotlaser.spms.mes.domain.vo.InstanceManagerVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 生产设备管理Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-03
 */
public interface InstanceManagerMapper extends BaseMapperPlus<InstanceManager, InstanceManagerVo> {


    /**
     * 查询生产设备管理及其关联信息
     */
    InstanceManager queryByIdWith(@Param("managerId") Long managerId);

    /**
     * 分页查询生产设备管理及其关联信息
     */
    List<InstanceManager> queryPageListWith(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<InstanceManager> wrapper);

    /**
     * 查询生产设备管理统计信息
     */
    List<InstanceManager> queryCountList(@Param("page") Page<Object> page, @Param("type") String type, @Param("formatter") String formatter, @Param("beginSendTime") String beginSendTime, @Param("endSendTime") String endSendTime);


}
