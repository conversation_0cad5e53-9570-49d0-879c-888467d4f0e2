package com.iotlaser.spms.mes.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生产设备管理执行工序枚举
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Getter
@AllArgsConstructor
public enum InstanceManagerOperatorProcess {

    DEV_DESIGN_STAFF("dev_design_staff", "设计部门"),
    ERP_PURCHASING_STAFF("erp_purchasing_staff", "采购部门"),
    MES_PRODUCTION_SUPERVISOR_STAFF("mes_production_supervisor_staff", "生产部门"),
    ;
    /**
     * 类型
     */
    private final String process;

    /**
     * 描述
     */
    private final String desc;
}
