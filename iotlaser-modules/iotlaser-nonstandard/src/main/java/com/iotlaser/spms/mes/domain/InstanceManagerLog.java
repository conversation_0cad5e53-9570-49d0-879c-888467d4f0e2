package com.iotlaser.spms.mes.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 生产设备管理日志对象 mes_instance_manager_log
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mes_instance_manager_log")
public class InstanceManagerLog extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @TableId(value = "log_id")
    private Long logId;

    /**
     * 管理ID
     */
    private Long managerId;

    /**
     * 执行人ID
     */
    private Long operatorId;

    /**
     * 执行人姓名
     */
    private String operatorName;

    /**
     * 执行工序
     */
    private String operatorProcess;

    /**
     * 执行状态
     */
    private String operatorStatus;

    /**
     * 执行时间
     */
    private Date operatorTime;

    /**
     * 执行说明
     */
    private String operatorRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
