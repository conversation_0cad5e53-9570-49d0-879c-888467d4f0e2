package com.iotlaser.spms.mes.domain.bo;

import com.iotlaser.spms.mes.domain.InstanceManagerLog;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 生产设备管理日志业务对象 mes_instance_manager_log
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InstanceManagerLog.class, reverseConvertGenerate = false)
public class InstanceManagerLogBo extends BaseEntity {

    /**
     * 日志ID
     */
    @NotNull(message = "日志ID不能为空", groups = {EditGroup.class})
    private Long logId;

    /**
     * 管理ID
     */
    @NotNull(message = "管理ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long managerId;

    /**
     * 执行人ID
     */
    private Long operatorId;

    /**
     * 执行人姓名
     */
    private String operatorName;

    /**
     * 执行工序
     */
    private String operatorProcess;

    /**
     * 执行状态
     */
    private String operatorStatus;

    /**
     * 执行时间
     */
    private Date operatorTime;

    /**
     * 执行说明
     */
    private String operatorRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
