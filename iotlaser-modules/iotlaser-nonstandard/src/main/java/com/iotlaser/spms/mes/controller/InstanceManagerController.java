package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.InstanceManagerBo;
import com.iotlaser.spms.mes.domain.vo.InstanceManagerVo;
import com.iotlaser.spms.mes.service.IInstanceManagerService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产设备管理
 *
 * <AUTHOR> Kai
 * @date 2025-06-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/instanceManager")
public class InstanceManagerController extends BaseController {

    private final IInstanceManagerService instanceManagerService;

    /**
     * 查询生产设备管理列表
     */
    @SaCheckPermission("mes:instanceManager:list")
    @GetMapping("/list")
    public TableDataInfo<InstanceManagerVo> list(InstanceManagerBo bo, PageQuery pageQuery) {
        return instanceManagerService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产设备管理列表
     */
    @SaCheckPermission("mes:instanceManager:export")
    @Log(title = "生产设备管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InstanceManagerBo bo, HttpServletResponse response) {
        List<InstanceManagerVo> list = instanceManagerService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产设备管理", InstanceManagerVo.class, response);
    }

    /**
     * 获取生产设备管理详细信息
     *
     * @param managerId 主键
     */
    @SaCheckPermission("mes:instanceManager:query")
    @GetMapping("/{managerId}")
    public R<InstanceManagerVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long managerId) {
        return R.ok(instanceManagerService.queryById(managerId));
    }

    /**
     * 新增生产设备管理
     */
    @SaCheckPermission("mes:instanceManager:add")
    @Log(title = "生产设备管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InstanceManagerBo bo) {
        return toAjax(instanceManagerService.insertByBo(bo));
    }

    /**
     * 修改生产设备管理
     */
    @SaCheckPermission("mes:instanceManager:edit")
    @Log(title = "生产设备管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InstanceManagerBo bo) {
        return toAjax(instanceManagerService.updateByBo(bo));
    }

    /**
     * 删除生产设备管理
     *
     * @param managerIds 主键串
     */
    @SaCheckPermission("mes:instanceManager:remove")
    @Log(title = "生产设备管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{managerIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] managerIds) {
        return toAjax(instanceManagerService.deleteWithValidByIds(List.of(managerIds), true));
    }

    /**
     * 生产设备管理执行
     */
    @SaCheckPermission("mes:instanceManager:execute")
    @Log(title = "生产设备管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("execute")
    public R<Void> execute(@Validated(EditGroup.class) @RequestBody InstanceManagerBo bo) {
        return toAjax(instanceManagerService.execute(bo));
    }

    /**
     * 获取生产设备管理以及关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:instanceManager:query")
    @GetMapping("with/{id}")
    public R<InstanceManagerVo> queryByIdWith(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(instanceManagerService.queryByIdWith(id));
    }

    /**
     * 查询生产设备管理列表以及关联详细信息
     */
    @SaCheckPermission("mes:instanceManager:list")
    @GetMapping("with/list")
    public TableDataInfo<InstanceManagerVo> queryPageListWith(InstanceManagerBo bo, PageQuery pageQuery) {
        return instanceManagerService.queryPageListWith(bo, pageQuery);
    }

    /**
     * 查询生产设备管理统计信息
     */
    @SaCheckPermission("mes:instanceManager:list")
    @GetMapping("count/list")
    public TableDataInfo<InstanceManagerVo> queryCountList(InstanceManagerBo bo, PageQuery pageQuery) {
        return instanceManagerService.queryCountList(bo, pageQuery);
    }

}
