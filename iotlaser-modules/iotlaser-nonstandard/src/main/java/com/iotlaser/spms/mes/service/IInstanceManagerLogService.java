package com.iotlaser.spms.mes.service;

import com.iotlaser.spms.mes.domain.bo.InstanceManagerLogBo;
import com.iotlaser.spms.mes.domain.vo.InstanceManagerLogVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 生产设备管理日志Service接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-03
 */
public interface IInstanceManagerLogService {

    /**
     * 查询生产设备管理日志
     *
     * @param logId 主键
     * @return 生产设备管理日志
     */
    InstanceManagerLogVo queryById(Long logId);

    /**
     * 分页查询生产设备管理日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产设备管理日志分页列表
     */
    TableDataInfo<InstanceManagerLogVo> queryPageList(InstanceManagerLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的生产设备管理日志列表
     *
     * @param bo 查询条件
     * @return 生产设备管理日志列表
     */
    List<InstanceManagerLogVo> queryList(InstanceManagerLogBo bo);

    /**
     * 新增生产设备管理日志
     *
     * @param bo 生产设备管理日志
     * @return 是否新增成功
     */
    Boolean insertByBo(InstanceManagerLogBo bo);

    /**
     * 修改生产设备管理日志
     *
     * @param bo 生产设备管理日志
     * @return 是否修改成功
     */
    Boolean updateByBo(InstanceManagerLogBo bo);

    /**
     * 校验并批量删除生产设备管理日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
