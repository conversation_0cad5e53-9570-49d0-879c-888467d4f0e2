package com.iotlaser.spms.mes.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 生产设备管理对象 mes_instance_manager
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mes_instance_manager")
public class InstanceManager extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 管理ID
     */
    @TableId(value = "manager_id")
    private Long managerId;

    /**
     * 当前执行人ID
     */
    private Long currentOperatorId;

    /**
     * 当前执行人姓名
     */
    private String currentOperatorName;

    /**
     * 当前执行工序
     */
    private String currentOperatorProcess;

    /**
     * 当前执行状态
     */
    private String currentOperatorStatus;

    /**
     * 当前执行时间
     */
    private Date currentOperatorTime;

    /**
     * 当前执行说明
     */
    private String currentOperatorRemark;

    /**
     * 下单日期
     */
    private Date orderTime;

    /**
     * 编号
     */
    private String managerCode;

    /**
     * 客户单位
     */
    private String customerName;

    /**
     * 设备名称
     */
    private String productName;

    /**
     * 产品型号
     */
    private String productSpecs;

    /**
     * 业务员ID
     */
    private Long businessId;

    /**
     * 业务员姓名
     */
    private String businessName;

    /**
     * 计划交期
     */
    private Date planTime;

    /**
     * 送货日期
     */
    private Date sendTime;

    /**
     * 车间生产负责人ID
     */
    private Long productionSupervisorId;

    /**
     * 车间生产负责人姓名
     */
    private String productionSupervisorName;

    /**
     * 客户要求摘要
     */
    private String customerRequirement;

    /**
     * 应用行业及样品
     */
    private String customerIndustry;

    /**
     * 发货地址
     */
    private String shipAddress;

    /**
     * 成本金额
     */
    private BigDecimal amountCost;

    /**
     * 销售金额
     */
    private BigDecimal amountSale;

    /**
     * 已收金额
     */
    private BigDecimal amountReceived;

    /**
     * 未收金额
     */
    private BigDecimal amountUnreceived;

    /**
     * 提成金额
     */
    private BigDecimal amountCommission;

    /**
     * 业务提成率
     */
    private BigDecimal commissionRate;

    /**
     * 管理配置
     */
    private String managerOptions;

    /**
     * 管理状态
     */
    private String managerStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


    /**
     * 生产设备管理日志 (ONE_TO_MANY)
     */
    @TableField(exist = false)
    private List<InstanceManagerLog> logs;


}
