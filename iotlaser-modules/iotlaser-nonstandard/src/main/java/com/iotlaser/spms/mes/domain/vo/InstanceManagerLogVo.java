package com.iotlaser.spms.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.mes.domain.InstanceManagerLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 生产设备管理日志视图对象 mes_instance_manager_log
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InstanceManagerLog.class)
public class InstanceManagerLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @ExcelProperty(value = "日志ID")
    private Long logId;

    /**
     * 管理ID
     */
    @ExcelProperty(value = "管理ID")
    private Long managerId;

    /**
     * 执行人ID
     */
    @ExcelProperty(value = "执行人ID")
    private Long operatorId;

    /**
     * 执行人姓名
     */
    @ExcelProperty(value = "执行人姓名")
    private String operatorName;

    /**
     * 执行工序
     */
    @ExcelProperty(value = "执行工序", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_instance_manager_operator_process")
    private String operatorProcess;

    /**
     * 执行状态
     */
    @ExcelProperty(value = "执行状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_instance_manager_operator_status")
    private String operatorStatus;

    /**
     * 执行时间
     */
    @ExcelProperty(value = "执行时间")
    private Date operatorTime;

    /**
     * 执行说明
     */
    @ExcelProperty(value = "执行说明")
    private String operatorRemark;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
