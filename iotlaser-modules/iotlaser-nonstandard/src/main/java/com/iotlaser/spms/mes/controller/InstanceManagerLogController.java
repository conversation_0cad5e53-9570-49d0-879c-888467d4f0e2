package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.InstanceManagerLogBo;
import com.iotlaser.spms.mes.domain.vo.InstanceManagerLogVo;
import com.iotlaser.spms.mes.service.IInstanceManagerLogService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产设备管理日志
 *
 * <AUTHOR> Kai
 * @date 2025-06-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/instanceManagerLog")
public class InstanceManagerLogController extends BaseController {

    private final IInstanceManagerLogService instanceManagerLogService;

    /**
     * 查询生产设备管理日志列表
     */
    @SaCheckPermission("mes:instanceManagerLog:list")
    @GetMapping("/list")
    public TableDataInfo<InstanceManagerLogVo> list(InstanceManagerLogBo bo, PageQuery pageQuery) {
        return instanceManagerLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产设备管理日志列表
     */
    @SaCheckPermission("mes:instanceManagerLog:export")
    @Log(title = "生产设备管理日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InstanceManagerLogBo bo, HttpServletResponse response) {
        List<InstanceManagerLogVo> list = instanceManagerLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产设备管理日志", InstanceManagerLogVo.class, response);
    }

    /**
     * 获取生产设备管理日志详细信息
     *
     * @param logId 主键
     */
    @SaCheckPermission("mes:instanceManagerLog:query")
    @GetMapping("/{logId}")
    public R<InstanceManagerLogVo> getInfo(@NotNull(message = "主键不能为空")
                                           @PathVariable Long logId) {
        return R.ok(instanceManagerLogService.queryById(logId));
    }

    /**
     * 新增生产设备管理日志
     */
    @SaCheckPermission("mes:instanceManagerLog:add")
    @Log(title = "生产设备管理日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InstanceManagerLogBo bo) {
        return toAjax(instanceManagerLogService.insertByBo(bo));
    }

    /**
     * 修改生产设备管理日志
     */
    @SaCheckPermission("mes:instanceManagerLog:edit")
    @Log(title = "生产设备管理日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InstanceManagerLogBo bo) {
        return toAjax(instanceManagerLogService.updateByBo(bo));
    }

    /**
     * 删除生产设备管理日志
     *
     * @param logIds 主键串
     */
    @SaCheckPermission("mes:instanceManagerLog:remove")
    @Log(title = "生产设备管理日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] logIds) {
        return toAjax(instanceManagerLogService.deleteWithValidByIds(List.of(logIds), true));
    }
}
