package com.iotlaser.spms.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.iotlaser.spms.mes.domain.InstanceManager;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 生产设备管理视图对象 mes_instance_manager
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InstanceManager.class)
public class InstanceManagerVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 管理ID
     */
    private Long managerId;

    /**
     * 当前执行人ID
     */
    private Long currentOperatorId;

    /**
     * 当前执行人姓名
     */
    @ExcelProperty(value = "当前执行人")
    private String currentOperatorName;

    /**
     * 当前执行工序
     */
    @ExcelProperty(value = "当前执行工序", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_instance_manager_operator_process")
    private String currentOperatorProcess;

    /**
     * 当前执行状态
     */
    @ExcelProperty(value = "当前执行状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_instance_manager_operator_status")
    private String currentOperatorStatus;

    /**
     * 当前执行时间
     */
    @ExcelProperty(value = "当前执行时间")
    private Date currentOperatorTime;

    /**
     * 当前执行说明
     */
    @ExcelProperty(value = "当前执行说明")
    private String currentOperatorRemark;

    /**
     * 下单日期
     */
    @ExcelProperty(value = "下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date orderTime;

    /**
     * 编号
     */
    @ExcelProperty(value = "编号")
    private String managerCode;

    /**
     * 客户单位
     */
    @ExcelProperty(value = "客户单位")
    private String customerName;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String productName;

    /**
     * 产品型号
     */
    @ExcelProperty(value = "产品型号")
    private String productSpecs;

    /**
     * 业务员ID
     */
    @ExcelProperty(value = "业务员ID")
    private Long businessId;

    /**
     * 业务员姓名
     */
    @ExcelProperty(value = "业务员姓名")
    private String businessName;

    /**
     * 计划交期
     */
    @ExcelProperty(value = "计划交期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planTime;

    /**
     * 送货日期
     */
    @ExcelProperty(value = "送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date sendTime;

    /**
     * 车间生产负责人ID
     */
    private Long productionSupervisorId;

    /**
     * 车间生产负责人姓名
     */
    @ExcelProperty(value = "车间生产负责人姓名")
    private String productionSupervisorName;

    /**
     * 客户要求摘要
     */
    @ExcelProperty(value = "客户要求摘要")
    private String customerRequirement;

    /**
     * 应用行业及样品
     */
    @ExcelProperty(value = "应用行业及样品")
    private String customerIndustry;

    /**
     * 发货地址
     */
    @ExcelProperty(value = "发货地址")
    private String shipAddress;

    /**
     * 成本金额
     */
    @ExcelProperty(value = "成本金额")
    private BigDecimal amountCost;

    /**
     * 销售金额
     */
    @ExcelProperty(value = "销售金额")
    private BigDecimal amountSale;

    /**
     * 已收金额
     */
    @ExcelProperty(value = "已收金额")
    private BigDecimal amountReceived;

    /**
     * 未收金额
     */
    @ExcelProperty(value = "未收金额")
    private BigDecimal amountUnreceived;

    /**
     * 提成金额
     */
    @ExcelProperty(value = "提成金额")
    private BigDecimal amountCommission;

    /**
     * 业务提成率
     */
    @ExcelProperty(value = "业务提成率")
    private BigDecimal commissionRate;

    /**
     * 管理配置
     */
    private String managerOptions;

    /**
     * 管理状态
     */
    @ExcelProperty(value = "管理状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wf_business_status")
    private String managerStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


    /**
     * 生产设备管理日志 (ONE_TO_MANY)
     */
    private List<InstanceManagerLogVo> logs;


}
