package com.iotlaser.spms.mes.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生产设备管理状态枚举
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Getter
@AllArgsConstructor
public enum InstanceManagerStatus {

    DRAFT("draft", "草稿"),
    WAITING("waiting", "执行中"),
    FINISH("finish", "已完成"),
    ;
    /**
     * 状态
     */
    private final String status;

    /**
     * 描述
     */
    private final String desc;
}
