package com.iotlaser.spms.mes.service;

import com.iotlaser.spms.mes.domain.bo.InstanceManagerBo;
import com.iotlaser.spms.mes.domain.vo.InstanceManagerVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 生产设备管理Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-03
 */
public interface IInstanceManagerService {

    /**
     * 查询生产设备管理
     *
     * @param managerId 主键
     * @return 生产设备管理
     */
    InstanceManagerVo queryById(Long managerId);

    /**
     * 分页查询生产设备管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产设备管理分页列表
     */
    TableDataInfo<InstanceManagerVo> queryPageList(InstanceManagerBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的生产设备管理列表
     *
     * @param bo 查询条件
     * @return 生产设备管理列表
     */
    List<InstanceManagerVo> queryList(InstanceManagerBo bo);

    /**
     * 新增生产设备管理
     *
     * @param bo 生产设备管理
     * @return 是否新增成功
     */
    Boolean insertByBo(InstanceManagerBo bo);

    /**
     * 修改生产设备管理
     *
     * @param bo 生产设备管理
     * @return 是否修改成功
     */
    Boolean updateByBo(InstanceManagerBo bo);

    /**
     * 生产设备管理执行
     *
     * @param bo 生产设备管理
     * @return 是否修改成功
     */
    Boolean execute(InstanceManagerBo bo);

    /**
     * 校验并批量删除生产设备管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询生产设备管理及其关联信息
     *
     * @param managerId 主键
     * @return 生产设备管理
     */
    InstanceManagerVo queryByIdWith(Long managerId);

    /**
     * 分页查询生产设备管理列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产设备管理分页列表
     */
    TableDataInfo<InstanceManagerVo> queryPageListWith(InstanceManagerBo bo, PageQuery pageQuery);

    /**
     * 查询生产设备管理统计信息
     */
    TableDataInfo<InstanceManagerVo> queryCountList(InstanceManagerBo bo, PageQuery pageQuery);
}
