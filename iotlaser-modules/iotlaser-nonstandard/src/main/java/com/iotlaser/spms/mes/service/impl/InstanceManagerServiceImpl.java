package com.iotlaser.spms.mes.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.mes.domain.InstanceManager;
import com.iotlaser.spms.mes.domain.bo.InstanceManagerBo;
import com.iotlaser.spms.mes.domain.bo.InstanceManagerLogBo;
import com.iotlaser.spms.mes.domain.vo.InstanceManagerVo;
import com.iotlaser.spms.mes.enums.InstanceManagerOperatorProcess;
import com.iotlaser.spms.mes.enums.InstanceManagerOperatorStatus;
import com.iotlaser.spms.mes.mapper.InstanceManagerMapper;
import com.iotlaser.spms.mes.service.IInstanceManagerLogService;
import com.iotlaser.spms.mes.service.IInstanceManagerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.domain.dto.RoleDTO;
import org.dromara.common.core.enums.BusinessStatusEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 生产设备管理Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-03
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InstanceManagerServiceImpl implements IInstanceManagerService {

    private final InstanceManagerMapper baseMapper;
    private final IInstanceManagerLogService instanceManagerLogService;

    /**
     * 查询生产设备管理
     *
     * @param managerId 主键
     * @return 生产设备管理
     */
    @Override
    public InstanceManagerVo queryById(Long managerId) {
        InstanceManagerVo vo = baseMapper.selectVoById(managerId);
        filerData(vo);
        return vo;
    }

    /**
     * 分页查询生产设备管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产设备管理分页列表
     */
    @Override
    public TableDataInfo<InstanceManagerVo> queryPageList(InstanceManagerBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InstanceManager> lqw = buildQueryWrapper(bo);
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            lqw.orderByDesc(InstanceManager::getManagerId);
        }
        Page<InstanceManagerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        filerData(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产设备管理列表
     *
     * @param bo 查询条件
     * @return 生产设备管理列表
     */
    @Override
    public List<InstanceManagerVo> queryList(InstanceManagerBo bo) {
        LambdaQueryWrapper<InstanceManager> lqw = buildQueryWrapper(bo);
        List<InstanceManagerVo> vos = baseMapper.selectVoList(lqw);
        filerData(vos);
        return vos;
    }

    private LambdaQueryWrapper<InstanceManager> buildQueryWrapper(InstanceManagerBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InstanceManager> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCurrentOperatorId() != null, InstanceManager::getCurrentOperatorId, bo.getCurrentOperatorId());
        lqw.like(StringUtils.isNotBlank(bo.getCurrentOperatorName()), InstanceManager::getCurrentOperatorName, bo.getCurrentOperatorName());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrentOperatorStatus()), InstanceManager::getCurrentOperatorStatus, bo.getCurrentOperatorStatus());
        lqw.eq(bo.getCurrentOperatorTime() != null, InstanceManager::getCurrentOperatorTime, bo.getCurrentOperatorTime());
        lqw.like(StringUtils.isNotBlank(bo.getCurrentOperatorRemark()), InstanceManager::getCurrentOperatorRemark, bo.getCurrentOperatorRemark());
        lqw.eq(bo.getOrderTime() != null, InstanceManager::getOrderTime, bo.getOrderTime());
        lqw.like(StringUtils.isNotBlank(bo.getManagerCode()), InstanceManager::getManagerCode, bo.getManagerCode());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), InstanceManager::getCustomerName, bo.getCustomerName());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), InstanceManager::getProductName, bo.getProductName());
        lqw.like(StringUtils.isNotBlank(bo.getProductSpecs()), InstanceManager::getProductSpecs, bo.getProductSpecs());
        lqw.eq(bo.getBusinessId() != null, InstanceManager::getBusinessId, bo.getBusinessId());
        lqw.like(StringUtils.isNotBlank(bo.getBusinessName()), InstanceManager::getBusinessName, bo.getBusinessName());
        lqw.eq(bo.getPlanTime() != null, InstanceManager::getPlanTime, bo.getPlanTime());
        lqw.eq(bo.getSendTime() != null, InstanceManager::getSendTime, bo.getSendTime());
        lqw.eq(bo.getProductionSupervisorId() != null, InstanceManager::getProductionSupervisorId, bo.getProductionSupervisorId());
        lqw.like(StringUtils.isNotBlank(bo.getProductionSupervisorName()), InstanceManager::getProductionSupervisorName, bo.getProductionSupervisorName());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerRequirement()), InstanceManager::getCustomerRequirement, bo.getCustomerRequirement());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerIndustry()), InstanceManager::getCustomerIndustry, bo.getCustomerIndustry());
        lqw.like(StringUtils.isNotBlank(bo.getShipAddress()), InstanceManager::getShipAddress, bo.getShipAddress());
        lqw.eq(bo.getAmountCost() != null, InstanceManager::getAmountCost, bo.getAmountCost());
        lqw.eq(bo.getAmountSale() != null, InstanceManager::getAmountSale, bo.getAmountSale());
        lqw.eq(bo.getAmountReceived() != null, InstanceManager::getAmountReceived, bo.getAmountReceived());
        lqw.eq(bo.getAmountUnreceived() != null, InstanceManager::getAmountUnreceived, bo.getAmountUnreceived());
        lqw.eq(StringUtils.isNotBlank(bo.getManagerStatus()), InstanceManager::getManagerStatus, bo.getManagerStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), InstanceManager::getStatus, bo.getStatus());
        lqw.between(params.get("beginCurrentOperatorTime") != null && params.get("endCurrentOperatorTime") != null,
            InstanceManager::getCurrentOperatorTime, params.get("beginCurrentOperatorTime"), params.get("endCurrentOperatorTime"));
        lqw.between(params.get("beginOrderTime") != null && params.get("endOrderTime") != null,
            InstanceManager::getOrderTime, params.get("beginOrderTime"), params.get("endOrderTime"));
        lqw.between(params.get("beginPlanTime") != null && params.get("endPlanTime") != null,
            InstanceManager::getPlanTime, params.get("beginPlanTime"), params.get("endPlanTime"));
        lqw.between(params.get("beginSendTime") != null && params.get("endSendTime") != null,
            InstanceManager::getSendTime, params.get("beginSendTime"), params.get("endSendTime"));
        return lqw;
    }

    /**
     * 新增生产设备管理
     *
     * @param bo 生产设备管理
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(InstanceManagerBo bo) {
        try {
            InstanceManager add = MapstructUtils.convert(bo, InstanceManager.class);
            // 设置管理状态为草稿
            bo.setManagerStatus(BusinessStatusEnum.DRAFT.getStatus());
            JSONObject options = new JSONObject();
            options.putOpt(InstanceManagerOperatorProcess.DEV_DESIGN_STAFF.getProcess(), InstanceManagerOperatorStatus.WAITING);
            options.putOpt(InstanceManagerOperatorProcess.ERP_PURCHASING_STAFF.getProcess(), InstanceManagerOperatorStatus.WAITING);
            options.putOpt(InstanceManagerOperatorProcess.MES_PRODUCTION_SUPERVISOR_STAFF.getProcess(), InstanceManagerOperatorStatus.WAITING);
            bo.setManagerOptions(options.toString());
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增生产设备管理失败");
            }

            bo.setManagerId(add.getManagerId());
            log.info("新增生产设备管理成功：{}", add.getManagerCode());
            return true;
        } catch (Exception e) {
            log.error("新增生产设备管理失败：{}", e.getMessage(), e);
            throw new ServiceException("新增生产设备管理失败：" + e.getMessage());
        }
    }

    /**
     * 修改生产设备管理
     *
     * @param bo 生产设备管理
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(InstanceManagerBo bo) {
        try {
            InstanceManager update = MapstructUtils.convert(bo, InstanceManager.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改生产设备管理失败：记录不存在或数据未变更");
            }

            log.info("修改生产设备管理成功：{}", update.getManagerCode());
            return true;
        } catch (Exception e) {
            log.error("修改生产设备管理失败：{}", e.getMessage(), e);
            throw new ServiceException("修改生产设备管理失败：" + e.getMessage());
        }
    }

    /**
     * 生产设备管理执行
     *
     * @param bo 生产设备管理
     * @return 是否修改成功
     */
    @Override
    public Boolean execute(InstanceManagerBo bo) {
        if (StringUtils.isEmpty(bo.getCurrentOperatorRemark())) {
            throw new ServiceException("执行说明不能为空");
        }
        bo.setCurrentOperatorId(LoginHelper.getUserId());
        bo.setCurrentOperatorName(LoginHelper.getLoginUser().getNickname());
        bo.setCurrentOperatorProcess(bo.getCurrentOperatorProcess());
        bo.setCurrentOperatorStatus(bo.getCurrentOperatorStatus());
        bo.setCurrentOperatorTime(DateUtils.getNowDate());

        InstanceManagerVo vo = queryById(bo.getManagerId());
        JSONObject options = JSONUtil.parseObj(vo.getManagerOptions());
        List<String> roleKeys = LoginHelper.getLoginUser().getRoles().stream().map(RoleDTO::getRoleKey).toList();
        if (roleKeys.contains(InstanceManagerOperatorProcess.DEV_DESIGN_STAFF.getProcess())) {
            options.putOpt(InstanceManagerOperatorProcess.DEV_DESIGN_STAFF.getProcess(), bo.getCurrentOperatorStatus());
        } else if (roleKeys.contains(InstanceManagerOperatorProcess.ERP_PURCHASING_STAFF.getProcess())) {
            options.putOpt(InstanceManagerOperatorProcess.ERP_PURCHASING_STAFF.getProcess(), bo.getCurrentOperatorStatus());
        } else if (roleKeys.contains(InstanceManagerOperatorProcess.MES_PRODUCTION_SUPERVISOR_STAFF.getProcess())) {
            options.putOpt(InstanceManagerOperatorProcess.MES_PRODUCTION_SUPERVISOR_STAFF.getProcess(), bo.getCurrentOperatorStatus());
        }
        bo.setManagerOptions(options.toString());

        InstanceManagerLogBo log = new InstanceManagerLogBo();
        log.setManagerId(bo.getManagerId());
        log.setOperatorId(bo.getCurrentOperatorId());
        log.setOperatorName(bo.getCurrentOperatorName());
        log.setOperatorProcess(bo.getCurrentOperatorProcess());
        log.setOperatorStatus(bo.getCurrentOperatorStatus());
        log.setOperatorRemark(bo.getCurrentOperatorRemark());
        log.setOperatorTime(bo.getCurrentOperatorTime());
        instanceManagerLogService.insertByBo(log);

        InstanceManager update = MapstructUtils.convert(bo, InstanceManager.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InstanceManager entity) {

    }

    /**
     * 校验并批量删除生产设备管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验设备管理是否可以删除
            List<InstanceManager> managers = baseMapper.selectByIds(ids);
            for (InstanceManager manager : managers) {
                // 检查是否有关联的设备管理日志
                log.info("删除设备管理校验：设备管理【{}】", manager.getManagerId());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 查询生产设备管理及其关联信息
     *
     * @param managerId 主键
     * @return 生产设备管理
     */
    @Override
    public InstanceManagerVo queryByIdWith(Long managerId) {
        InstanceManagerVo vo = MapstructUtils.convert(baseMapper.queryByIdWith(managerId), InstanceManagerVo.class);
        filerData(vo);
        return vo;
    }

    /**
     * 分页查询生产设备管理列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产设备管理分页列表
     */
    @Override
    public TableDataInfo<InstanceManagerVo> queryPageListWith(InstanceManagerBo bo, PageQuery pageQuery) {
        QueryWrapper<InstanceManager> queryWrapper = buildQueryWrapperWith(bo);
        List<InstanceManagerVo> result = MapstructUtils.convert(baseMapper.queryPageListWith(pageQuery.build(), queryWrapper), InstanceManagerVo.class);
        filerData(result);
        return TableDataInfo.build(result);
    }

    /**
     * 查询生产设备管理统计信息
     */
    @Override
    public TableDataInfo<InstanceManagerVo> queryCountList(InstanceManagerBo bo, PageQuery pageQuery) {
        String type = StringUtils.defaultIfEmpty(bo.getRemark(), "month");
        String formatter;
        int year = DateUtil.year(DateUtils.getNowDate());
        String beginSendTime = bo.getParams().get("beginSendTime") == null ? year + "-01-01" : bo.getParams().get("beginSendTime").toString();
        String endSendTime = bo.getParams().get("beginSendTime") == null ? year + "-12-31" : bo.getParams().get("endSendTime").toString();

        if (type.equals("month")) {
            formatter = "'yyyy-MM'";
        } else if (type.equals("quarter")) {
            formatter = "'yyyy-\"Q\"Q'";
        } else {
            formatter = "'yyyy'";
        }
        type = "'" + type + "'";
        List<InstanceManagerVo> result = MapstructUtils.convert(baseMapper.queryCountList(pageQuery.build(), type, formatter, beginSendTime, endSendTime), InstanceManagerVo.class);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<InstanceManager> buildQueryWrapperWith(InstanceManagerBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<InstanceManager> wrapper = Wrappers.query();
        wrapper.eq("manager.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("manager.manager_id");
        wrapper.eq(bo.getCurrentOperatorId() != null, "manager.current_operator_id", bo.getCurrentOperatorId());
        wrapper.like(StringUtils.isNotBlank(bo.getCurrentOperatorName()), "manager.current_operator_name", bo.getCurrentOperatorName());
        wrapper.eq(StringUtils.isNotBlank(bo.getCurrentOperatorStatus()), "manager.current_operator_status", bo.getCurrentOperatorStatus());
        wrapper.eq(bo.getCurrentOperatorTime() != null, "manager.current_operator_time", bo.getCurrentOperatorTime());
        wrapper.eq(StringUtils.isNotBlank(bo.getCurrentOperatorRemark()), "manager.current_operator_remark", bo.getCurrentOperatorRemark());
        wrapper.eq(bo.getOrderTime() != null, "manager.order_time", bo.getOrderTime());
        wrapper.eq(StringUtils.isNotBlank(bo.getManagerCode()), "manager.manager_code", bo.getManagerCode());
        wrapper.like(StringUtils.isNotBlank(bo.getCustomerName()), "manager.customer_name", bo.getCustomerName());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "manager.product_name", bo.getProductName());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductSpecs()), "manager.product_specs", bo.getProductSpecs());
        wrapper.eq(bo.getBusinessId() != null, "manager.business_id", bo.getBusinessId());
        wrapper.like(StringUtils.isNotBlank(bo.getBusinessName()), "manager.business_name", bo.getBusinessName());
        wrapper.eq(bo.getPlanTime() != null, "manager.plan_time", bo.getPlanTime());
        wrapper.eq(bo.getSendTime() != null, "manager.send_time", bo.getSendTime());
        wrapper.eq(bo.getProductionSupervisorId() != null, "manager.production_supervisor_id", bo.getProductionSupervisorId());
        wrapper.like(StringUtils.isNotBlank(bo.getProductionSupervisorName()), "manager.production_supervisor_name", bo.getProductionSupervisorName());
        wrapper.eq(StringUtils.isNotBlank(bo.getCustomerRequirement()), "manager.customer_requirement", bo.getCustomerRequirement());
        wrapper.eq(StringUtils.isNotBlank(bo.getCustomerIndustry()), "manager.customer_industry", bo.getCustomerIndustry());
        wrapper.eq(StringUtils.isNotBlank(bo.getShipAddress()), "manager.ship_address ", bo.getShipAddress());
        // ✅ 优化：移除金额字段的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：wrapper.eq(bo.getAmountCost() != null, "manager.amount_cost ", bo.getAmountCost());
        // 原代码：wrapper.eq(bo.getAmountSale() != null, "manager.amount_sale", bo.getAmountSale());
        // 原代码：wrapper.eq(bo.getAmountReceived() != null, "manager.amount_received", bo.getAmountReceived());
        // 原代码：wrapper.eq(bo.getAmountUnreceived() != null, "manager.amount_unreceived", bo.getAmountUnreceived());
        // TODO: 如需要可以后续添加金额范围查询支持
        wrapper.eq(StringUtils.isNotBlank(bo.getManagerStatus()), "manager.manager_status", bo.getManagerStatus());
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "manager.status", bo.getStatus());
        return wrapper;
    }

    private void filerData(List<InstanceManagerVo> vos) {
        boolean flag = StpUtil.hasRoleOr("admin", "mes_admin", "erp_financial_staff");
        for (InstanceManagerVo vo : vos) {
            if (!flag) {
                filer(vo);
            }

        }
    }

    private void filerData(InstanceManagerVo vo) {
        boolean flag = StpUtil.hasRoleOr("admin", "mes_admin", "erp_financial_staff");
        if (!flag) {
            filer(vo);
        }
    }

    private void filer(InstanceManagerVo vo) {
        vo.setAmountSale(null);
        vo.setAmountCost(null);
        vo.setAmountReceived(null);
        vo.setAmountUnreceived(null);
    }
}
