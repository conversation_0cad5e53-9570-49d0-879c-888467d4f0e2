<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iotlaser.spms.mes.mapper.InstanceManagerMapper">

    <resultMap id="BaseResultMap" type="com.iotlaser.spms.mes.domain.InstanceManager">
        <result column="manager_id" property="managerId"/>
        <result column="current_operator_id" property="currentOperatorId"/>
        <result column="current_operator_name" property="currentOperatorName"/>
        <result column="current_operator_status" property="currentOperatorStatus"/>
        <result column="current_operator_time" property="currentOperatorTime"/>
        <result column="current_operator_remark" property="currentOperatorRemark"/>
        <result column="order_time" property="orderTime"/>
        <result column="manager_code" property="managerCode"/>
        <result column="customer_name" property="customerName"/>
        <result column="product_name" property="productName"/>
        <result column="product_specs" property="productSpecs"/>
        <result column="business_id" property="businessId"/>
        <result column="business_name" property="businessName"/>
        <result column="plan_time" property="planTime"/>
        <result column="send_time" property="sendTime"/>
        <result column="production_supervisor_id" property="productionSupervisorId"/>
        <result column="production_supervisor_name" property="productionSupervisorName"/>
        <result column="customer_requirement" property="customerRequirement"/>
        <result column="customer_industry" property="customerIndustry"/>
        <result column="ship_address" property="shipAddress"/>
        <result column="amount_cost" property="amountCost"/>
        <result column="amount_sale" property="amountSale"/>
        <result column="amount_received" property="amountReceived"/>
        <result column="amount_unreceived" property="amountUnreceived"/>
        <result column="amount_commission" property="amountCommission"/>
        <result column="commission_rate" property="commissionRate"/>
        <result column="manager_status" property="managerStatus"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <sql id="Base_Column_List">
        manager_id
        ,current_operator_id
        ,current_operator_name
        ,current_operator_status
        ,current_operator_time
        ,current_operator_remark
        ,order_time
        ,manager_code
        ,customer_name
        ,product_name
        ,product_specs
        ,business_id
        ,business_name
        ,plan_time
        ,send_time
        ,production_supervisor_id
        ,production_supervisor_name
        ,customer_requirement
        ,customer_industry
        ,ship_address
        ,amount_cost
        ,amount_sale
        ,amount_received
        ,amount_unreceived
        ,manager_status
        ,remark
        ,status
        ,del_flag
        ,create_by
        ,create_time
        ,update_by
        ,update_time
        ,create_dept
        ,tenant_id
    </sql>

    <resultMap id="InstanceManagerResultMap" type="com.iotlaser.spms.mes.domain.InstanceManager" extends="BaseResultMap">
        <collection property="logs" ofType="com.iotlaser.spms.mes.domain.InstanceManagerLog">
            <id column="sub_logs_log_id" property="logId"/>
            <result column="sub_logs_manager_id" property="managerId"/>
            <result column="sub_logs_operator_id" property="operatorId"/>
            <result column="sub_logs_operator_name" property="operatorName"/>
            <result column="sub_logs_operator_process" property="operatorProcess"/>
            <result column="sub_logs_operator_status" property="operatorStatus"/>
            <result column="sub_logs_operator_time" property="operatorTime"/>
            <result column="sub_logs_operator_remark" property="operatorRemark"/>
            <result column="sub_logs_remark" property="remark"/>
            <result column="sub_logs_status" property="status"/>
            <result column="sub_logs_del_flag" property="delFlag"/>
            <result column="sub_logs_create_by" property="createBy"/>
            <result column="sub_logs_create_time" property="createTime"/>
            <result column="sub_logs_update_by" property="updateBy"/>
            <result column="sub_logs_update_time" property="updateTime"/>
            <result column="sub_logs_create_dept" property="createDept"/>
            <result column="sub_logs_tenant_id" property="tenantId"/>
        </collection>
    </resultMap>


    <select id="queryByIdWith" resultMap="InstanceManagerResultMap">
        SELECT manager.*,
               logs.log_id          AS sub_logs_log_id,
               logs.manager_id      AS sub_logs_manager_id,
               logs.operator_id     AS sub_logs_operator_id,
               logs.operator_name   AS sub_logs_operator_name,
               logs.operator_process AS sub_logs_operator_process,
               logs.operator_status AS sub_logs_operator_status,
               logs.operator_time   AS sub_logs_operator_time,
               logs.operator_remark AS sub_logs_operator_remark,
               logs.remark          AS sub_logs_remark,
               logs.status          AS sub_logs_status,
               logs.del_flag        AS sub_logs_del_flag,
               logs.create_by       AS sub_logs_create_by,
               logs.create_time     AS sub_logs_create_time,
               logs.update_by       AS sub_logs_update_by,
               logs.update_time     AS sub_logs_update_time,
               logs.create_dept     AS sub_logs_create_dept,
               logs.tenant_id       AS sub_logs_tenant_id
        FROM mes_instance_manager manager
                 LEFT JOIN mes_instance_manager_log logs ON manager.manager_id = logs.manager_id
            and logs.del_flag = '0'
        WHERE manager.manager_id = #{managerId}
          and manager.del_flag = '0'
    </select>

    <select id="queryPageListWith" resultMap="InstanceManagerResultMap">
        SELECT
        manager.*,
        logs.log_id AS sub_logs_log_id,
        logs.manager_id AS sub_logs_manager_id,
        logs.operator_id AS sub_logs_operator_id,
        logs.operator_name AS sub_logs_operator_name,
        logs.operator_process AS sub_logs_operator_process,
        logs.operator_status AS sub_logs_operator_status,
        logs.operator_time AS sub_logs_operator_time,
        logs.operator_remark AS sub_logs_operator_remark,
        logs.remark AS sub_logs_remark,
        logs.status AS sub_logs_status,
        logs.del_flag AS sub_logs_del_flag,
        logs.create_by AS sub_logs_create_by,
        logs.create_time AS sub_logs_create_time,
        logs.update_by AS sub_logs_update_by,
        logs.update_time AS sub_logs_update_time,
        logs.create_dept AS sub_logs_create_dept,
        logs.tenant_id AS sub_logs_tenant_id
        FROM
        mes_instance_manager manager
        LEFT JOIN mes_instance_manager_log logs ON manager.manager_id = logs.manager_id
        AND logs.del_flag = '0'
        <if test="ew != null and ew.customSqlSegment != null and ew.customSqlSegment != ''">
            ${ew.customSqlSegment}
        </if>
    </select>

    <select id="queryCountList" resultMap="InstanceManagerResultMap">
        SELECT
        TO_CHAR(DATE_TRUNC(${type}, mim.send_time), ${formatter}) AS remark,
        SUM(mim.amount_cost) AS amount_cost,
        SUM(mim.amount_sale) AS amount_sale,
        SUM(mim.amount_received) AS amount_received,
        SUM(mim.amount_unreceived) AS amount_unreceived,
        SUM(mim.amount_commission) AS amount_commission
        FROM
        public.mes_instance_manager mim
        WHERE
        mim.send_time IS NOT NULL
        AND mim.del_flag = '0'
        AND mim.send_time::date &gt;= #{beginSendTime}
        AND mim.send_time::date &lt;= #{endSendTime}
        GROUP BY
        DATE_TRUNC(${type}, mim.send_time)
        ORDER BY
        DATE_TRUNC(${type}, mim.send_time)
    </select>

</mapper>
