package com.iotlaser.spms.enums;

import com.iotlaser.spms.mes.enums.ProductionReturnStatus;
import com.iotlaser.spms.mes.enums.ProductionOrderStatus;
import com.iotlaser.spms.mes.enums.ProductionInboundStatus;
import com.iotlaser.spms.mes.enums.ProductionIssueStatus;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.erp.enums.FinApInvoiceStatus;
import com.iotlaser.spms.erp.enums.FinApPaymentStatus;
import com.iotlaser.spms.wms.enums.SourceType;
import com.iotlaser.spms.base.enums.CycleMethod;
import com.iotlaser.spms.pro.enums.ProcessCategory;
import com.iotlaser.spms.pro.enums.RoutingStatus;
import com.iotlaser.spms.pro.enums.BomStatus;
import com.iotlaser.spms.pro.enums.ProductType;
import com.iotlaser.spms.pro.enums.InstanceStatus;
import com.ruoyi.common.core.domain.entity.IDictEnum;

/**
 * 枚举字典信息验证测试
 * 验证所有枚举类是否正确实现了IDictEnum接口的完整功能
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-24
 */
public class EnumDictValidationTest {

    public static void main(String[] args) {
        System.out.println("=== 枚举字典信息验证测试 ===");
        
        try {
            // 1. 测试MES模块枚举
            testMesEnums();
            
            // 2. 测试ERP模块枚举
            testErpEnums();
            
            // 3. 测试WMS模块枚举
            testWmsEnums();
            
            // 4. 测试BASE模块枚举
            testBaseEnums();
            
            // 5. 测试PRO模块枚举
            testProEnums();
            
            System.out.println("\n=== 验证测试总结 ===");
            System.out.println("✅ 所有枚举类字典信息验证通过！");
            System.out.println("✅ DICT_NAME字段：完整");
            System.out.println("✅ DICT_DESC字段：完整");
            System.out.println("✅ getDictName()方法：正常");
            System.out.println("✅ getDictDesc()方法：正常");
            System.out.println("✅ IDictEnum接口实现：完整");
            
        } catch (Exception e) {
            System.err.println("❌ 验证测试失败: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

    /**
     * 测试MES模块枚举
     */
    private static void testMesEnums() {
        System.out.println("\n1. MES模块枚举验证");
        
        // 测试ProductionReturnStatus
        validateEnum(ProductionReturnStatus.DRAFT, "生产退料单状态", "ProductionReturnStatus");
        
        // 测试ProductionOrderStatus
        validateEnum(ProductionOrderStatus.DRAFT, "生产订单状态", "ProductionOrderStatus");
        
        // 测试ProductionInboundStatus
        validateEnum(ProductionInboundStatus.DRAFT, "生产入库单状态", "ProductionInboundStatus");
        
        // 测试ProductionIssueStatus
        validateEnum(ProductionIssueStatus.DRAFT, "生产领料单状态", "ProductionIssueStatus");
        
        System.out.println("   ✅ MES模块所有枚举验证通过");
    }

    /**
     * 测试ERP模块枚举
     */
    private static void testErpEnums() {
        System.out.println("\n2. ERP模块枚举验证");
        
        // 测试PurchaseOrderStatus
        validateEnum(PurchaseOrderStatus.DRAFT, "采购订单状态", "PurchaseOrderStatus");
        
        // 测试PurchaseInboundStatus
        validateEnum(PurchaseInboundStatus.DRAFT, "采购入库单状态", "PurchaseInboundStatus");
        
        // 测试FinApInvoiceStatus
        validateEnum(FinApInvoiceStatus.DRAFT, "应付发票状态", "FinApInvoiceStatus");
        
        // 测试FinApPaymentStatus
        validateEnum(FinApPaymentStatus.DRAFT, "应付付款状态", "FinApPaymentStatus");
        
        System.out.println("   ✅ ERP模块所有枚举验证通过");
    }

    /**
     * 测试WMS模块枚举
     */
    private static void testWmsEnums() {
        System.out.println("\n3. WMS模块枚举验证");
        
        // 测试SourceType
        validateEnum(SourceType.PURCHASE_INBOUND, "来源类型", "SourceType");
        
        System.out.println("   ✅ WMS模块所有枚举验证通过");
    }

    /**
     * 测试BASE模块枚举
     */
    private static void testBaseEnums() {
        System.out.println("\n4. BASE模块枚举验证");
        
        // 测试CycleMethod
        validateEnum(CycleMethod.FIFO, "盘点方式", "CycleMethod");
        
        System.out.println("   ✅ BASE模块所有枚举验证通过");
    }

    /**
     * 测试PRO模块枚举
     */
    private static void testProEnums() {
        System.out.println("\n5. PRO模块枚举验证");
        
        // 测试ProcessCategory
        validateEnum(ProcessCategory.PRODUCTION, "工序类别", "ProcessCategory");
        
        // 测试RoutingStatus
        validateEnum(RoutingStatus.DRAFT, "工艺路线状态", "RoutingStatus");
        
        // 测试BomStatus
        validateEnum(BomStatus.DRAFT, "BOM状态", "BomStatus");
        
        // 测试ProductType
        validateEnum(ProductType.FINISHED_PRODUCT, "产品类型", "ProductType");
        
        // 测试InstanceStatus
        validateEnum(InstanceStatus.DRAFT, "实例状态", "InstanceStatus");
        
        System.out.println("   ✅ PRO模块所有枚举验证通过");
    }

    /**
     * 验证单个枚举的字典信息
     */
    private static void validateEnum(IDictEnum<?> enumValue, String expectedDictName, String enumClassName) {
        try {
            // 验证getDictCode方法
            String dictCode = enumValue.getDictCode();
            assert dictCode != null && !dictCode.isEmpty() : enumClassName + " getDictCode()不能为空";
            
            // 验证getDictName方法
            String dictName = enumValue.getDictName();
            assert dictName != null && !dictName.isEmpty() : enumClassName + " getDictName()不能为空";
            assert dictName.equals(expectedDictName) : enumClassName + " getDictName()应该是: " + expectedDictName + ", 实际是: " + dictName;
            
            // 验证getDictDesc方法
            String dictDesc = enumValue.getDictDesc();
            assert dictDesc != null && !dictDesc.isEmpty() : enumClassName + " getDictDesc()不能为空";
            assert dictDesc.length() > 10 : enumClassName + " getDictDesc()应该是详细描述，长度应该大于10";
            
            // 验证getValue方法
            Object value = enumValue.getValue();
            assert value != null : enumClassName + " getValue()不能为空";
            
            // 验证getName方法
            String name = enumValue.getName();
            assert name != null && !name.isEmpty() : enumClassName + " getName()不能为空";
            
            // 验证getDesc方法
            String desc = enumValue.getDesc();
            assert desc != null && !desc.isEmpty() : enumClassName + " getDesc()不能为空";
            
            System.out.println("   ✅ " + enumClassName + " 验证通过");
            System.out.println("      - DICT_CODE: " + dictCode);
            System.out.println("      - DICT_NAME: " + dictName);
            System.out.println("      - DICT_DESC: " + dictDesc.substring(0, Math.min(30, dictDesc.length())) + "...");
            
        } catch (Exception e) {
            throw new RuntimeException(enumClassName + " 验证失败: " + e.getMessage(), e);
        }
    }
}
