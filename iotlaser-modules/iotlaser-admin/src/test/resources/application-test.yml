# 测试环境配置
spring:
  profiles:
    active: test

  # 数据源配置 - 使用H2内存数据库
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
    username: sa
    password:

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect

  # Redis配置 - 使用嵌入式Redis
  redis:
    host: localhost
    port: 6379
    database: 1
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 3600000

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 2
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.iotlaser.spms: DEBUG
    org.springframework.transaction: DEBUG
    org.springframework.orm.jpa: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 测试专用配置
test:
  # 测试数据配置
  data:
    cleanup: true # 测试后清理数据
    init-script: classpath:test-data.sql

  # 性能测试配置
  performance:
    timeout: 5000 # 5秒超时
    concurrent-users: 10

  # Mock配置
  mock:
    external-services: true # Mock外部服务

# Sa-Token配置
sa-token:
  token-name: Authorization
  timeout: 2592000
  activity-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false
