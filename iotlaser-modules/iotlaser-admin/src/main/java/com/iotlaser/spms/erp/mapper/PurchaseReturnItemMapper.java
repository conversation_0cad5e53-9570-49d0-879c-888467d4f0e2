package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.PurchaseReturnItem;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 采购退货明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/07
 */
public interface PurchaseReturnItemMapper extends BaseMapperPlus<PurchaseReturnItem, PurchaseReturnItemVo> {

    /**
     * 查询采购退货明细表及其关联信息
     */
    PurchaseReturnItemVo queryByIdWith(@Param("itemId") Long itemId);

    /**
     * 分页查询采购退货明细表及其关联信息
     */
    List<PurchaseReturnItemVo> queryPageListWith(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<PurchaseReturnItem> wrapper);

}
