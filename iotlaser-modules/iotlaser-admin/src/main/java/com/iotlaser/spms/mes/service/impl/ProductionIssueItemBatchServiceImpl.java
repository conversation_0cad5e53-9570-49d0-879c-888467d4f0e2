package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.mes.domain.ProductionIssueItemBatch;
import com.iotlaser.spms.mes.domain.bo.ProductionIssueItemBatchBo;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueItemBatchVo;
import com.iotlaser.spms.mes.mapper.ProductionIssueItemBatchMapper;
import com.iotlaser.spms.mes.service.IProductionIssueItemBatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 生产领料批次明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductionIssueItemBatchServiceImpl implements IProductionIssueItemBatchService {

    private final ProductionIssueItemBatchMapper baseMapper;

    /**
     * 查询生产领料批次明细
     *
     * @param batchId 主键
     * @return 生产领料批次明细
     */
    @Override
    public ProductionIssueItemBatchVo queryById(Long batchId) {
        return baseMapper.selectVoById(batchId);
    }

    /**
     * 分页查询生产领料批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产领料批次明细分页列表
     */
    @Override
    public TableDataInfo<ProductionIssueItemBatchVo> queryPageList(ProductionIssueItemBatchBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductionIssueItemBatch> lqw = buildQueryWrapper(bo);
        Page<ProductionIssueItemBatchVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产领料批次明细列表
     *
     * @param bo 查询条件
     * @return 生产领料批次明细列表
     */
    @Override
    public List<ProductionIssueItemBatchVo> queryList(ProductionIssueItemBatchBo bo) {
        LambdaQueryWrapper<ProductionIssueItemBatch> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductionIssueItemBatch> buildQueryWrapper(ProductionIssueItemBatchBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductionIssueItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductionIssueItemBatch::getBatchId);
        lqw.eq(bo.getItemId() != null, ProductionIssueItemBatch::getItemId, bo.getItemId());
        lqw.eq(bo.getIssueId() != null, ProductionIssueItemBatch::getIssueId, bo.getIssueId());
        lqw.eq(bo.getInventoryBatchId() != null, ProductionIssueItemBatch::getInventoryBatchId, bo.getInventoryBatchId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), ProductionIssueItemBatch::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), ProductionIssueItemBatch::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), ProductionIssueItemBatch::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getProductId() != null, ProductionIssueItemBatch::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductionIssueItemBatch::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), ProductionIssueItemBatch::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, ProductionIssueItemBatch::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), ProductionIssueItemBatch::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), ProductionIssueItemBatch::getUnitName, bo.getUnitName());
        // ✅ 优化：移除数量和价格的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, ProductionIssueItemBatch::getQuantity, bo.getQuantity());
        // 原代码：lqw.eq(bo.getPrice() != null, ProductionIssueItemBatch::getPrice, bo.getPrice());
        // TODO: 如需要可以后续添加数量和价格的范围查询支持
        lqw.eq(bo.getLocationId() != null, ProductionIssueItemBatch::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), ProductionIssueItemBatch::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), ProductionIssueItemBatch::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductionIssueItemBatch::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增生产领料批次明细
     *
     * @param bo 生产领料批次明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProductionIssueItemBatchBo bo) {
        ProductionIssueItemBatch add = MapstructUtils.convert(bo, ProductionIssueItemBatch.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBatchId(add.getBatchId());
        }
        return flag;
    }

    /**
     * 修改生产领料批次明细
     *
     * @param bo 生产领料批次明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProductionIssueItemBatchBo bo) {
        ProductionIssueItemBatch update = MapstructUtils.convert(bo, ProductionIssueItemBatch.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductionIssueItemBatch entity) {
        // 校验必填字段
        if (entity.getItemId() == null) {
            throw new ServiceException("领料明细不能为空");
        }
        if (entity.getInventoryBatchId() == null) {
            throw new ServiceException("库存批次不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("领料数量必须大于0");
        }
        if (StringUtils.isBlank(entity.getInternalBatchNumber())) {
            throw new ServiceException("内部批次号不能为空");
        }
    }

    /**
     * 校验并批量删除生产领料批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验批次明细是否可以删除
            List<ProductionIssueItemBatch> batches = baseMapper.selectByIds(ids);
            for (ProductionIssueItemBatch batch : batches) {
                log.info("删除生产领料批次明细，批次号：{}", batch.getInternalBatchNumber());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
