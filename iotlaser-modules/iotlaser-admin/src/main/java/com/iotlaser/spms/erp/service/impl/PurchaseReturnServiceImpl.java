package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.PurchaseReturn;
import com.iotlaser.spms.erp.domain.bo.PurchaseReturnBo;
import com.iotlaser.spms.erp.domain.bo.PurchaseReturnItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnItemVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnVo;
import com.iotlaser.spms.erp.enums.PurchaseReturnStatus;
import com.iotlaser.spms.erp.mapper.PurchaseReturnMapper;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import com.iotlaser.spms.erp.service.IPurchaseReturnItemService;
import com.iotlaser.spms.erp.service.IPurchaseReturnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_PURCHASE_RETURN_CODE;

/**
 * 采购退货Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseReturnServiceImpl implements IPurchaseReturnService {

    private final PurchaseReturnMapper baseMapper;
    private final IPurchaseReturnItemService itemService;
    private final IPurchaseInboundService purchaseInboundService;
    private final Gen gen;

    /**
     * 查询采购退货
     *
     * @param returnId 主键
     * @return 采购退货
     */
    @Override
    public PurchaseReturnVo queryById(Long returnId) {
        return baseMapper.selectVoById(returnId);
    }

    /**
     * 分页查询采购退货列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购退货分页列表
     */
    @Override
    public TableDataInfo<PurchaseReturnVo> queryPageList(PurchaseReturnBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseReturn> lqw = buildQueryWrapper(bo);
        Page<PurchaseReturnVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购退货列表
     *
     * @param bo 查询条件
     * @return 采购退货列表
     */
    @Override
    public List<PurchaseReturnVo> queryList(PurchaseReturnBo bo) {
        LambdaQueryWrapper<PurchaseReturn> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseReturn> buildQueryWrapper(PurchaseReturnBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseReturn> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseReturn::getReturnId);
        lqw.eq(StringUtils.isNotBlank(bo.getReturnCode()), PurchaseReturn::getReturnCode, bo.getReturnCode());
        lqw.like(StringUtils.isNotBlank(bo.getReturnName()), PurchaseReturn::getReturnName, bo.getReturnName());
        lqw.eq(bo.getOrderId() != null, PurchaseReturn::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderCode()), PurchaseReturn::getOrderCode, bo.getOrderCode());
        lqw.like(StringUtils.isNotBlank(bo.getOrderName()), PurchaseReturn::getOrderName, bo.getOrderName());
        lqw.eq(bo.getInspectionId() != null, PurchaseReturn::getInspectionId, bo.getInspectionId());
        lqw.eq(StringUtils.isNotBlank(bo.getInspectionCode()), PurchaseReturn::getInspectionCode, bo.getInspectionCode());
        lqw.like(StringUtils.isNotBlank(bo.getInspectionName()), PurchaseReturn::getInspectionName, bo.getInspectionName());
        lqw.eq(bo.getSupplierId() != null, PurchaseReturn::getSupplierId, bo.getSupplierId());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierCode()), PurchaseReturn::getSupplierCode, bo.getSupplierCode());
        lqw.like(StringUtils.isNotBlank(bo.getSupplierName()), PurchaseReturn::getSupplierName, bo.getSupplierName());
        lqw.eq(bo.getReturnDate() != null, PurchaseReturn::getReturnDate, bo.getReturnDate());
        lqw.eq(bo.getReturnStatus() != null, PurchaseReturn::getReturnStatus, bo.getReturnStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseReturn::getStatus, bo.getStatus());
        lqw.between(params.get("beginReturnDate") != null && params.get("endReturnDate") != null,
            PurchaseReturn::getReturnDate, params.get("beginReturnDate"), params.get("endReturnDate"));
        return lqw;
    }

    /**
     * 新增采购退货
     *
     * @param bo 采购退货
     * @return 创建的采购退货
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(PurchaseReturnBo bo) {
        if (StringUtils.isEmpty(bo.getReturnCode())) {
            bo.setReturnCode(gen.code(ERP_PURCHASE_RETURN_CODE));
        }
        bo.setReturnStatus(PurchaseReturnStatus.DRAFT);
        PurchaseReturn add = MapstructUtils.convert(bo, PurchaseReturn.class);
        validEntityBeforeSave(add);
        // 插入数据库
        boolean flag = baseMapper.insert(add) > 0;
        if (flag && add.getItems() != null && !add.getItems().isEmpty()) {
            // 插入订单项
            List<PurchaseReturnItemBo> itemBos = new ArrayList<>();
            add.getItems().forEach(item -> {
                item.setReturnId(add.getReturnId());
                PurchaseReturnItemBo itemBo = MapstructUtils.convert(item, PurchaseReturnItemBo.class);
                itemBos.add(itemBo);
            });
            itemService.insertOrUpdateBatch(itemBos);
        }
        return flag;
    }

    /**
     * 修改采购退货
     *
     * @param bo 采购退货
     * @return 修改后的采购退货
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(PurchaseReturnBo bo) {
        // 将采购退货Bo转换为实体类
        PurchaseReturn update = MapstructUtils.convert(bo, PurchaseReturn.class);
        // 保存前验证实体
        validEntityBeforeSave(update);
        // 更新采购退货记录，判断更新是否成功
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag && update.getItems() != null && !update.getItems().isEmpty()) {
            // 获取当前采购退货项列表
            List<PurchaseReturnItemVo> currentItems = itemService.queryByReturnId(bo.getReturnId());
            List<Long> itemIds = currentItems.stream()
                .map(PurchaseReturnItemVo::getItemId)
                .collect(Collectors.toList());

            // 准备新的退货项
            List<PurchaseReturnItemBo> itemBos = new ArrayList<>();
            update.getItems().forEach(item -> {
                item.setReturnId(bo.getReturnId());
                PurchaseReturnItemBo itemBo = MapstructUtils.convert(item, PurchaseReturnItemBo.class);
                itemBos.add(itemBo);
                itemIds.remove(item.getItemId());
            });

            itemService.insertOrUpdateBatch(itemBos);
            if (!itemIds.isEmpty()) {
                itemService.deleteWithValidByIds(itemIds, false);
            }
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PurchaseReturn entity) {
        // 校验退货单编号唯一性
        if (StringUtils.isNotBlank(entity.getReturnCode())) {
            LambdaQueryWrapper<PurchaseReturn> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PurchaseReturn::getReturnCode, entity.getReturnCode());
            if (entity.getReturnId() != null) {
                wrapper.ne(PurchaseReturn::getReturnId, entity.getReturnId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("退货单编号已存在：" + entity.getReturnCode());
            }
        }
    }

    /**
     * 校验并批量删除采购退货信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验退货单状态，只有草稿状态的退货单才能删除
            List<PurchaseReturn> returns = baseMapper.selectByIds(ids);
            for (PurchaseReturn purchaseReturn : returns) {
                if (purchaseReturn.getReturnStatus() != PurchaseReturnStatus.DRAFT) {
                    throw new ServiceException("退货单【" + purchaseReturn.getReturnCode() + "】状态为【" +
                        purchaseReturn.getReturnStatus() + "】，不允许删除");
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 确认采购退货单
     *
     * @param returnId 退货单ID
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmReturn(Long returnId) {
        PurchaseReturn purchaseReturn = baseMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new ServiceException("退货单不存在");
        }

        // 校验状态
        if (purchaseReturn.getReturnStatus() != PurchaseReturnStatus.DRAFT) {
            throw new ServiceException("退货单【" + purchaseReturn.getReturnCode() + "】状态为【" +
                purchaseReturn.getReturnStatus() + "】，不允许确认");
        }

        // 更新状态为待出库
        purchaseReturn.setReturnStatus(PurchaseReturnStatus.PENDING_WAREHOUSE);
        boolean result = baseMapper.updateById(purchaseReturn) > 0;

        if (result) {
            log.info("采购退货单【{}】确认成功", purchaseReturn.getReturnCode());
        }

        return result;
    }

    /**
     * 批量确认采购退货单
     *
     * @param returnIds 退货单ID集合
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchConfirmReturns(Collection<Long> returnIds) {
        for (Long returnId : returnIds) {
            confirmReturn(returnId);
        }
        return true;
    }

    /**
     * 完成采购退货出库
     *
     * @param returnId 退货单ID
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeReturn(Long returnId) {
        PurchaseReturn purchaseReturn = baseMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new ServiceException("退货单不存在");
        }

        // 校验状态
        if (purchaseReturn.getReturnStatus() != PurchaseReturnStatus.PENDING_WAREHOUSE) {
            throw new ServiceException("退货单【" + purchaseReturn.getReturnCode() + "】状态为【" +
                purchaseReturn.getReturnStatus() + "】，不允许完成出库");
        }

        // 更新状态为已出库
        purchaseReturn.setReturnStatus(PurchaseReturnStatus.COMPLETED);
        boolean result = baseMapper.updateById(purchaseReturn) > 0;

        if (result) {
            log.info("采购退货单【{}】出库完成", purchaseReturn.getReturnCode());
            // 处理库存减少逻辑
            processInventoryDecrease(purchaseReturn);
        }

        return result;
    }

    /**
     * 取消采购退货单
     *
     * @param returnId 退货单ID
     * @param reason   取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelReturn(Long returnId, String reason) {
        PurchaseReturn purchaseReturn = baseMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new ServiceException("退货单不存在");
        }

        // 校验状态，只有草稿和待出库状态可以取消
        if (purchaseReturn.getReturnStatus() != PurchaseReturnStatus.DRAFT &&
            purchaseReturn.getReturnStatus() != PurchaseReturnStatus.PENDING_WAREHOUSE) {
            throw new ServiceException("退货单【" + purchaseReturn.getReturnCode() + "】状态为【" +
                purchaseReturn.getReturnStatus() + "】，不允许取消");
        }

        // 更新状态为草稿，并记录取消原因
        purchaseReturn.setReturnStatus(PurchaseReturnStatus.DRAFT);
        if (StringUtils.isNotBlank(reason)) {
            purchaseReturn.setRemark(StringUtils.isBlank(purchaseReturn.getRemark()) ?
                "取消原因：" + reason : purchaseReturn.getRemark() + "；取消原因：" + reason);
        }
        boolean result = baseMapper.updateById(purchaseReturn) > 0;

        if (result) {
            log.info("采购退货单【{}】取消成功，原因：{}", purchaseReturn.getReturnCode(), reason);
        }

        return result;
    }

    /**
     * 根据采购入库单创建退货单
     *
     * @param purchaseInboundId 采购入库单ID
     * @return 创建的退货单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseReturnVo createFromPurchaseInbound(Long purchaseInboundId) {
        // 获取采购入库单信息
        PurchaseInboundVo inbound = purchaseInboundService.queryById(purchaseInboundId);
        if (inbound == null) {
            throw new ServiceException("采购入库单不存在");
        }

        // 创建退货单
        PurchaseReturn purchaseReturn = new PurchaseReturn();
        purchaseReturn.setReturnCode(gen.code(ERP_PURCHASE_RETURN_CODE));
        purchaseReturn.setReturnName("基于入库单【" + inbound.getInboundCode() + "】的退货单");
        purchaseReturn.setOrderId(inbound.getOrderId());
        purchaseReturn.setOrderCode(inbound.getOrderCode());
        purchaseReturn.setOrderName(inbound.getOrderName());
        purchaseReturn.setInspectionId(inbound.getInspectionId());
        purchaseReturn.setInspectionCode(inbound.getInspectionCode());
        purchaseReturn.setInspectionName(inbound.getInspectionName());
        purchaseReturn.setSupplierId(inbound.getSupplierId());
        purchaseReturn.setSupplierCode(inbound.getSupplierCode());
        purchaseReturn.setSupplierName(inbound.getSupplierName());
        purchaseReturn.setReturnDate(LocalDate.now());
        purchaseReturn.setReturnStatus(PurchaseReturnStatus.DRAFT);
        purchaseReturn.setRemark("基于采购入库单【" + inbound.getInboundCode() + "】创建");

        // 插入退货单
        boolean flag = baseMapper.insert(purchaseReturn) > 0;
        if (!flag) {
            throw new ServiceException("创建退货单失败");
        }

        // 创建退货明细
        if (inbound.getItems() != null && !inbound.getItems().isEmpty()) {
            List<PurchaseReturnItemBo> returnItemBos = inbound.getItems().stream().map(inboundItem -> {
                PurchaseReturnItemBo returnItemBo = new PurchaseReturnItemBo();
                returnItemBo.setReturnId(purchaseReturn.getReturnId());
                returnItemBo.setProductId(inboundItem.getProductId());
                returnItemBo.setProductCode(inboundItem.getProductCode());
                returnItemBo.setProductName(inboundItem.getProductName());
                returnItemBo.setUnitId(inboundItem.getUnitId());
                returnItemBo.setUnitCode(inboundItem.getUnitCode());
                returnItemBo.setUnitName(inboundItem.getUnitName());
                returnItemBo.setQuantity(inboundItem.getQuantity());
                returnItemBo.setPrice(inboundItem.getPrice());
                returnItemBo.setLocationId(inboundItem.getLocationId());
                returnItemBo.setLocationCode(inboundItem.getLocationCode());
                returnItemBo.setLocationName(inboundItem.getLocationName());
                returnItemBo.setRemark("基于入库明细创建");
                return returnItemBo;
            }).collect(Collectors.toList());

            itemService.insertOrUpdateBatch(returnItemBos);
        }

        log.info("基于采购入库单【{}】创建退货单【{}】成功", inbound.getInboundCode(), purchaseReturn.getReturnCode());

        return queryById(purchaseReturn.getReturnId());
    }

    /**
     * 处理库存减少逻辑
     *
     * @param purchaseReturn 采购退货单
     */
    private void processInventoryDecrease(PurchaseReturn purchaseReturn) {
        try {
            // 获取退货明细
            // 注意：这里需要根据实际的退货明细Service进行调用
            // List<PurchaseReturnItem> items = itemService.getByReturnId(purchaseReturn.getReturnId());

            // 由于当前可能没有退货明细Service，这里记录日志
            log.info("采购退货库存减少：退货单【{}】", purchaseReturn.getReturnCode());

            // TODO: 实际项目中需要：
            // 1. 获取采购退货明细
            // 2. 遍历明细，调用库存服务减少库存
            // 3. 记录库存变动日志
            //
            // for (PurchaseReturnItem item : items) {
            //     inventoryService.decreaseInventory(item.getProductId(), item.getLocationId(), item.getQuantity());
            //     log.info("采购退货库存减少：产品【{}】数量【{}】", item.getProductName(), item.getQuantity());
            // }

            log.info("采购退货单【{}】库存减少处理完成", purchaseReturn.getReturnCode());
        } catch (Exception e) {
            log.error("采购退货单【{}】库存减少失败：{}", purchaseReturn.getReturnCode(), e.getMessage(), e);
            throw new ServiceException("库存减少失败：" + e.getMessage());
        }
    }
}
