package com.iotlaser.spms.mes.domain.bo;

import com.iotlaser.spms.mes.domain.ProductionInbound;
import com.iotlaser.spms.mes.enums.ProductionInboundStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 生产入库业务对象 mes_production_inbound
 *
 * <AUTHOR>
 * @date 2025/05/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductionInbound.class, reverseConvertGenerate = false)
public class ProductionInboundBo extends BaseEntity {

    /**
     * 入库单ID
     */
    @NotNull(message = "入库单ID不能为空", groups = {EditGroup.class})
    private Long inboundId;

    /**
     * 入库单编号
     */
    @NotBlank(message = "入库单编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String inboundCode;

    /**
     * 入库单名称
     */
    private String inboundName;

    /**
     * 生产订单ID
     */
    private Long orderId;

    /**
     * 生产订单编码
     */
    private String orderCode;

    /**
     * 生产订单名称
     */
    private String orderName;

    /**
     * 检验单ID
     */
    private Long inspectionId;

    /**
     * 检验单编号
     */
    private String inspectionCode;

    /**
     * 检验单名称
     */
    private String inspectionName;

    /**
     * 入库时间
     */
    private Date inboundTime;

    /**
     * 入库状态
     */
    private ProductionInboundStatus inboundStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
