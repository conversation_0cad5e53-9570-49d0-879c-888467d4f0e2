package com.iotlaser.spms.base.strategy;

import com.iotlaser.spms.base.domain.bo.AutoCodePartBo;
import com.iotlaser.spms.base.domain.bo.AutoCodeResultBo;
import com.iotlaser.spms.base.domain.vo.AutoCodePartVo;
import com.iotlaser.spms.base.domain.vo.AutoCodeResultVo;
import com.iotlaser.spms.base.domain.vo.AutoCodeRuleVo;
import com.iotlaser.spms.base.enums.GenCodeType;
import com.iotlaser.spms.base.enums.PartType;
import com.iotlaser.spms.base.service.IAutoCodePartService;
import com.iotlaser.spms.base.service.IAutoCodeResultService;
import com.iotlaser.spms.base.service.IAutoCodeRuleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.enums.FormatsType;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 生成业务编号
 *
 * <AUTHOR>
 * @date 2025/5/14
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class Gen {

    public static final ThreadLocal<Boolean> IS_NEW_SERIAL_CYCLE_FLAG = new ThreadLocal<>();

    private final IAutoCodeRuleService ruleService;
    private final IAutoCodePartService partService;
    private final IAutoCodeResultService resultService;
    private final PartTypeHandler handler;

    @Log(title = "生成业务编号", businessType = BusinessType.INSERT)
    @Transactional(rollbackFor = Exception.class)
    public synchronized String code(GenCodeType genCodeType) {
        return generateCodeInternal(genCodeType.getValue(), null);
    }

    @Log(title = "生成业务编号", businessType = BusinessType.INSERT)
    @Transactional(rollbackFor = Exception.class)
    public synchronized String code(GenCodeType genCodeType, String inputCharacter) {
        return generateCodeInternal(genCodeType.getValue(), inputCharacter);
    }

    @Log(title = "生成业务编号", businessType = BusinessType.INSERT)
    @Transactional(rollbackFor = Exception.class)
    public synchronized String code(String ruleCode) {
        return generateCodeInternal(ruleCode, null);
    }

    @Log(title = "生成业务编号", businessType = BusinessType.INSERT)
    @Transactional(rollbackFor = Exception.class)
    public synchronized String code(String ruleCode, String inputCharacter) {
        return generateCodeInternal(ruleCode, inputCharacter);
    }

    /**
     * 内部生成编码的方法
     *
     * @param ruleCode       编码规则代码
     * @param inputCharacter 输入字符
     * @return 生成的编码字符串
     * @throws ServiceException 如果生成规则不存在、已禁用、未配置组成部分、流水号部分生成为空值或生成的编码为空，则抛出此异常
     */
    private String generateCodeInternal(String ruleCode, String inputCharacter) {
        try {
            // 用于存储流水号部分的引用
            final AtomicReference<String> lastSerialNoRef = new AtomicReference<>();
            // 根据规则代码查询自动编码规则信息
            AutoCodeRuleVo ruleVo = ruleService.selectVoOneByRuleCode(ruleCode);
            // 如果规则不存在，抛出异常
            if (Objects.isNull(ruleVo)) {
                throw new ServiceException("规则：未获取到'" + ruleCode + "'的生成规则");
            }
            // 如果规则已禁用，抛出异常
            if (!"0".equals(ruleVo.getStatus())) {
                throw new ServiceException("规则：'" + ruleCode + "'已禁用");
            }
            // 创建部分参数对象，并设置规则ID
            AutoCodePartBo partParam = new AutoCodePartBo();
            partParam.setRuleId(ruleVo.getRuleId());
            // 设置排序参数，并查询编码组成部分列表
            List<AutoCodePartVo> parts = partService.queryList(partParam);
            // 如果没有配置任何编码组成部分，抛出异常
            if (parts.isEmpty()) {
                throw new ServiceException("规则：'" + ruleCode + "'未配置任何编码组成部分");
            }
            // 计算流水号部分的数量
            long serialNoPartCount = parts.stream()
                .filter(part -> PartType.PART_TYPE_SERIALNO == part.getPartType())
                .count();
            // 如果流水号部分的数量超过1，抛出异常
            if (serialNoPartCount > 1) {
                throw new ServiceException("规则：'" + ruleCode + "'流水号方式的组成只能存在一个！");
            }
            // 构建最终的编码字符串
            StringBuilder buff = new StringBuilder();
            for (AutoCodePartVo codePart : parts) {
                codePart.setInputCharacter(inputCharacter);
                // 执行编码生成逻辑
                String partStr = handler.choiceExecute(codePart);
                // 如果是流水号部分，进行特殊处理
                if (PartType.PART_TYPE_SERIALNO == codePart.getPartType()) {
                    if (StringUtils.isBlank(partStr)) {
                        throw new ServiceException("规则：'" + ruleCode + "'的流水号部分生成为空值");
                    }
                    lastSerialNoRef.set(partStr);
                }
                buff.append(partStr);
            }
            // 如果生成的编码为空，抛出异常
            if (StringUtils.isEmpty(buff.toString())) {
                throw new ServiceException("规则：'" + ruleCode + "'生成的编码为空！");
            }
            // 对生成的编码进行填充
            String autoCode = paddingStr(ruleVo, buff);
            // 保存自动编码生成结果
            saveAutoCodeResult(ruleVo, autoCode, inputCharacter, lastSerialNoRef.get());
            // 返回生成的编码
            return autoCode;
        } finally {
            // 移除新流水周期的标志
            IS_NEW_SERIAL_CYCLE_FLAG.remove();
        }
    }

    /**
     * 根据规则对字符串进行补齐操作
     *
     * @param rule 规则对象，包含是否补齐、最大长度、补齐字符和补齐方式等信息
     * @param sb   待补齐的字符串构建器
     * @return 返回补齐后的字符串
     * @throws ServiceException 如果字符串长度超过最大长度限制，则抛出服务异常
     */
    private String paddingStr(AutoCodeRuleVo rule, StringBuilder sb) {
        // 检查是否需要进行补齐操作
        String isPadding = rule.getIsPadded();
        if ("Y".equals(isPadding)) {
            // 获取规则定义的最大长度和补齐字符
            int maxLength = rule.getMaxLength();
            String paddingChar = rule.getPaddedChar();
            int currentLength = sb.length();

            // 如果当前字符串长度超过最大长度，抛出异常
            if (currentLength > maxLength) {
                throw new ServiceException("规则：'" + rule.getRuleCode() + "'的编码长度 (" + currentLength + ") 在补齐前已超出最大长度 (" + maxLength + ")");
            }

            // 计算需要补齐的字符数量
            int charsToPad = maxLength - currentLength;
            if (charsToPad > 0) {
                StringBuilder padding = new StringBuilder(charsToPad);
                for (int i = 0; i < charsToPad; i++) {
                    padding.append(paddingChar);
                }

                // 根据规则中的补齐方式（左补齐或右补齐）来添加补齐字符
                if ("L".equals(rule.getPaddedMethod())) {
                    return padding.append(sb).toString();
                } else {
                    return sb.append(padding).toString();
                }
            }
        }
        // 如果不需要补齐，直接返回原始字符串
        return sb.toString();
    }

    /**
     * 保存自动编码的结果
     * <p>
     * 此方法负责将生成的自动编码结果保存到数据库中，以便后续可以追踪和重用这些编码规则和结果
     * 它处理的情况包括：检查流水号是否为空或无效，更新编码结果，以及管理编码索引的循环
     *
     * @param rule                 自动编码规则的视图对象，包含了规则的相关信息
     * @param autoCode             生成的自动编码字符串
     * @param inputChar            用户输入的字符，用于编码生成
     * @param lastSerialNoValueStr 最后生成的流水号部分的字符串表示
     */
    private void saveAutoCodeResult(AutoCodeRuleVo rule, String autoCode, String inputChar, String lastSerialNoValueStr) {
        // 检查流水号是否为空，如果为空，则记录日志并返回，不进行保存操作
        if (lastSerialNoValueStr == null) {
            log.debug("规则 '{}' 不包含流水号部分或流水号为空，不保存 AutoCodeResult。", rule.getRuleCode());
            return;
        }

        // 如果流水号为空字符串，抛出异常，因为无法保存无效的流水号结果
        if (StringUtils.isBlank(lastSerialNoValueStr)) {
            throw new ServiceException("流水号部分生成了空值，无法保存结果。规则: " + rule.getRuleCode());
        }

        // 将流水号字符串转换为长整型，如果转换失败，抛出异常
        long lastSerialNo;
        try {
            lastSerialNo = Long.parseLong(lastSerialNoValueStr);
        } catch (NumberFormatException e) {
            throw new ServiceException("流水号部分返回的值 '" + lastSerialNoValueStr + "' 不是有效的数字。规则: " + rule.getRuleCode());
        }

        // 创建一个新的 AutoCodeResultBo 对象，用于保存到数据库
        AutoCodeResultBo newResultBo = new AutoCodeResultBo();
        newResultBo.setRuleId(rule.getRuleId());
        newResultBo.setGenDate(DateUtils.parseDateToStr(FormatsType.YYYYMMDDHHMMSS, DateUtils.getNowDate()));
        newResultBo.setLastResult(autoCode);
        newResultBo.setLastSerialNo(lastSerialNo);
        newResultBo.setLastInputChar(inputChar);

        // 根据 IS_NEW_SERIAL_CYCLE_FLAG 标志判断是否是新的流水号周期
        Boolean isNewCycle = IS_NEW_SERIAL_CYCLE_FLAG.get();

        // 如果是新的周期，设置生成索引为1，否则查询数据库中最新的结果并递增索引
        if (isNewCycle != null && isNewCycle) {
            newResultBo.setGenIndex(1L);
        } else {
            AutoCodeResultBo queryLastForRuleBo = new AutoCodeResultBo();
            queryLastForRuleBo.setRuleId(rule.getRuleId());

            AutoCodeResultVo previousResultForRule = resultService.findLastResult(queryLastForRuleBo);

            // 如果找到了之前的编码结果，递增索引，否则设置为1
            if (previousResultForRule != null) {
                newResultBo.setGenIndex(previousResultForRule.getGenIndex() + 1);
            } else {
                newResultBo.setGenIndex(1L);
            }
        }

        // 调用服务层方法，将新的编码结果保存到数据库
        resultService.insertByBo(newResultBo);
    }

}

