package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 银行流水类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinBankFlowType implements IDictEnum<String> {

    INCOME("income", "收入", "银行账户收入"),
    EXPENSE("expense", "支出", "银行账户支出"),
    TRANSFER_IN("transfer_in", "转入", "从其他账户转入"),
    TRANSFER_OUT("transfer_out", "转出", "转出到其他账户"),
    INTEREST("interest", "利息", "银行利息收入"),
    FEE("fee", "手续费", "银行手续费支出"),
    REFUND("refund", "退款", "退款收入"),
    ADJUSTMENT("adjustment", "调整", "银行调整"),
    OTHER("other", "其他", "其他类型流水");

    public final static String DICT_CODE = "erp_fin_bank_flow_type";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 银行流水类型枚举
     */
    public static FinBankFlowType getByValue(String value) {
        for (FinBankFlowType flowType : values()) {
            if (flowType.getValue().equals(value)) {
                return flowType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    /**
     * 判断是否为收入类型
     *
     * @return 是否为收入类型
     */
    public boolean isIncome() {
        return this == INCOME || this == TRANSFER_IN || this == INTEREST || this == REFUND;
    }

    /**
     * 判断是否为支出类型
     *
     * @return 是否为支出类型
     */
    public boolean isExpense() {
        return this == EXPENSE || this == TRANSFER_OUT || this == FEE;
    }

    /**
     * 判断是否为转账类型
     *
     * @return 是否为转账类型
     */
    public boolean isTransfer() {
        return this == TRANSFER_IN || this == TRANSFER_OUT;
    }

    /**
     * 判断是否需要匹配业务单据
     *
     * @return 是否需要匹配业务单据
     */
    public boolean requiresBusinessMatch() {
        return this == INCOME || this == EXPENSE || this == REFUND;
    }

    /**
     * 获取金额方向（1为正，-1为负）
     *
     * @return 金额方向
     */
    public int getAmountDirection() {
        if (isIncome()) {
            return 1; // 正数，增加余额
        } else if (isExpense()) {
            return -1; // 负数，减少余额
        } else {
            return 0; // 中性，如调整
        }
    }

    /**
     * 获取对应的业务类型
     *
     * @return 对应的业务类型
     */
    public String getBusinessType() {
        switch (this) {
            case INCOME:
                return "RECEIPT"; // 收款
            case EXPENSE:
                return "PAYMENT"; // 付款
            case TRANSFER_IN:
            case TRANSFER_OUT:
                return "TRANSFER"; // 转账
            case INTEREST:
                return "INTEREST"; // 利息
            case FEE:
                return "FEE"; // 手续费
            case REFUND:
                return "REFUND"; // 退款
            case ADJUSTMENT:
                return "ADJUSTMENT"; // 调整
            case OTHER:
            default:
                return "OTHER"; // 其他
        }
    }
}
