package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.PurchaseInboundItemBatch;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundItemBatchBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemBatchVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 采购入库批次明细Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
public interface IPurchaseInboundItemBatchService {

    /**
     * 查询采购入库批次明细
     *
     * @param batchId 主键
     * @return 采购入库批次明细
     */
    PurchaseInboundItemBatchVo queryById(Long batchId);

    /**
     * 分页查询采购入库批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购入库批次明细分页列表
     */
    TableDataInfo<PurchaseInboundItemBatchVo> queryPageList(PurchaseInboundItemBatchBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的采购入库批次明细列表
     *
     * @param bo 查询条件
     * @return 采购入库批次明细列表
     */
    List<PurchaseInboundItemBatchVo> queryList(PurchaseInboundItemBatchBo bo);

    /**
     * 新增采购入库批次明细
     *
     * @param bo 采购入库批次明细
     * @return 是否新增成功
     */
    Boolean insertByBo(PurchaseInboundItemBatchBo bo);

    /**
     * 修改采购入库批次明细
     *
     * @param bo 采购入库批次明细
     * @return 是否修改成功
     */
    Boolean updateByBo(PurchaseInboundItemBatchBo bo);

    /**
     * 校验并批量删除采购入库批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量插入采购入库批次明细
     * ✅ 修正：使用BO而非直接暴露Entity
     *
     * @param batches 批次BO列表
     * @return 是否插入成功
     */
    Boolean insertOrUpdateBatch(List<PurchaseInboundItemBatchBo> batches);

    /**
     * 根据明细ID查询批次列表
     *
     * @param itemId 明细ID
     * @return 批次列表
     */
    List<PurchaseInboundItemBatch> queryByItemId(Long itemId);
}
