package com.iotlaser.spms.base.service;

import com.iotlaser.spms.base.domain.bo.LocationBo;
import com.iotlaser.spms.base.domain.vo.LocationVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 位置库位Service接口
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
public interface ILocationService {

    /**
     * 查询位置库位
     *
     * @param locationId 主键
     * @return 位置库位
     */
    LocationVo queryById(Long locationId);

    /**
     * 查询符合条件的位置库位列表
     *
     * @param bo 查询条件
     * @return 位置库位列表
     */
    List<LocationVo> queryList(LocationBo bo);

    /**
     * 新增位置库位
     *
     * @param bo 位置库位
     * @return 是否新增成功
     */
    Boolean insertByBo(LocationBo bo);

    /**
     * 修改位置库位
     *
     * @param bo 位置库位
     * @return 是否修改成功
     */
    Boolean updateByBo(LocationBo bo);

    /**
     * 校验并批量删除位置库位信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取位置树结构
     *
     * @param warehouseCode 仓库编码（可选）
     * @return 位置树列表
     */
    List<LocationVo> getLocationTree(String warehouseCode);

    /**
     * 获取子位置列表
     *
     * @param parentId 父位置ID
     * @return 子位置列表
     */
    List<LocationVo> getSubLocations(Long parentId);

    /**
     * 更新位置状态
     *
     * @param locationId 位置ID
     * @param status     状态（1-启用，0-禁用）
     * @return 是否更新成功
     */
    Boolean updateLocationStatus(Long locationId, String status);

    /**
     * 获取可用位置列表
     *
     * @param warehouseCode    仓库编码
     * @param requiredCapacity 所需容量（可选）
     * @return 可用位置列表
     */
    List<LocationVo> getAvailableLocations(String warehouseCode, BigDecimal requiredCapacity);

    /**
     * 根据位置类型获取位置列表
     *
     * @param locationType 位置类型
     * @return 位置列表
     */
    List<LocationVo> getLocationsByType(String locationType);

    /**
     * 根据位置编码查询位置信息
     *
     * @param locationCode 位置编码
     * @return 位置信息
     */
    LocationVo getByLocationCode(String locationCode);

    /**
     * 设置位置维护状态
     *
     * @param locationId 位置ID
     * @param reason     维护原因
     * @return 是否设置成功
     */
    Boolean setLocationMaintenance(Long locationId, String reason);
}
