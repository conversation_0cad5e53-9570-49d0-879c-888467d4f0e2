package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 材料预警信息VO
 *
 * <AUTHOR> Assistant
 * @date 2025-06-24
 */
@Data
@ExcelIgnoreUnannotated
public class MaterialWarningVo {

    /**
     * 原材料ID
     */
    private Long materialId;

    /**
     * 原材料编码
     */
    @ExcelProperty(value = "原材料编码")
    private String materialCode;

    /**
     * 原材料名称
     */
    @ExcelProperty(value = "原材料名称")
    private String materialName;

    /**
     * 当前库存量
     */
    @ExcelProperty(value = "当前库存量")
    private BigDecimal currentStock;

    /**
     * 安全库存量
     */
    @ExcelProperty(value = "安全库存量")
    private BigDecimal safetyStock;

    /**
     * 预警阈值
     */
    @ExcelProperty(value = "预警阈值")
    private BigDecimal warningThreshold;

    /**
     * 预警类型：LOW_STOCK(库存不足)、EXPIRY(即将过期)、SLOW_MOVING(滞销)
     */
    @ExcelProperty(value = "预警类型")
    private String warningType;

    /**
     * 预警等级：HIGH(高)、MEDIUM(中)、LOW(低)
     */
    @ExcelProperty(value = "预警等级")
    private String warningLevel;

    /**
     * 预警时间
     */
    @ExcelProperty(value = "预警时间")
    private LocalDateTime warningTime;

    /**
     * 预警原因
     */
    private String warningReason;

    /**
     * 建议措施
     */
    private String suggestedAction;

    /**
     * 备注
     */
    private String remark;
}
