package com.iotlaser.spms.base.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.base.domain.bo.AutoCodePartBo;
import com.iotlaser.spms.base.domain.vo.AutoCodePartVo;
import com.iotlaser.spms.base.service.IAutoCodePartService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 编码生成规则组成
 *
 * <AUTHOR> Kai
 * @date 2025/03/11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/base/autoCodePart")
public class AutoCodePartController extends BaseController {

    private final IAutoCodePartService sysAutoCodePartService;

    /**
     * 查询编码生成规则组成列表
     */
    @SaCheckPermission("base:autoCodePart:list")
    @GetMapping("/list")
    public TableDataInfo<AutoCodePartVo> list(AutoCodePartBo bo, PageQuery pageQuery) {
        return sysAutoCodePartService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出编码生成规则组成列表
     */
    @SaCheckPermission("base:autoCodePart:export")
    @Log(title = "编码生成规则组成", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AutoCodePartBo bo, HttpServletResponse response) {
        List<AutoCodePartVo> list = sysAutoCodePartService.queryList(bo);
        ExcelUtil.exportExcel(list, "编码生成规则组成", AutoCodePartVo.class, response);
    }

    /**
     * 获取编码生成规则组成详细信息
     *
     * @param partId 主键
     */
    @SaCheckPermission("base:autoCodePart:query")
    @GetMapping("/{partId}")
    public R<AutoCodePartVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long partId) {
        return R.ok(sysAutoCodePartService.queryById(partId));
    }

    /**
     * 新增编码生成规则组成
     */
    @SaCheckPermission("base:autoCodePart:add")
    @Log(title = "编码生成规则组成", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AutoCodePartBo bo) {
        return toAjax(sysAutoCodePartService.insertByBo(bo));
    }

    /**
     * 修改编码生成规则组成
     */
    @SaCheckPermission("base:autoCodePart:edit")
    @Log(title = "编码生成规则组成", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AutoCodePartBo bo) {
        return toAjax(sysAutoCodePartService.updateByBo(bo));
    }

    /**
     * 删除编码生成规则组成
     *
     * @param partIds 主键串
     */
    @SaCheckPermission("base:autoCodePart:remove")
    @Log(title = "编码生成规则组成", businessType = BusinessType.DELETE)
    @DeleteMapping("/{partIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] partIds) {
        return toAjax(sysAutoCodePartService.deleteWithValidByIds(List.of(partIds), true));
    }
}
