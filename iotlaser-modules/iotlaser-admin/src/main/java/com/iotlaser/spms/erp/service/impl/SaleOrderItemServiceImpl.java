package com.iotlaser.spms.erp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.SaleOrderItem;
import com.iotlaser.spms.erp.domain.bo.SaleOrderItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderItemVo;
import com.iotlaser.spms.erp.mapper.SaleOrderItemMapper;
import com.iotlaser.spms.erp.service.ISaleOrderItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 销售订单明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleOrderItemServiceImpl implements ISaleOrderItemService {

    private final SaleOrderItemMapper baseMapper;

    /**
     * 查询销售订单明细
     *
     * @param itemId 主键
     * @return 销售订单明细
     */
    @Override
    public SaleOrderItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询销售订单明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售订单明细分页列表
     */
    @Override
    public TableDataInfo<SaleOrderItemVo> queryPageList(SaleOrderItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleOrderItem> lqw = buildQueryWrapper(bo);
        Page<SaleOrderItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售订单明细列表
     *
     * @param bo 查询条件
     * @return 销售订单明细列表
     */
    @Override
    public List<SaleOrderItemVo> queryList(SaleOrderItemBo bo) {
        LambdaQueryWrapper<SaleOrderItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleOrderItem> buildQueryWrapper(SaleOrderItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleOrderItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleOrderItem::getItemId);
        lqw.eq(bo.getOrderId() != null, SaleOrderItem::getOrderId, bo.getOrderId());
        lqw.eq(bo.getProductId() != null, SaleOrderItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), SaleOrderItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), SaleOrderItem::getProductName, bo.getProductName());
        // ✅ 优化：移除数量和价格的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, SaleOrderItem::getQuantity, bo.getQuantity());
        // 原代码：lqw.eq(bo.getPrice() != null, SaleOrderItem::getPrice, bo.getPrice());
        // TODO: 如需要可以后续添加数量和价格的范围查询支持
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleOrderItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增销售订单明细
     *
     * @param bo 销售订单明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SaleOrderItemBo bo) {
        // 计算价税分离字段
        calculateTaxFields(bo);

        SaleOrderItem add = MapstructUtils.convert(bo, SaleOrderItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改销售订单明细
     *
     * @param bo 销售订单明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SaleOrderItemBo bo) {
        // 重新计算价税分离字段
        calculateTaxFields(bo);

        SaleOrderItem update = MapstructUtils.convert(bo, SaleOrderItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleOrderItem entity) {
        // 校验必填字段
        if (entity.getOrderId() == null) {
            throw new ServiceException("销售订单不能为空");
        }
        if (entity.getProductId() == null) {
            throw new ServiceException("产品不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("销售数量必须大于0");
        }
        if (entity.getPrice() == null || entity.getPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("销售价格不能为负数");
        }

        // 校验同一订单中产品不能重复
        if (entity.getOrderId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<SaleOrderItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleOrderItem::getOrderId, entity.getOrderId());
            wrapper.eq(SaleOrderItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(SaleOrderItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一销售订单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除销售订单明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验明细是否可以删除
            List<SaleOrderItem> items = baseMapper.selectByIds(ids);
            for (SaleOrderItem item : items) {
                // 检查关联的销售订单状态
                // 检查关联的销售订单状态
                log.info("删除销售订单明细，产品：{}", item.getProductName());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 批量新增销售订单明细
     *
     * @param items 销售订单明细列表
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<SaleOrderItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }
        List<SaleOrderItem> entities = MapstructUtils.convert(items, SaleOrderItem.class);
        return baseMapper.insertBatch(entities);
    }

    /**
     * 批量新增或更新销售订单明细
     *
     * @param items 销售订单明细列表
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<SaleOrderItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }
        List<SaleOrderItem> entities = MapstructUtils.convert(items, SaleOrderItem.class);
        return baseMapper.insertOrUpdateBatch(entities);
    }

    /**
     * 根据订单ID查询明细列表
     *
     * @param orderId 订单ID
     * @return 明细列表
     */
    @Override
    public List<SaleOrderItemVo> queryByOrderId(Long orderId) {
        LambdaQueryWrapper<SaleOrderItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SaleOrderItem::getOrderId, orderId);
        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 查询销售订单明细表及其关联信息
     *
     * @param itemId 主键
     * @return 销售订单明细表
     */
    @Override
    public SaleOrderItemVo queryByIdWith(Long itemId) {
        return baseMapper.queryByIdWith(itemId);
    }

    /**
     * 分页查询销售订单明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售订单明细表分页列表
     */
    @Override
    public TableDataInfo<SaleOrderItemVo> queryPageListWith(SaleOrderItemBo bo, PageQuery pageQuery) {
        QueryWrapper<SaleOrderItem> queryWrapper = buildQueryWrapperWith(bo);
        List<SaleOrderItemVo> result = baseMapper.queryPageListWith(pageQuery.build(), queryWrapper);
        return TableDataInfo.build(result);
    }

    /**
     * 构建查询条件包装器（用于关联查询）
     *
     * @param bo 查询条件
     * @return 查询条件包装器
     */
    private QueryWrapper<SaleOrderItem> buildQueryWrapperWith(SaleOrderItemBo bo) {
        Map<String, Object> params = BeanUtil.beanToMap(bo, false, true);
        QueryWrapper<SaleOrderItem> wrapper = Wrappers.query();
        wrapper.eq(bo.getOrderId() != null, "item.order_id", bo.getOrderId())
            .eq(bo.getProductId() != null, "item.product_id", bo.getProductId())
            .like(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode())
            .like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName())
            .eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }

    /**
     * 计算价税分离字段
     *
     * @param bo 业务对象
     */
    private void calculateTaxFields(SaleOrderItemBo bo) {
        // 校验必要字段
        if (bo.getQuantity() == null || bo.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("数量必须大于0");
        }

        if (bo.getTaxRate() == null) {
            bo.setTaxRate(BigDecimal.ZERO); // 默认税率为0
        }

        // 校验税率范围 (0-100%)
        if (bo.getTaxRate().compareTo(BigDecimal.ZERO) < 0 ||
            bo.getTaxRate().compareTo(new BigDecimal("100")) > 0) {
            throw new ServiceException("税率必须在0-100%之间");
        }

        // 根据输入的字段计算其他字段
        if (bo.getPriceExclusiveTax() != null) {
            // 基于单价（不含税）计算
            calculateFromPriceExclusiveTax(bo);
        } else if (bo.getPrice() != null) {
            // 基于单价（含税）计算
            calculateFromPrice(bo);
        } else if (bo.getAmountExclusiveTax() != null) {
            // 基于金额（不含税）计算
            calculateFromAmountExclusiveTax(bo);
        } else if (bo.getAmount() != null) {
            // 基于金额（含税）计算
            calculateFromTotalAmount(bo);
        } else {
            throw new ServiceException("必须提供单价或金额信息");
        }
    }

    /**
     * 基于单价（不含税）计算其他字段
     */
    private void calculateFromPriceExclusiveTax(SaleOrderItemBo bo) {
        BigDecimal priceExclusiveTax = bo.getPriceExclusiveTax();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算金额（不含税） = 单价（不含税） × 数量
        BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(quantity)
            .setScale(2, RoundingMode.HALF_UP);
        bo.setAmountExclusiveTax(amountExclusiveTax);

        // 计算税额 = 金额（不含税） × 税率
        BigDecimal taxAmount = amountExclusiveTax.multiply(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP))
            .setScale(2, RoundingMode.HALF_UP);
        bo.setTaxAmount(taxAmount);

        // 计算单价（含税） = 单价（不含税） × (1 + 税率)
        BigDecimal price = priceExclusiveTax.multiply(BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)))
            .setScale(2, RoundingMode.HALF_UP);
        bo.setPrice(price);

        // 计算金额（含税） = 金额（不含税） + 税额
        BigDecimal amount = amountExclusiveTax.add(taxAmount);
        bo.setAmount(amount);
    }

    /**
     * 基于单价（含税）计算其他字段
     */
    private void calculateFromPrice(SaleOrderItemBo bo) {
        BigDecimal price = bo.getPrice();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算金额（含税） = 单价（含税） × 数量
        BigDecimal amount = price.multiply(quantity)
            .setScale(2, RoundingMode.HALF_UP);
        bo.setAmount(amount);

        // 计算单价（不含税） = 单价（含税） ÷ (1 + 税率)
        BigDecimal priceExclusiveTax = price.divide(BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)), 2, RoundingMode.HALF_UP);
        bo.setPriceExclusiveTax(priceExclusiveTax);

        // 计算金额（不含税） = 单价（不含税） × 数量
        BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(quantity)
            .setScale(2, RoundingMode.HALF_UP);
        bo.setAmountExclusiveTax(amountExclusiveTax);

        // 计算税额 = 金额（含税） - 金额（不含税）
        BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        bo.setTaxAmount(taxAmount);
    }

    /**
     * 基于金额（不含税）计算其他字段
     */
    private void calculateFromAmountExclusiveTax(SaleOrderItemBo bo) {
        BigDecimal amountExclusiveTax = bo.getAmountExclusiveTax();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算单价（不含税） = 金额（不含税） ÷ 数量
        BigDecimal priceExclusiveTax = amountExclusiveTax.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPriceExclusiveTax(priceExclusiveTax);

        // 计算税额 = 金额（不含税） × 税率
        BigDecimal taxAmount = amountExclusiveTax.multiply(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP))
            .setScale(2, RoundingMode.HALF_UP);
        bo.setTaxAmount(taxAmount);

        // 计算金额（含税） = 金额（不含税） + 税额
        BigDecimal amount = amountExclusiveTax.add(taxAmount);
        bo.setAmount(amount);

        // 计算单价（含税） = 金额（含税） ÷ 数量
        BigDecimal price = amount.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPrice(price);
    }

    /**
     * 基于金额（含税）计算其他字段
     */
    private void calculateFromTotalAmount(SaleOrderItemBo bo) {
        BigDecimal amount = bo.getAmount();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算单价（含税） = 金额（含税） ÷ 数量
        BigDecimal price = amount.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPrice(price);

        // 计算金额（不含税） = 金额（含税） ÷ (1 + 税率)
        BigDecimal amountExclusiveTax = amount.divide(BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)), 2, RoundingMode.HALF_UP);
        bo.setAmountExclusiveTax(amountExclusiveTax);

        // 计算单价（不含税） = 金额（不含税） ÷ 数量
        BigDecimal priceExclusiveTax = amountExclusiveTax.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPriceExclusiveTax(priceExclusiveTax);

        // 计算税额 = 金额（含税） - 金额（不含税）
        BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        bo.setTaxAmount(taxAmount);
    }
}
