package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.SaleReturnItem;
import com.iotlaser.spms.erp.domain.vo.SaleReturnItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 销售退货明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/08
 */
public interface SaleReturnItemMapper extends BaseMapperPlus<SaleReturnItem, SaleReturnItemVo> {

    /**
     * 查询销售退货明细表及其关联信息
     */
    SaleReturnItemVo queryByIdWith(@Param("itemId") Long itemId);

    /**
     * 分页查询销售退货明细表及其关联信息
     */
    List<SaleReturnItemVo> queryPageListWith(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<SaleReturnItem> wrapper);

}
