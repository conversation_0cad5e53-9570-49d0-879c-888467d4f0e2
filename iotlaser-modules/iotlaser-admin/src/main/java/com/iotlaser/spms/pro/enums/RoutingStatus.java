package com.iotlaser.spms.pro.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工艺路线状态枚举
 * 用于管理工艺路线的生命周期状态，从编制到生效的完整流程
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-15
 */
@Getter
@AllArgsConstructor
public enum RoutingStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "编制中"),
    PENDING_REVIEW("pending_review", "待审核", "等待审核"),
    APPROVED("approved", "已审核", "审核通过"),
    ACTIVE("active", "生效", "可用于生产"),
    INACTIVE("inactive", "失效", "不可用于生产"),
    ARCHIVED("archived", "归档", "已归档");

    public final static String DICT_CODE = "pro_routing_status";
    public final static String DICT_NAME = "工艺路线状态";
    public final static String DICT_DESC = "管理工艺路线的生命周期状态，从编制、审核到生效的完整流程状态";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 工艺路线状态枚举
     */
    public static RoutingStatus getByValue(String value) {
        for (RoutingStatus routingStatus : values()) {
            if (routingStatus.getValue().equals(value)) {
                return routingStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
