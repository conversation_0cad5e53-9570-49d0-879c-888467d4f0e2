package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinArReceiptReceivableLink;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收款单与应收单核销关系业务对象 erp_fin_ar_receipt_receivable_link
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinArReceiptReceivableLink.class, reverseConvertGenerate = false)
public class FinArReceiptReceivableLinkBo extends BaseEntity {

    /**
     * 关系ID
     */
    @NotNull(message = "关系ID不能为空", groups = {EditGroup.class})
    private Long linkId;

    /**
     * 收款ID
     */
    @NotNull(message = "收款ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long receiptId;

    /**
     * 应收ID
     */
    @NotNull(message = "应收ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long receivableId;

    /**
     * 核销金额
     */
    private BigDecimal appliedAmount;

    /**
     * 核销日期
     */
    private LocalDate cancellationDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
