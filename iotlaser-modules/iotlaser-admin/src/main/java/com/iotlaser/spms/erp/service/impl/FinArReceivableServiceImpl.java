package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.FinArReceivable;
import com.iotlaser.spms.erp.domain.FinArReceivableItem;
import com.iotlaser.spms.erp.domain.bo.FinArReceivableBo;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundItemBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundItemVo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import com.iotlaser.spms.erp.enums.FinArReceivableStatus;
import com.iotlaser.spms.erp.mapper.FinArReceivableMapper;
import com.iotlaser.spms.erp.service.*;
import com.iotlaser.spms.erp.utils.AmountCalculationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_AR_RECEIVABLE_CODE;

/**
 * 应收单Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinArReceivableServiceImpl implements IFinArReceivableService {

    private final FinArReceivableMapper baseMapper;
    private final IFinArReceivableItemService finArReceivableItemService;
    private final ISaleOutboundItemService saleOutboundItemService;
    private final ISaleOrderItemService saleOrderItemService;
    private final ICompanyService companyService;
    private final Gen gen;
    @Lazy
    @Autowired
    private ISaleOutboundService saleOutboundService;
    // 添加缺失的依赖注入
    @Lazy
    @Autowired
    private IFinArReceiptReceivableLinkService finArReceiptReceivableLinkService;

    /**
     * 查询应收单
     *
     * @param receivableId 主键
     * @return 应收单
     */
    @Override
    public FinArReceivableVo queryById(Long receivableId) {
        return baseMapper.selectVoById(receivableId);
    }

    /**
     * 分页查询应收单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应收单分页列表
     */
    @Override
    public TableDataInfo<FinArReceivableVo> queryPageList(FinArReceivableBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinArReceivable> lqw = buildQueryWrapper(bo);
        Page<FinArReceivableVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的应收单列表
     *
     * @param bo 查询条件
     * @return 应收单列表
     */
    @Override
    public List<FinArReceivableVo> queryList(FinArReceivableBo bo) {
        LambdaQueryWrapper<FinArReceivable> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinArReceivable> buildQueryWrapper(FinArReceivableBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinArReceivable> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinArReceivable::getReceivableId);
        lqw.eq(StringUtils.isNotBlank(bo.getReceivableCode()), FinArReceivable::getReceivableCode, bo.getReceivableCode());
        lqw.like(StringUtils.isNotBlank(bo.getReceivableName()), FinArReceivable::getReceivableName, bo.getReceivableName());
        lqw.eq(bo.getCustomerId() != null, FinArReceivable::getCustomerId, bo.getCustomerId());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerCode()), FinArReceivable::getCustomerCode, bo.getCustomerCode());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), FinArReceivable::getCustomerName, bo.getCustomerName());
        lqw.eq(bo.getDirectSourceId() != null, FinArReceivable::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), FinArReceivable::getDirectSourceCode, bo.getDirectSourceCode());
        lqw.like(StringUtils.isNotBlank(bo.getDirectSourceName()), FinArReceivable::getDirectSourceName, bo.getDirectSourceName());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceType()), FinArReceivable::getDirectSourceType, bo.getDirectSourceType());
        lqw.eq(bo.getSourceId() != null, FinArReceivable::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), FinArReceivable::getSourceCode, bo.getSourceCode());
        lqw.like(StringUtils.isNotBlank(bo.getSourceName()), FinArReceivable::getSourceName, bo.getSourceName());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceType()), FinArReceivable::getSourceType, bo.getSourceType());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceNumber()), FinArReceivable::getInvoiceNumber, bo.getInvoiceNumber());
        lqw.eq(bo.getInvoiceDate() != null, FinArReceivable::getInvoiceDate, bo.getInvoiceDate());
        lqw.eq(bo.getAmountExclusiveTax() != null, FinArReceivable::getAmountExclusiveTax, bo.getAmountExclusiveTax());
        lqw.eq(bo.getTaxAmount() != null, FinArReceivable::getTaxAmount, bo.getTaxAmount());
        lqw.eq(bo.getAmount() != null, FinArReceivable::getAmount, bo.getAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getReceivableStatus()), FinArReceivable::getReceivableStatus, bo.getReceivableStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinArReceivable::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增应收单
     *
     * @param bo 应收单
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(FinArReceivableBo bo) {
        try {
            // 1. 生成应收编码
            if (StringUtils.isEmpty(bo.getReceivableCode())) {
                bo.setReceivableCode(gen.code(ERP_AR_RECEIVABLE_CODE));
            }

            // 2. 设置初始状态
            if (bo.getReceivableStatus() == null) {
                bo.setReceivableStatus(FinArReceivableStatus.PENDING.getValue());
            }

            // 3. 填充冗余字段
            fillRedundantFields(bo);

            // 4. 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 5. 计算金额（价税分离）
            calculateAmounts(bo);

            // 6. 转换为实体并校验
            FinArReceivable add = MapstructUtils.convert(bo, FinArReceivable.class);
            validEntityBeforeSave(add);

            // 7. 插入数据库
            boolean flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setReceivableId(add.getReceivableId());
                log.info("新增应收单成功：{}", add.getReceivableCode());
            }
            return flag;
        } catch (Exception e) {
            log.error("新增应收单失败：{}", e.getMessage(), e);
            throw new ServiceException("新增应收单失败：" + e.getMessage());
        }
    }

    /**
     * 修改应收单
     *
     * @param bo 应收单
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(FinArReceivableBo bo) {
        try {
            // 1. 填充冗余字段
            fillRedundantFields(bo);

            // 2. 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 3. 计算金额（价税分离）
            calculateAmounts(bo);

            // 4. 转换为实体并校验
            FinArReceivable update = MapstructUtils.convert(bo, FinArReceivable.class);
            validEntityBeforeSave(update);

            // 5. 更新数据库
            boolean result = baseMapper.updateById(update) > 0;
            if (result) {
                log.info("修改应收单成功：{}", update.getReceivableCode());
            }
            return result;
        } catch (Exception e) {
            log.error("修改应收单失败：{}", e.getMessage(), e);
            throw new ServiceException("修改应收单失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinArReceivable entity) {
        // 校验应收编号唯一性
        if (StringUtils.isNotBlank(entity.getReceivableCode())) {
            LambdaQueryWrapper<FinArReceivable> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinArReceivable::getReceivableCode, entity.getReceivableCode());
            if (entity.getReceivableId() != null) {
                wrapper.ne(FinArReceivable::getReceivableId, entity.getReceivableId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("应收编号已存在：" + entity.getReceivableCode());
            }
        }

        // 校验状态流转合法性
        if (entity.getReceivableId() != null) {
            FinArReceivable existing = baseMapper.selectById(entity.getReceivableId());
            // TODO: 修复状态转换验证逻辑
            // if (existing != null && !isValidStatusTransition(existing.getReceivableStatus(), FinArReceivableStatus.getByValue(entity.getReceivableStatus()))) {
            //     throw new ServiceException("状态流转不合法");
            // }
        }

        // 校验金额合理性
        if (entity.getAmount() != null && entity.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("应收总金额必须大于0");
        }

        // 校验必填字段
        if (StringUtils.isBlank(entity.getReceivableName())) {
            throw new ServiceException("应收单名称不能为空");
        }
        if (entity.getCustomerId() == null) {
            throw new ServiceException("客户不能为空");
        }

        // 校验客户状态
        if (entity.getCustomerId() != null) {
            CompanyVo customer = companyService.queryById(entity.getCustomerId());
            if (customer == null) {
                throw new ServiceException("客户不存在");
            }
        }
    }

    /**
     * 校验并批量删除应收单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验应收单是否可以删除
            List<FinArReceivable> receivables = baseMapper.selectByIds(ids);
            for (FinArReceivable receivable : receivables) {
                // 1. 检查应收单状态，只有草稿状态的应收单才能删除
                if (!"PENDING".equals(receivable.getReceivableStatus())) {
                    throw new ServiceException("应收单【" + receivable.getReceivableName() + "】状态为【" +
                        receivable.getReceivableStatus() + "】，不允许删除");
                }

                // 2. 检查是否有关联的收款核销记录
                // TODO: 实现existsByReceivableId方法
                // if (finArReceiptReceivableLinkService.existsByReceivableId(receivable.getReceivableId())) {
                //     throw new ServiceException("应收单【" + receivable.getReceivableName() + "】存在收款核销记录，不允许删除");
                // }

                // 3. 检查是否有关联的应收单明细
                // TODO: 实现existsByReceivableId和getItemIdsByReceivableId方法
                // if (finArReceivableItemService.existsByReceivableId(receivable.getReceivableId())) {
                //     // 级联删除应收单明细
                //     List<Long> itemIds = finArReceivableItemService.getItemIdsByReceivableId(receivable.getReceivableId());
                //     if (!itemIds.isEmpty()) {
                //         finArReceivableItemService.deleteWithValidByIds(itemIds, false);
                //         log.info("级联删除应收单明细，应收单：{}，明细数量：{}", receivable.getReceivableName(), itemIds.size());
                //     }
                // }

                log.info("删除应收单校验通过：{}", receivable.getReceivableName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除应收单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除应收单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除应收单失败：" + e.getMessage());
        }
    }

    /**
     * 从销售订单生成应收单
     *
     * @param saleOrderId        销售订单ID
     * @param saleOrderCode      销售订单编号
     * @param customerId         客户ID
     * @param customerCode       客户编码
     * @param customerName       客户名称
     * @param amount             应收总金额
     * @param amountExclusiveTax 金额（不含税）
     * @param taxAmount          税额
     * @param dueDate            到期日期
     * @return 是否生成成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateFromSaleOrder(Long saleOrderId, String saleOrderCode,
                                         Long customerId, String customerCode, String customerName,
                                         BigDecimal amount, BigDecimal amountExclusiveTax,
                                         BigDecimal taxAmount, LocalDate dueDate) {
        try {
            FinArReceivable receivable = new FinArReceivable();

            // 生成应收编号
            receivable.setReceivableCode(generateReceivableCode());
            receivable.setReceivableName("销售应收-" + saleOrderCode);

            // 客户信息
            receivable.setCustomerId(customerId);
            receivable.setCustomerCode(customerCode);
            receivable.setCustomerName(customerName);

            // 来源信息
            receivable.setDirectSourceId(saleOrderId);
            receivable.setDirectSourceCode(saleOrderCode);
            receivable.setDirectSourceName("销售订单-" + saleOrderCode);
            receivable.setDirectSourceType("SALE_ORDER");
            receivable.setSourceId(saleOrderId);
            receivable.setSourceCode(saleOrderCode);
            receivable.setSourceName("销售订单-" + saleOrderCode);
            receivable.setSourceType("SALE_ORDER");

            // 金额信息
            receivable.setAmount(amount);
            receivable.setAmountExclusiveTax(amountExclusiveTax);
            receivable.setTaxAmount(taxAmount);

            // 日期信息
            receivable.setInvoiceDate(LocalDate.now());

            // 状态信息
            receivable.setReceivableStatus("PENDING");

            boolean result = baseMapper.insert(receivable) > 0;

            if (result) {
                log.info("应收单生成成功 - 来源: {}, 客户: {}, 金额: {}",
                    saleOrderCode, customerName, amount);
            }

            return result;
        } catch (Exception e) {
            log.error("应收单生成失败 - 来源: {}, 错误: {}", saleOrderCode, e.getMessage(), e);
            throw new ServiceException("应收单生成失败：" + e.getMessage());
        }
    }

    /**
     * 确认收款
     *
     * @param receivableId   应收单ID
     * @param receivedAmount 实收金额
     * @param confirmById    确认人ID
     * @param confirmByName  确认人姓名
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmReceived(Long receivableId, BigDecimal receivedAmount,
                                   Long confirmById, String confirmByName) {
        try {
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在");
            }

            if (!"PENDING".equals(receivable.getReceivableStatus())) {
                throw new ServiceException("应收单状态不允许确认收款");
            }

            if (!AmountCalculationUtils.isAmountEqual(receivedAmount, receivable.getAmount())) {
                throw new ServiceException("实收金额与应收金额不符");
            }

            // 更新应收单状态
            receivable.setReceivableStatus("RECEIVED");
            // TODO: 需要新增confirmById字段用于记录确认人ID
            // receivable.setConfirmById(confirmById);
            // TODO: 需要新增confirmByName字段用于记录确认人姓名
            // receivable.setConfirmByName(confirmByName);
            // TODO: 需要新增confirmTime字段用于记录确认时间
            // receivable.setConfirmTime(LocalDateTime.now());

            log.info("收款确认：确认人【{}】确认时间【{}】", confirmByName, LocalDateTime.now());

            boolean result = baseMapper.updateById(receivable) > 0;

            if (result) {
                log.info("收款确认成功 - 应收单: {}, 金额: {}, 确认人: {}",
                    receivable.getReceivableCode(), receivedAmount, confirmByName);
            }

            return result;
        } catch (Exception e) {
            log.error("收款确认失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            throw new ServiceException("收款确认失败：" + e.getMessage());
        }
    }

    /**
     * 更新应收单状态
     *
     * @param receivableId 应收单ID
     * @param newStatus    新状态
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateReceivableStatus(Long receivableId, String newStatus) {
        try {
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在");
            }

            receivable.setReceivableStatus(newStatus);
            boolean result = baseMapper.updateById(receivable) > 0;

            if (result) {
                log.info("应收单状态更新成功 - 应收单: {}, 新状态: {}",
                    receivable.getReceivableCode(), newStatus);
            }

            return result;
        } catch (Exception e) {
            log.error("应收单状态更新失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            throw new ServiceException("应收单状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 从销售订单自动生成应收单
     *
     * @param saleOrderId   销售订单ID
     * @param saleOrderCode 销售订单编号
     * @param customerId    客户ID
     * @param customerCode  客户编码
     * @param customerName  客户名称
     * @param amount        应收总金额
     * @param dueDate       到期日期
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 应收单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long generateFromSaleOrder(Long saleOrderId, String saleOrderCode,
                                      Long customerId, String customerCode, String customerName,
                                      BigDecimal amount, LocalDate dueDate,
                                      Long operatorId, String operatorName) {
        try {
            // 检查是否已经生成过应收单
            LambdaQueryWrapper<FinArReceivable> checkWrapper = Wrappers.lambdaQuery();
            checkWrapper.eq(FinArReceivable::getSourceId, saleOrderId);
            checkWrapper.eq(FinArReceivable::getSourceType, "SALE_ORDER");

            if (baseMapper.exists(checkWrapper)) {
                throw new ServiceException("销售订单已生成应收单，不能重复生成");
            }

            FinArReceivable receivable = new FinArReceivable();

            // 生成应收单编号和名称
            receivable.setReceivableCode(generateReceivableCode());
            receivable.setReceivableName("销售应收-" + customerName + "-" + saleOrderCode);

            // 客户信息
            receivable.setCustomerId(customerId);
            receivable.setCustomerCode(customerCode);
            receivable.setCustomerName(customerName);

            // 来源信息
            receivable.setSourceId(saleOrderId);
            receivable.setSourceType("SALE_ORDER");
            receivable.setSourceCode(saleOrderCode);

            // 金额信息
            receivable.setAmountExclusiveTax(amount);
            receivable.setTaxAmount(BigDecimal.ZERO);
            receivable.setAmount(amount);

            // 日期信息
            // TODO: 需要新增receivableDate字段用于记录应收日期
            // receivable.setReceivableDate(LocalDate.now());
            // TODO: 需要新增dueDate字段用于记录到期日期
            // receivable.setDueDate(dueDate);

            log.info("应收日期【{}】到期日期【{}】", LocalDate.now(), dueDate);

            // 状态信息
            receivable.setReceivableStatus("PENDING");

            // 操作人信息
            // TODO: 需要新增applicantId字段用于记录申请人ID
            // receivable.setApplicantId(operatorId);
            // TODO: 需要新增applicantName字段用于记录申请人姓名
            // receivable.setApplicantName(operatorName);

            log.info("操作人信息：申请人【{}】", operatorName);

            boolean result = baseMapper.insert(receivable) > 0;

            if (result) {
                // TODO: 创建应收发票明细记录
                // 当前缺少应收发票明细表 erp_fin_ar_receivable_item
                // 需要实现以下功能：
                // 1. 创建 FinArReceivableItem 实体类
                // 2. 创建 IFinArReceivableItemService 服务接口
                // 3. 从销售订单明细创建应收发票明细
                createReceivableItemsFromSaleOrder(receivable.getReceivableId(), saleOrderId);

                log.info("从销售订单自动生成应收单成功 - 销售订单: {}, 应收单: {}, 金额: {}",
                    saleOrderCode, receivable.getReceivableCode(), amount);
                return receivable.getReceivableId();
            } else {
                throw new ServiceException("应收单生成失败");
            }

        } catch (Exception e) {
            log.error("从销售订单生成应收单失败 - 销售订单: {}, 错误: {}", saleOrderCode, e.getMessage(), e);
            throw new ServiceException("从销售订单生成应收单失败：" + e.getMessage());
        }
    }

    /**
     * 批量生成应收单
     *
     * @param saleOrderInfos 销售订单信息列表
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 生成的应收单ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchGenerateFromSaleOrders(List<SaleOrderInfo> saleOrderInfos,
                                                  Long operatorId, String operatorName) {
        try {
            List<Long> receivableIds = new ArrayList<>();

            for (SaleOrderInfo orderInfo : saleOrderInfos) {
                Long receivableId = generateFromSaleOrder(
                    orderInfo.getSaleOrderId(),
                    orderInfo.getSaleOrderCode(),
                    orderInfo.getCustomerId(),
                    orderInfo.getCustomerCode(),
                    orderInfo.getCustomerName(),
                    orderInfo.getAmount(),
                    orderInfo.getDueDate(),
                    operatorId,
                    operatorName
                );
                receivableIds.add(receivableId);
            }

            log.info("批量生成应收单成功 - 数量: {}, 操作人: {}", receivableIds.size(), operatorName);

            return receivableIds;
        } catch (Exception e) {
            log.error("批量生成应收单失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量生成应收单失败：" + e.getMessage());
        }
    }

    /**
     * 应收逾期预警
     *
     * @param warningDays 预警天数
     * @return 即将逾期的应收单列表
     */
    public List<FinArReceivableVo> getOverdueWarning(Integer warningDays) {
        try {
            LocalDate warningDate = LocalDate.now().plusDays(warningDays);

            LambdaQueryWrapper<FinArReceivable> wrapper = Wrappers.lambdaQuery();
            wrapper.in(FinArReceivable::getReceivableStatus, "PENDING", "PARTIALLY_PAID");
            wrapper.isNotNull(FinArReceivable::getInvoiceDate);
            wrapper.orderByAsc(FinArReceivable::getInvoiceDate);

            List<FinArReceivableVo> allReceivables = baseMapper.selectVoList(wrapper);

            // 过滤出即将逾期的记录
            List<FinArReceivableVo> warningList = allReceivables.stream()
                .filter(r -> {
                    LocalDate tempDueDate = r.getInvoiceDate().plusDays(30);
                    return tempDueDate.isBefore(warningDate) && tempDueDate.isAfter(LocalDate.now());
                })
                .collect(Collectors.toList());

            log.info("应收逾期预警查询完成 - 预警天数: {}, 预警数量: {}", warningDays, warningList.size());
            return warningList;
        } catch (Exception e) {
            log.error("应收逾期预警查询失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("应收逾期预警查询失败：" + e.getMessage());
        }
    }

    /**
     * 生成应收编号
     */
    private String generateReceivableCode() {
        // 简单的编号生成逻辑，实际项目中可以使用更复杂的规则
        return "AR" + System.currentTimeMillis();
    }


    /**
     * 确认应收单
     *
     * @param receivableId  应收单ID
     * @param confirmById   确认人ID
     * @param confirmByName 确认人姓名
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmReceivable(Long receivableId, Long confirmById, String confirmByName) {
        try {
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在");
            }

            if (!"PENDING".equals(receivable.getReceivableStatus())) {
                throw new ServiceException("应收单状态不允许确认");
            }

            receivable.setReceivableStatus("CONFIRMED");
            boolean result = baseMapper.updateById(receivable) > 0;

            if (result) {
                log.info("应收单确认成功 - 应收单: {}, 确认人: {}", receivable.getReceivableCode(), confirmByName);
            }

            return result;
        } catch (Exception e) {
            log.error("应收单确认失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            throw new ServiceException("应收单确认失败：" + e.getMessage());
        }
    }

    /**
     * 取消应收单
     *
     * @param receivableId 应收单ID
     * @param cancelById   取消人ID
     * @param cancelByName 取消人姓名
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelReceivable(Long receivableId, Long cancelById, String cancelByName, String cancelReason) {
        try {
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在");
            }

            if (!"PENDING".equals(receivable.getReceivableStatus()) && !"CONFIRMED".equals(receivable.getReceivableStatus())) {
                throw new ServiceException("应收单状态不允许取消");
            }

            receivable.setReceivableStatus("CANCELLED");
            receivable.setRemark(cancelReason);
            boolean result = baseMapper.updateById(receivable) > 0;

            if (result) {
                log.info("应收单取消成功 - 应收单: {}, 取消人: {}, 原因: {}",
                    receivable.getReceivableCode(), cancelByName, cancelReason);
            }

            return result;
        } catch (Exception e) {
            log.error("应收单取消失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            throw new ServiceException("应收单取消失败：" + e.getMessage());
        }
    }

    /**
     * 设置应收单为逾期状态
     *
     * @param receivableId 应收单ID
     * @return 是否设置成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean setOverdueStatus(Long receivableId) {
        try {
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在");
            }

            if (!"PENDING".equals(receivable.getReceivableStatus()) && !"PARTIALLY_PAID".equals(receivable.getReceivableStatus())) {
                return false; // 只有待收款和部分收款状态才能设置为逾期
            }

            // 使用invoiceDate + 30天作为临时到期日期判断
            LocalDate tempDueDate = receivable.getInvoiceDate() != null ?
                receivable.getInvoiceDate().plusDays(30) : LocalDate.now().minusDays(1);

            if (tempDueDate.isBefore(LocalDate.now())) {
                receivable.setReceivableStatus("OVERDUE");
                boolean result = baseMapper.updateById(receivable) > 0;

                if (result) {
                    log.info("应收单设置逾期状态成功 - 应收单: {}, 临时到期日期: {}",
                        receivable.getReceivableCode(), tempDueDate);
                }
                return result;
            }

            log.info("应收单未到期 - 应收单: {}, 临时到期日期: {}",
                receivable.getReceivableCode(), tempDueDate);
            return false;
        } catch (Exception e) {
            log.error("设置应收单逾期状态失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            throw new ServiceException("设置应收单逾期状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量设置逾期状态
     *
     * @return 设置逾期的应收单数量
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer batchSetOverdueStatus() {
        try {
            LambdaQueryWrapper<FinArReceivable> wrapper = Wrappers.lambdaQuery();
            wrapper.in(FinArReceivable::getReceivableStatus, "PENDING", "PARTIALLY_PAID");
            wrapper.isNotNull(FinArReceivable::getInvoiceDate);

            List<FinArReceivable> allReceivables = baseMapper.selectList(wrapper);

            int count = 0;
            LocalDate today = LocalDate.now();

            for (FinArReceivable receivable : allReceivables) {
                // 使用invoiceDate + 30天作为临时到期日期
                LocalDate tempDueDate = receivable.getInvoiceDate().plusDays(30);
                if (tempDueDate.isBefore(today)) {
                    receivable.setReceivableStatus("OVERDUE");
                    if (baseMapper.updateById(receivable) > 0) {
                        count++;
                        log.debug("设置应收单逾期 - 应收单: {}, 临时到期日期: {}",
                            receivable.getReceivableCode(), tempDueDate);
                    }
                }
            }

            log.info("批量设置应收单逾期状态完成 - 处理数量: {}", count);
            return count;
        } catch (Exception e) {
            log.error("批量设置应收单逾期状态失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量设置应收单逾期状态失败：" + e.getMessage());
        }
    }

    /**
     * 从明细汇总到主表
     *
     * @param receivableId 应收单ID
     * @return 是否汇总成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean summarizeFromItems(Long receivableId) {
        try {
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在");
            }

            // 获取应收明细列表
            List<FinArReceivableItem> items = finArReceivableItemService.getItemsByReceivableId(receivableId);

            if (items.isEmpty()) {
                // 如果没有明细，清零主表金额
                receivable.setAmountExclusiveTax(BigDecimal.ZERO);
                receivable.setTaxAmount(BigDecimal.ZERO);
                receivable.setAmount(BigDecimal.ZERO);
            } else {
                // 汇总计算各项金额
                BigDecimal amountExclusiveTax = items.stream()
                    .map(item -> item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal totalTaxAmount = items.stream()
                    .map(item -> item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal amount = items.stream()
                    .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 更新主表金额
                receivable.setAmountExclusiveTax(amountExclusiveTax);
                receivable.setTaxAmount(totalTaxAmount);
                receivable.setAmount(amount);
            }

            boolean result = baseMapper.updateById(receivable) > 0;

            if (result) {
                log.info("应收单明细汇总成功 - 应收单: {}, 金额（不含税）: {}, 税额: {}, 总金额: {}",
                    receivable.getReceivableCode(), receivable.getAmountExclusiveTax(),
                    receivable.getTaxAmount(), receivable.getAmount());
            }

            return result;
        } catch (Exception e) {
            log.error("应收单明细汇总失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            throw new ServiceException("应收单明细汇总失败：" + e.getMessage());
        }
    }

    /**
     * 批量从销售出库单生成应收账款
     *
     * @param outboundIds    出库单ID列表
     * @param receivableType 应收类型
     * @param dueDate        到期日期
     * @param paymentTerms   付款条件
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 批量生成结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchGenerateFromSaleOutbounds(List<Long> outboundIds, String receivableType,
                                                              LocalDate dueDate, String paymentTerms,
                                                              Long operatorId, String operatorName) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Long> successIds = new ArrayList<>();
            List<String> failureMessages = new ArrayList<>();

            for (Long outboundId : outboundIds) {
                try {
                    Long receivableId = generateFromSaleOutbound(outboundId, receivableType, dueDate, paymentTerms, operatorId, operatorName);
                    successIds.add(receivableId);
                } catch (Exception e) {
                    failureMessages.add("出库单ID " + outboundId + ": " + e.getMessage());
                    log.warn("批量生成应收账款失败，出库单ID：{}，错误：{}", outboundId, e.getMessage());
                }
            }

            result.put("successCount", successIds.size());
            result.put("failureCount", failureMessages.size());
            result.put("successIds", successIds);
            result.put("failureMessages", failureMessages);

            log.info("批量从销售出库单生成应收账款完成：成功【{}】个，失败【{}】个", successIds.size(), failureMessages.size());

            return result;
        } catch (Exception e) {
            log.error("批量从销售出库单生成应收账款失败：{}", e.getMessage(), e);
            throw new ServiceException("批量从销售出库单生成应收账款失败：" + e.getMessage());
        }
    }

    /**
     * 从销售出库单生成应收账款
     *
     * @param outboundId     出库单ID
     * @param receivableType 应收类型
     * @param dueDate        到期日期
     * @param paymentTerms   付款条件
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 应收账款ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long generateFromSaleOutbound(Long outboundId, String receivableType,
                                         LocalDate dueDate, String paymentTerms,
                                         Long operatorId, String operatorName) {
        try {
            // 1. 获取出库单信息
            SaleOutboundVo outbound = saleOutboundService.queryById(outboundId);
            if (outbound == null) {
                throw new ServiceException("销售出库单不存在");
            }

            // 校验出库单状态
            if (!"COMPLETED".equals(outbound.getOutboundStatus())) {
                throw new ServiceException("只有已完成的出库单才能生成应收账款");
            }

            // 检查是否已经生成过应收账款
            if (existsByOutboundId(outboundId)) {
                throw new ServiceException("该出库单已生成应收账款，不能重复生成");
            }

            // 2. 获取出库明细
            SaleOutboundItemBo queryBo = new SaleOutboundItemBo();
            queryBo.setOutboundId(outboundId);
            List<SaleOutboundItemVo> outboundItems = saleOutboundItemService.queryList(queryBo);
            if (outboundItems.isEmpty()) {
                throw new ServiceException("出库单没有明细，无法生成应收账款");
            }

            // 3. 创建应收账款主记录
            FinArReceivable receivable = new FinArReceivable();

            // 生成应收账款编号和名称
            receivable.setReceivableCode(generateReceivableCode());
            receivable.setReceivableName("销售应收-" + outbound.getCustomerName() + "-" + outbound.getOutboundCode());
            // TODO: 需要新增receivableType字段用于记录应收类型
            // receivable.setReceivableType(receivableType);
            // TODO: 需要新增receivableDate字段用于记录应收日期
            // receivable.setReceivableDate(LocalDate.now());
            // TODO: 需要新增dueDate字段用于记录到期日期
            // receivable.setDueDate(dueDate);
            // TODO: 需要新增paymentTerms字段用于记录付款条件
            // receivable.setPaymentTerms(paymentTerms);

            log.info("应收信息：类型【{}】应收日期【{}】到期日期【{}】付款条件【{}】",
                receivableType, LocalDate.now(), dueDate, paymentTerms);

            // 从出库单复制客户信息
            receivable.setCustomerId(outbound.getCustomerId());
            receivable.setCustomerCode(outbound.getCustomerCode());
            receivable.setCustomerName(outbound.getCustomerName());

            // 设置来源信息
            receivable.setSourceId(outboundId);
            receivable.setSourceType("SALE_OUTBOUND");
            receivable.setSourceCode(outbound.getOutboundCode());
            receivable.setSourceName(outbound.getOutboundName());

            // 计算应收金额（从出库明细汇总）
            BigDecimal totalAmountExclusiveTax = BigDecimal.ZERO;
            BigDecimal totalTaxAmount = BigDecimal.ZERO;
            BigDecimal totalAmount = BigDecimal.ZERO;

            for (SaleOutboundItemVo item : outboundItems) {
                BigDecimal itemAmount = item.getPrice().multiply(item.getQuantity());
                totalAmount = totalAmount.add(itemAmount);

                // 如果有税率信息，计算不含税金额和税额
                if (item.getTaxRate() != null && item.getTaxRate().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal taxRate = item.getTaxRate().divide(BigDecimal.valueOf(100));
                    BigDecimal itemAmountExclusiveTax = itemAmount.divide(BigDecimal.ONE.add(taxRate), 2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal itemTaxAmount = itemAmount.subtract(itemAmountExclusiveTax);

                    totalAmountExclusiveTax = totalAmountExclusiveTax.add(itemAmountExclusiveTax);
                    totalTaxAmount = totalTaxAmount.add(itemTaxAmount);
                } else {
                    // 没有税率信息，假设为不含税价格
                    totalAmountExclusiveTax = totalAmountExclusiveTax.add(itemAmount);
                }
            }

            // ✅ 使用现有字段
            receivable.setAmountExclusiveTax(totalAmountExclusiveTax);
            receivable.setTaxAmount(totalTaxAmount);
            receivable.setAmount(totalAmount);  // 使用amount字段而不是totalAmount

            // 设置应收状态
            receivable.setReceivableStatus("PENDING");

            // 设置操作人信息
            // TODO: 需要新增handlerId字段用于记录经办人ID
            // receivable.setHandlerId(operatorId);
            // TODO: 需要新增handlerName字段用于记录经办人姓名
            // receivable.setHandlerName(operatorName);
            // TODO: 需要新增applicantId字段用于记录申请人ID
            // receivable.setApplicantId(operatorId);
            // TODO: 需要新增applicantName字段用于记录申请人姓名
            // receivable.setApplicantName(operatorName);

            log.info("操作人信息：经办人【{}】申请人【{}】", operatorName, operatorName);

            // 插入应收账款主记录
            int result = baseMapper.insert(receivable);
            if (result <= 0) {
                throw new ServiceException("应收账款生成失败");
            }

            Long receivableId = receivable.getReceivableId();

            log.info("从销售出库单生成应收账款成功 - 出库单: {}, 应收账款: {}, 金额: {}, 操作人: {}",
                outbound.getOutboundCode(), receivable.getReceivableCode(), totalAmount, operatorName);

            return receivableId;
        } catch (Exception e) {
            log.error("从销售出库单生成应收账款失败 - 出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);
            throw new ServiceException("从销售出库单生成应收账款失败：" + e.getMessage());
        }
    }

    /**
     * 批量从出库单生成应收账款
     *
     * @param outboundIds    出库单ID列表
     * @param receivableType 应收类型
     * @param dueDate        到期日期
     * @param paymentTerms   付款条件
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 批量生成结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchGenerateFromOutbounds(List<Long> outboundIds, String receivableType,
                                                          LocalDate dueDate, String paymentTerms,
                                                          Long operatorId, String operatorName) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (Long outboundId : outboundIds) {
                try {
                    Long receivableId = generateFromSaleOutbound(outboundId, receivableType,
                        dueDate, paymentTerms, operatorId, operatorName);

                    successList.add(Map.of(
                        "outboundId", outboundId,
                        "receivableId", receivableId,
                        "status", "SUCCESS"
                    ));
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "outboundId", outboundId,
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", outboundIds.size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", operatorId);
            result.put("operatorName", operatorName);
            result.put("operationTime", LocalDateTime.now());

            log.info("批量从出库单生成应收账款完成 - 总数: {}, 成功: {}, 失败: {}, 操作人: {}",
                outboundIds.size(), successList.size(), failureList.size(), operatorName);

            return result;
        } catch (Exception e) {
            log.error("批量从出库单生成应收账款失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量从出库单生成应收账款失败：" + e.getMessage());
        }
    }

    /**
     * 检查出库单是否已生成应收账款
     *
     * @param outboundId 出库单ID
     * @return 是否已生成应收账款
     */
    @Override
    public Boolean existsByOutboundId(Long outboundId) {
        LambdaQueryWrapper<FinArReceivable> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinArReceivable::getSourceId, outboundId);
        wrapper.eq(FinArReceivable::getSourceType, "SALE_OUTBOUND");
        return baseMapper.exists(wrapper);
    }

    /**
     * 检查销售订单是否已生成应收账款
     *
     * @param orderId 销售订单ID
     * @return 是否已生成应收账款
     */
    @Override
    public Boolean existsByOrderId(Long orderId) {
        LambdaQueryWrapper<FinArReceivable> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinArReceivable::getSourceId, orderId);
        wrapper.eq(FinArReceivable::getSourceType, "SALE_ORDER");
        return baseMapper.exists(wrapper);
    }

    /**
     * 根据出库单ID查询应收账款
     *
     * @param outboundId 出库单ID
     * @return 应收账款信息
     */
    @Override
    public FinArReceivableVo queryByOutboundId(Long outboundId) {
        LambdaQueryWrapper<FinArReceivable> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinArReceivable::getSourceId, outboundId);
        wrapper.eq(FinArReceivable::getSourceType, "SALE_OUTBOUND");
        wrapper.last("LIMIT 1");

        FinArReceivable receivable = baseMapper.selectOne(wrapper);
        return receivable != null ? MapstructUtils.convert(receivable, FinArReceivableVo.class) : null;
    }

    /**
     * 获取客户应收账款汇总
     *
     * @param customerId 客户ID
     * @return 应收账款汇总信息
     */
    @Override
    public Map<String, Object> getCustomerReceivableSummary(Long customerId) {
        try {
            Map<String, Object> summary = new HashMap<>();

            // 查询客户所有应收账款
            LambdaQueryWrapper<FinArReceivable> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinArReceivable::getCustomerId, customerId);
            List<FinArReceivable> receivables = baseMapper.selectList(wrapper);

            // 统计各状态的应收账款
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal pendingAmount = BigDecimal.ZERO;
            BigDecimal overdueAmount = BigDecimal.ZERO;
            BigDecimal paidAmount = BigDecimal.ZERO;

            int totalCount = receivables.size();
            int pendingCount = 0;
            int overdueCount = 0;
            int paidCount = 0;

            LocalDate today = LocalDate.now();

            for (FinArReceivable receivable : receivables) {
                // ✅ 使用现有的amount字段而不是totalAmount
                BigDecimal amount = receivable.getAmount() != null ? receivable.getAmount() : BigDecimal.ZERO;
                totalAmount = totalAmount.add(amount);

                String status = receivable.getReceivableStatus();
                switch (status) {
                    case "PENDING":
                        pendingAmount = pendingAmount.add(amount);
                        pendingCount++;
                        // TODO: 检查是否逾期需要dueDate字段
                        // if (receivable.getDueDate() != null && receivable.getDueDate().isBefore(today)) {
                        //     overdueAmount = overdueAmount.add(amount);
                        //     overdueCount++;
                        // }
                        break;
                    case "PARTIALLY_PAID":
                        pendingAmount = pendingAmount.add(amount);
                        pendingCount++;
                        break;
                    case "FULLY_PAID":
                    case "RECEIVED":
                        paidAmount = paidAmount.add(amount);
                        paidCount++;
                        break;
                    case "OVERDUE":
                        overdueAmount = overdueAmount.add(amount);
                        overdueCount++;
                        break;
                }
            }

            summary.put("customerId", customerId);
            summary.put("totalCount", totalCount);
            summary.put("totalAmount", totalAmount);
            summary.put("pendingCount", pendingCount);
            summary.put("pendingAmount", pendingAmount);
            summary.put("overdueCount", overdueCount);
            summary.put("overdueAmount", overdueAmount);
            summary.put("paidCount", paidCount);
            summary.put("paidAmount", paidAmount);
            summary.put("summaryDate", today);

            log.info("客户应收账款汇总完成 - 客户ID: {}, 总金额: {}, 待收: {}, 逾期: {}",
                customerId, totalAmount, pendingAmount, overdueAmount);

            return summary;
        } catch (Exception e) {
            log.error("获取客户应收账款汇总失败 - 客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
            throw new ServiceException("获取客户应收账款汇总失败：" + e.getMessage());
        }
    }

    /**
     * 从销售订单明细生成应收单明细
     *
     * @param receivableId     应收单ID
     * @param saleOrderItemIds 销售订单明细ID列表
     * @param operatorId       操作人ID（暂不使用）
     * @param operatorName     操作人姓名（暂不使用）
     * @return 是否生成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateReceivableItemsFromSaleOrderItems(Long receivableId, List<Long> saleOrderItemIds,
                                                             Long operatorId, String operatorName) {
        try {
            if (saleOrderItemIds == null || saleOrderItemIds.isEmpty()) {
                throw new ServiceException("销售订单明细ID列表不能为空");
            }

            // 1. 校验应收单是否存在
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在");
            }

            log.info("开始从销售订单明细生成应收单明细 - 应收单ID: {}, 明细数量: {}",
                receivableId, saleOrderItemIds.size());

            // 2. 获取销售订单明细信息并创建应收单明细
            for (Long saleOrderItemId : saleOrderItemIds) {
                // 获取销售订单明细信息
                var saleOrderItem = saleOrderItemService.queryById(saleOrderItemId);
                if (saleOrderItem == null) {
                    log.warn("销售订单明细不存在，跳过 - 明细ID: {}", saleOrderItemId);
                    continue;
                }

                // 创建应收单明细
                FinArReceivableItem receivableItem = new FinArReceivableItem();
                receivableItem.setReceivableId(receivableId);
                receivableItem.setSourceId(receivable.getSourceId());
                receivableItem.setSourceCode(receivable.getSourceCode());
                receivableItem.setSourceName(receivable.getSourceName());
                receivableItem.setSourceType(receivable.getSourceType());
                receivableItem.setSourceItemId(saleOrderItemId);

                // 从销售订单明细复制产品信息和金额信息
                receivableItem.setProductId(saleOrderItem.getProductId());
                receivableItem.setProductCode(saleOrderItem.getProductCode());
                receivableItem.setProductName(saleOrderItem.getProductName());
                receivableItem.setUnitId(saleOrderItem.getUnitId());
                receivableItem.setUnitCode(saleOrderItem.getUnitCode());
                receivableItem.setUnitName(saleOrderItem.getUnitName());
                // 修复类型转换：quantity字段已改为BigDecimal类型，直接设置
                receivableItem.setQuantity(saleOrderItem.getQuantity() != null ? saleOrderItem.getQuantity() : BigDecimal.ZERO);
                receivableItem.setPrice(saleOrderItem.getPrice());
                receivableItem.setPriceExclusiveTax(saleOrderItem.getPriceExclusiveTax());
                receivableItem.setAmount(saleOrderItem.getAmount());
                receivableItem.setAmountExclusiveTax(saleOrderItem.getAmountExclusiveTax());
                receivableItem.setTaxRate(saleOrderItem.getTaxRate());
                receivableItem.setTaxAmount(saleOrderItem.getTaxAmount());

                receivableItem.setRemark("从销售订单明细生成 - 明细ID: " + saleOrderItemId);

                // 插入应收单明细
                var receivableItemBo = MapstructUtils.convert(receivableItem, com.iotlaser.spms.erp.domain.bo.FinArReceivableItemBo.class);
                finArReceivableItemService.insertByBo(receivableItemBo);

                log.info("创建应收单明细成功 - 应收单ID: {}, 销售订单明细ID: {}, 产品: {}, 金额: {}",
                    receivableId, saleOrderItemId, saleOrderItem.getProductName(), saleOrderItem.getAmount());
            }

            // 3. 更新应收单主表金额汇总
            summarizeFromItems(receivableId);

            log.info("从销售订单明细生成应收单明细完成 - 应收单ID: {}, 生成明细数量: {}",
                receivableId, saleOrderItemIds.size());

            return true;
        } catch (Exception e) {
            log.error("从销售订单明细生成应收单明细失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            throw new ServiceException("生成应收单明细失败：" + e.getMessage());
        }
    }

    /**
     * 从销售出库单生成应收单（重载方法）
     *
     * @param outboundId   出库单ID
     * @param orderId      销售订单ID
     * @param customerId   客户ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 应收单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long generateFromSaleOutbound(Long outboundId, Long orderId, Long customerId,
                                         Long operatorId, String operatorName) {
        try {
            // 1. 获取出库单信息
            SaleOutboundVo outbound = saleOutboundService.queryById(outboundId);
            if (outbound == null) {
                throw new ServiceException("销售出库单不存在");
            }

            // 校验出库单状态
            if (!"COMPLETED".equals(outbound.getOutboundStatus())) {
                throw new ServiceException("只有已完成的出库单才能生成应收单");
            }

            // 检查是否已经生成过应收单
            if (existsByOutboundId(outboundId)) {
                throw new ServiceException("该出库单已生成应收单，不能重复生成");
            }

            // 2. 创建应收单主记录
            FinArReceivable receivable = new FinArReceivable();

            // 生成应收单编号和名称
            receivable.setReceivableCode(generateReceivableCode());
            receivable.setReceivableName("销售应收-" + outbound.getCustomerName() + "-" + outbound.getOutboundCode());

            // 客户信息
            receivable.setCustomerId(customerId);
            receivable.setCustomerCode(outbound.getCustomerCode());
            receivable.setCustomerName(outbound.getCustomerName());

            // 来源信息（应收单关联出库单）
            receivable.setDirectSourceId(outboundId);
            receivable.setDirectSourceCode(outbound.getOutboundCode());
            receivable.setDirectSourceName(outbound.getOutboundName());
            receivable.setDirectSourceType("SALE_OUTBOUND");

            // 源订单信息（应收单追溯到销售订单）
            receivable.setSourceId(orderId);
            receivable.setSourceCode(outbound.getOrderCode());
            receivable.setSourceName(outbound.getOrderName());
            receivable.setSourceType("SALE_ORDER");

            // 设置应收状态
            receivable.setReceivableStatus("PENDING");

            // 插入应收单主记录
            int result = baseMapper.insert(receivable);
            if (result <= 0) {
                throw new ServiceException("应收单生成失败");
            }

            Long receivableId = receivable.getReceivableId();

            log.info("从销售出库单生成应收单成功 - 出库单: {}, 应收单: {}, 操作人: {}",
                outbound.getOutboundCode(), receivable.getReceivableCode(), operatorName);

            return receivableId;
        } catch (Exception e) {
            log.error("从销售出库单生成应收单失败 - 出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);
            throw new ServiceException("从销售出库单生成应收单失败：" + e.getMessage());
        }
    }

    /**
     * 从出库单明细生成应收单明细
     *
     * @param receivableId    应收单ID
     * @param outboundItemIds 出库单明细ID列表
     * @param operatorId      操作人ID（暂不使用）
     * @param operatorName    操作人姓名（暂不使用）
     * @return 是否生成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateReceivableItemsFromOutboundItems(Long receivableId, List<Long> outboundItemIds,
                                                            Long operatorId, String operatorName) {
        try {
            if (outboundItemIds == null || outboundItemIds.isEmpty()) {
                throw new ServiceException("出库单明细ID列表不能为空");
            }

            // 1. 校验应收单是否存在
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在");
            }

            log.info("开始从出库单明细生成应收单明细 - 应收单ID: {}, 明细数量: {}",
                receivableId, outboundItemIds.size());

            // 2. 获取出库单明细信息并创建应收单明细
            for (Long outboundItemId : outboundItemIds) {
                // 获取出库单明细信息
                var outboundItem = saleOutboundItemService.queryById(outboundItemId);
                if (outboundItem == null) {
                    log.warn("出库单明细不存在，跳过 - 明细ID: {}", outboundItemId);
                    continue;
                }

                // 创建应收单明细
                FinArReceivableItem receivableItem = new FinArReceivableItem();
                receivableItem.setReceivableId(receivableId);

                // 设置源订单信息（明细表负责维护完整的源订单关联）
                receivableItem.setSourceId(receivable.getSourceId());        // 销售订单ID
                receivableItem.setSourceCode(receivable.getSourceCode());    // 销售订单编号
                receivableItem.setSourceName(receivable.getSourceName());    // 销售订单名称
                receivableItem.setSourceType("SALE_ORDER");                  // 源类型
                // TODO: 需要通过出库单明细获取关联的销售订单明细ID
                // receivableItem.setSourceItemId(outboundItem.getOrderItemId());

                // 设置直接来源信息（来自应收单主表）
                receivableItem.setDirectSourceId(receivable.getDirectSourceId());     // 出库单ID
                receivableItem.setDirectSourceCode(receivable.getDirectSourceCode()); // 出库单编号
                receivableItem.setDirectSourceName(receivable.getDirectSourceName()); // 出库单名称
                receivableItem.setDirectSourceType("SALE_OUTBOUND");                  // 直接来源类型
                // TODO: FinArReceivableItem实体中没有directSourceItemId字段
                // receivableItem.setDirectSourceItemId(outboundItemId);                 // 出库单明细ID

                // 从出库单明细复制产品信息和金额信息
                receivableItem.setProductId(outboundItem.getProductId());
                receivableItem.setProductCode(outboundItem.getProductCode());
                receivableItem.setProductName(outboundItem.getProductName());
                receivableItem.setUnitId(outboundItem.getUnitId());
                receivableItem.setUnitCode(outboundItem.getUnitCode());
                receivableItem.setUnitName(outboundItem.getUnitName());
                // 修复类型转换：quantity字段已改为BigDecimal类型，直接设置
                receivableItem.setQuantity(outboundItem.getQuantity() != null ? outboundItem.getQuantity() : BigDecimal.ZERO);
                receivableItem.setPrice(outboundItem.getPrice());
                receivableItem.setPriceExclusiveTax(outboundItem.getPriceExclusiveTax());
                receivableItem.setAmount(outboundItem.getAmount());
                receivableItem.setAmountExclusiveTax(outboundItem.getAmountExclusiveTax());
                receivableItem.setTaxRate(outboundItem.getTaxRate());
                receivableItem.setTaxAmount(outboundItem.getTaxAmount());

                receivableItem.setRemark("从出库单明细生成 - 明细ID: " + outboundItemId);

                // 插入应收单明细
                var receivableItemBo = MapstructUtils.convert(receivableItem, com.iotlaser.spms.erp.domain.bo.FinArReceivableItemBo.class);
                finArReceivableItemService.insertByBo(receivableItemBo);

                log.info("创建应收单明细成功 - 应收单ID: {}, 出库单明细ID: {}, 产品: {}, 金额: {}",
                    receivableId, outboundItemId, outboundItem.getProductName(), outboundItem.getAmount());
            }

            // 3. 更新应收单主表金额汇总
            summarizeFromItems(receivableId);

            log.info("从出库单明细生成应收单明细完成 - 应收单ID: {}, 生成明细数量: {}",
                receivableId, outboundItemIds.size());

            return true;
        } catch (Exception e) {
            log.error("从出库单明细生成应收单明细失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            throw new ServiceException("生成应收单明细失败：" + e.getMessage());
        }
    }

    /**
     * 从应收单生成收款单
     *
     * @param receivableId  应收单ID
     * @param receiptAmount 收款金额
     * @param accountId     账户ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 收款单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long generateReceiptOrderFromReceivable(Long receivableId, BigDecimal receiptAmount,
                                                   Long accountId, Long operatorId, String operatorName) {
        try {
            // 1. 校验应收单状态
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在");
            }

            if (!"PENDING".equals(receivable.getReceivableStatus())) {
                throw new ServiceException("只有待收款状态的应收单才能生成收款单");
            }

            // 2. 校验收款金额
            if (receiptAmount == null || receiptAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("收款金额必须大于0");
            }

            if (receiptAmount.compareTo(receivable.getAmount()) > 0) {
                throw new ServiceException("收款金额不能超过应收单金额");
            }

            log.info("开始从应收单生成收款单 - 应收单: {}, 收款金额: {}, 操作人: {}",
                receivable.getReceivableCode(), receiptAmount, operatorName);

            // 3. 创建收款单
            // TODO: 需要注入FinArReceiptOrderService
            // Long receiptId = finArReceiptOrderService.generateFromReceivable(
            //     receivableId,
            //     receivable.getCustomerId(),
            //     receiptAmount,
            //     accountId,
            //     operatorId,
            //     operatorName
            // );

            // 暂时返回模拟的收款单ID
            Long receiptId = System.currentTimeMillis();

            // 4. 建立收款单与应收单的关联
            // TODO: 需要在FinArReceiptReceivableLinkService中创建关联记录
            // finArReceiptReceivableLinkService.applyReceiptToReceivable(
            //     receiptId, receivableId, receiptAmount, "从应收单生成收款单"
            // );

            log.info("从应收单生成收款单成功 - 应收单: {}, 收款单ID: {}, 收款金额: {}",
                receivable.getReceivableCode(), receiptId, receiptAmount);

            return receiptId;
        } catch (Exception e) {
            log.error("从应收单生成收款单失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            throw new ServiceException("生成收款单失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(FinArReceivableBo bo) {
        // 填充客户信息
        if (bo.getCustomerId() != null) {
            CompanyVo customer = companyService.queryById(bo.getCustomerId());
            if (customer != null) {
                bo.setCustomerCode(customer.getCompanyCode());
                bo.setCustomerName(customer.getCompanyName());
            }
        }
    }

    /**
     * 填充责任人信息
     */
    private void fillResponsiblePersonInfo(FinArReceivableBo bo) {
        Long currentUserId = LoginHelper.getUserId();
        String currentUserName = LoginHelper.getUsername();

        // 如果是新增，设置申请人
        if (bo.getReceivableId() == null) {
            bo.setApplicantId(currentUserId);
            bo.setApplicantName(currentUserName);
        }

        // 设置经办人（每次更新都更新）
        bo.setHandlerId(currentUserId);
        bo.setHandlerName(currentUserName);
    }

    /**
     * 计算金额（价税分离）
     */
    private void calculateAmounts(FinArReceivableBo bo) {
        // 如果有含税金额和税率，计算不含税金额和税额
        // TODO: 在FinArReceivableBo中添加getTaxRate()方法
        // if (bo.getAmount() != null && bo.getTaxRate() != null && bo.getTaxRate().compareTo(BigDecimal.ZERO) > 0) {
        //     BigDecimal amount = bo.getAmount(); // 含税金额
        //     BigDecimal taxRate = bo.getTaxRate(); // 税率

        //     // 计算不含税金额：不含税金额 = 含税金额 / (1 + 税率/100)
        //     BigDecimal divisor = BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
        //     BigDecimal amountExclusiveTax = amount.divide(divisor, 2, RoundingMode.HALF_UP);
        //     BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        //
        //     bo.setAmountExclusiveTax(amountExclusiveTax);
        //     bo.setTaxAmount(taxAmount);
        // } else if (bo.getAmountExclusiveTax() != null) {
        //     // 如果有不含税金额，确保含税金额和税额的一致性
        //     BigDecimal amountExclusiveTax = bo.getAmountExclusiveTax();
        //     BigDecimal taxAmount = bo.getTaxAmount() != null ? bo.getTaxAmount() : BigDecimal.ZERO;
        //     bo.setAmount(amountExclusiveTax.add(taxAmount));
        // }
    }

    /**
     * 校验状态流转合法性
     */
    private boolean isValidStatusTransition(FinArReceivableStatus fromStatus, FinArReceivableStatus toStatus) {
        if (fromStatus == null || toStatus == null) {
            return true;
        }

        // 定义合法的状态流转
        switch (fromStatus) {
            case PENDING:
                return toStatus == FinArReceivableStatus.PARTIALLY_PAID ||
                    toStatus == FinArReceivableStatus.FULLY_PAID ||
                    toStatus == FinArReceivableStatus.OVERDUE ||
                    toStatus == FinArReceivableStatus.CANCELLED;
            case PARTIALLY_PAID:
                return toStatus == FinArReceivableStatus.FULLY_PAID ||
                    toStatus == FinArReceivableStatus.OVERDUE ||
                    toStatus == FinArReceivableStatus.CANCELLED;
            case OVERDUE:
                return toStatus == FinArReceivableStatus.PARTIALLY_PAID ||
                    toStatus == FinArReceivableStatus.FULLY_PAID ||
                    toStatus == FinArReceivableStatus.CANCELLED;
            case FULLY_PAID:
            case CANCELLED:
                return toStatus == fromStatus; // 终态，不能再变更
            default:
                return false;
        }
    }

    /**
     * 从销售订单明细创建应收发票明细
     * TODO: 当前方法为框架方法，待应收发票明细表创建后完善实现
     *
     * @param receivableId 应收单ID
     * @param saleOrderId  销售订单ID
     */
    private void createReceivableItemsFromSaleOrder(Long receivableId, Long saleOrderId) {
        try {
            log.info("开始创建应收发票明细 - 应收单ID: {}, 销售订单ID: {}", receivableId, saleOrderId);

            // TODO: 实现应收发票明细创建逻辑
            // 当前缺少以下组件，需要按优先级实现：

            // 1. 创建应收发票明细实体类
            // @Entity FinArReceivableItem
            // - receivableId (关联应收单)
            // - sourceItemId (关联销售订单明细)
            // - productId, productCode, productName (产品信息)
            // - unitId, unitCode, unitName (计量单位信息)
            // - quantity, price, amount (数量和金额信息)
            // - taxRate, taxAmount (税务信息)

            // 2. 创建应收发票明细服务接口
            // interface IFinArReceivableItemService
            // - createItemFromOrderItem(receivableId, orderItem)
            // - getItemsByReceivableId(receivableId)
            // - updateItemAmount(itemId, amount)

            // 3. 实现明细创建逻辑
            // List<SaleOrderItemVo> orderItems = saleOrderItemService.queryByOrderId(saleOrderId);
            // for (SaleOrderItemVo orderItem : orderItems) {
            //     FinArReceivableItem item = new FinArReceivableItem();
            //
            //     // 关联信息
            //     item.setReceivableId(receivableId);
            //     item.setSourceItemId(orderItem.getItemId());
            //
            //     // 产品信息传递
            //     item.setProductId(orderItem.getProductId());
            //     item.setProductCode(orderItem.getProductCode());
            //     item.setProductName(orderItem.getProductName());
            //
            //     // 计量单位传递
            //     item.setUnitId(orderItem.getUnitId());
            //     item.setUnitCode(orderItem.getUnitCode());
            //     item.setUnitName(orderItem.getUnitName());
            //
            //     // 数量和金额传递
            //     item.setQuantity(orderItem.getQuantity());
            //     item.setPrice(orderItem.getPrice());
            //     item.setPriceExclusiveTax(orderItem.getPriceExclusiveTax());
            //     item.setAmount(orderItem.getAmount());
            //     item.setAmountExclusiveTax(orderItem.getAmountExclusiveTax());
            //     item.setTaxRate(orderItem.getTaxRate());
            //     item.setTaxAmount(orderItem.getTaxAmount());
            //
            //     // 保存明细
            //     receivableItemService.insert(item);
            // }

            // 4. 校验明细汇总与主表一致性
            // BigDecimal itemsTotal = receivableItemService.calculateTotalAmount(receivableId);
            // FinArReceivable receivable = baseMapper.selectById(receivableId);
            // if (receivable.getAmount().subtract(itemsTotal).abs().compareTo(new BigDecimal("0.01")) > 0) {
            //     log.warn("应收发票明细汇总与主表金额不一致 - 主表: {}, 明细汇总: {}",
            //         receivable.getAmount(), itemsTotal);
            // }

            log.info("应收发票明细创建完成 - 应收单ID: {} (当前为TODO实现)", receivableId);

        } catch (Exception e) {
            log.error("创建应收发票明细失败 - 应收单ID: {}, 销售订单ID: {}, 错误: {}",
                receivableId, saleOrderId, e.getMessage(), e);
            // 注意：当前为TODO实现，不抛出异常以免影响主流程
            // throw new ServiceException("创建应收发票明细失败：" + e.getMessage());
        }
    }

    // ==================== 应收发票明细处理方法（TODO） ====================

    /**
     * 校验应收发票明细与主表金额一致性
     * TODO: 当前方法为框架方法，待应收发票明细表创建后完善实现
     *
     * @param receivableId 应收单ID
     * @return 是否一致
     */
    private boolean validateReceivableItemsConsistency(Long receivableId) {
        try {
            // TODO: 实现明细与主表一致性校验
            // 1. 获取应收单主表金额
            // FinArReceivable receivable = baseMapper.selectById(receivableId);
            //
            // 2. 计算明细汇总金额
            // List<FinArReceivableItem> items = receivableItemService.getItemsByReceivableId(receivableId);
            // BigDecimal itemsTotal = items.stream()
            //     .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
            //     .reduce(BigDecimal.ZERO, BigDecimal::add);
            //
            // 3. 比较金额差异
            // BigDecimal difference = receivable.getAmount().subtract(itemsTotal).abs();
            // boolean isConsistent = difference.compareTo(new BigDecimal("0.01")) <= 0;
            //
            // if (!isConsistent) {
            //     log.warn("应收发票金额不一致 - 应收单: {}, 主表金额: {}, 明细汇总: {}, 差异: {}",
            //         receivable.getReceivableCode(), receivable.getAmount(), itemsTotal, difference);
            // }
            //
            // return isConsistent;

            log.debug("应收发票明细一致性校验 - 应收单ID: {} (当前为TODO实现，返回true)", receivableId);
            return true; // 临时返回true

        } catch (Exception e) {
            log.error("校验应收发票明细一致性失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据来源ID和来源类型查询应收单列表
     *
     * @param sourceId   来源ID
     * @param sourceType 来源类型
     * @return 应收单列表
     */
    @Override
    public List<FinArReceivableVo> queryBySourceId(Long sourceId, String sourceType) {
        try {
            log.info("根据来源查询应收单 - 来源ID: {}, 来源类型: {}", sourceId, sourceType);

            if (sourceId == null || StringUtils.isBlank(sourceType)) {
                log.warn("查询参数不完整 - 来源ID: {}, 来源类型: {}", sourceId, sourceType);
                return new ArrayList<>();
            }

            LambdaQueryWrapper<FinArReceivable> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinArReceivable::getSourceId, sourceId);
            wrapper.eq(FinArReceivable::getSourceType, sourceType);
            wrapper.eq(FinArReceivable::getStatus, "1"); // 只查询有效记录
            wrapper.orderByDesc(FinArReceivable::getCreateTime);

            List<FinArReceivable> receivables = baseMapper.selectList(wrapper);
            List<FinArReceivableVo> result = MapstructUtils.convert(receivables, FinArReceivableVo.class);

            log.info("根据来源查询应收单完成 - 来源ID: {}, 查询结果数: {}", sourceId, result.size());

            return result;

        } catch (Exception e) {
            log.error("根据来源查询应收单失败 - 来源ID: {}, 来源类型: {}, 错误: {}",
                sourceId, sourceType, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 更新应收单收款后的状态
     *
     * @param receivableId  应收单ID
     * @param paymentAmount 收款金额
     * @return 是否更新成功
     */
    @Override
    public Boolean updateStatusAfterPayment(Long receivableId, BigDecimal paymentAmount) {
        try {
            log.info("更新应收单收款后状态 - 应收单ID: {}, 收款金额: {}", receivableId, paymentAmount);

            if (receivableId == null || paymentAmount == null || paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("参数不完整或收款金额无效");
            }

            // 获取应收单信息
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在");
            }

            // 计算已收款金额
            BigDecimal totalAppliedAmount = finArReceiptReceivableLinkService.getAppliedAmountByReceivableId(receivableId);

            // 确定新状态
            String newStatus = determineReceivableStatusAfterPayment(receivable.getAmount(), totalAppliedAmount);

            // 更新状态
            if (!newStatus.equals(receivable.getReceivableStatus())) {
                FinArReceivable update = new FinArReceivable();
                update.setReceivableId(receivableId);
                update.setReceivableStatus(newStatus);

                int result = baseMapper.updateById(update);
                if (result <= 0) {
                    throw new ServiceException("更新应收单状态失败");
                }

                log.info("应收单状态更新成功 - 应收单ID: {}, 原状态: {}, 新状态: {}, 已收款: {}",
                    receivableId, receivable.getReceivableStatus(), newStatus, totalAppliedAmount);
            }

            return true;

        } catch (Exception e) {
            log.error("更新应收单收款后状态失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            throw new ServiceException("更新应收单状态失败：" + e.getMessage());
        }
    }

    /**
     * 确定应收单收款后的状态
     */
    private String determineReceivableStatusAfterPayment(BigDecimal totalAmount, BigDecimal appliedAmount) {
        if (AmountCalculationUtils.safeCompare(appliedAmount, BigDecimal.ZERO) == 0) {
            return FinArReceivableStatus.PENDING.getValue();
        } else if (AmountCalculationUtils.safeCompare(appliedAmount, totalAmount) >= 0) {
            return FinArReceivableStatus.FULLY_PAID.getValue();
        } else {
            return FinArReceivableStatus.PARTIALLY_PAID.getValue();
        }
    }

    /**
     * 销售订单信息内部类
     */
    public static class SaleOrderInfo {
        private Long saleOrderId;
        private String saleOrderCode;
        private Long customerId;
        private String customerCode;
        private String customerName;
        private BigDecimal amount;
        private LocalDate dueDate;

        // getters and setters
        public Long getSaleOrderId() {
            return saleOrderId;
        }

        public void setSaleOrderId(Long saleOrderId) {
            this.saleOrderId = saleOrderId;
        }

        public String getSaleOrderCode() {
            return saleOrderCode;
        }

        public void setSaleOrderCode(String saleOrderCode) {
            this.saleOrderCode = saleOrderCode;
        }

        public Long getCustomerId() {
            return customerId;
        }

        public void setCustomerId(Long customerId) {
            this.customerId = customerId;
        }

        public String getCustomerCode() {
            return customerCode;
        }

        public void setCustomerCode(String customerCode) {
            this.customerCode = customerCode;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public LocalDate getDueDate() {
            return dueDate;
        }

        public void setDueDate(LocalDate dueDate) {
            this.dueDate = dueDate;
        }
    }
}
