package com.iotlaser.spms.base.service;

import com.iotlaser.spms.base.domain.bo.AutoCodeRuleBo;
import com.iotlaser.spms.base.domain.vo.AutoCodeRuleVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 编码生成规则Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/03/11
 */
public interface IAutoCodeRuleService {

    /**
     * 查询编码生成规则
     *
     * @param ruleId 主键
     * @return 编码生成规则
     */
    AutoCodeRuleVo queryById(Long ruleId);

    /**
     * 分页查询编码生成规则列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 编码生成规则分页列表
     */
    TableDataInfo<AutoCodeRuleVo> queryPageList(AutoCodeRuleBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的编码生成规则列表
     *
     * @param bo 查询条件
     * @return 编码生成规则列表
     */
    List<AutoCodeRuleVo> queryList(AutoCodeRuleBo bo);

    /**
     * 新增编码生成规则
     *
     * @param bo 编码生成规则
     * @return 是否新增成功
     */
    Boolean insertByBo(AutoCodeRuleBo bo);

    /**
     * 修改编码生成规则
     *
     * @param bo 编码生成规则
     * @return 是否修改成功
     */
    Boolean updateByBo(AutoCodeRuleBo bo);

    /**
     * 校验并批量删除编码生成规则信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    AutoCodeRuleVo selectVoOneByRuleCode(String ruleCode);

    /**
     * 启用编码规则
     *
     * @param ruleCode 规则编码
     * @return 是否启用成功
     */
    Boolean enableCodeRule(String ruleCode);

    /**
     * 禁用编码规则
     *
     * @param ruleCode 规则编码
     * @return 是否禁用成功
     */
    Boolean disableCodeRule(String ruleCode);

    /**
     * 更新编码规则状态
     *
     * @param ruleCode 规则编码
     * @param status   状态（1-启用，0-禁用）
     * @return 是否更新成功
     */
    Boolean updateCodeRuleStatus(String ruleCode, String status);

    /**
     * 获取所有启用的编码规则
     *
     * @return 启用的编码规则列表
     */
    List<AutoCodeRuleVo> getActiveRules();

    /**
     * 重置编码序列号
     *
     * @param ruleCode    规则编码
     * @param startNumber 起始序号
     * @return 是否重置成功
     */
    Boolean resetCodeSequence(String ruleCode, Long startNumber);
}
