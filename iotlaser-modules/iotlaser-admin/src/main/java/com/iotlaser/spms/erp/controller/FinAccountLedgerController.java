package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinAccountLedgerBo;
import com.iotlaser.spms.erp.domain.vo.FinAccountLedgerVo;
import com.iotlaser.spms.erp.service.IFinAccountLedgerService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 账户收支流水
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finAccountLedger")
public class FinAccountLedgerController extends BaseController {

    private final IFinAccountLedgerService finAccountLedgerService;

    /**
     * 查询账户收支流水列表
     */
    @SaCheckPermission("erp:finAccountLedger:list")
    @GetMapping("/list")
    public TableDataInfo<FinAccountLedgerVo> list(FinAccountLedgerBo bo, PageQuery pageQuery) {
        return finAccountLedgerService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出账户收支流水列表
     */
    @SaCheckPermission("erp:finAccountLedger:export")
    @Log(title = "账户收支流水", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinAccountLedgerBo bo, HttpServletResponse response) {
        List<FinAccountLedgerVo> list = finAccountLedgerService.queryList(bo);
        ExcelUtil.exportExcel(list, "账户收支流水", FinAccountLedgerVo.class, response);
    }

    /**
     * 获取账户收支流水详细信息
     *
     * @param ledgerId 主键
     */
    @SaCheckPermission("erp:finAccountLedger:query")
    @GetMapping("/{ledgerId}")
    public R<FinAccountLedgerVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long ledgerId) {
        return R.ok(finAccountLedgerService.queryById(ledgerId));
    }

    /**
     * 新增账户收支流水
     */
    @SaCheckPermission("erp:finAccountLedger:add")
    @Log(title = "账户收支流水", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinAccountLedgerBo bo) {
        return toAjax(finAccountLedgerService.insertByBo(bo));
    }

    /**
     * 修改账户收支流水
     */
    @SaCheckPermission("erp:finAccountLedger:edit")
    @Log(title = "账户收支流水", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinAccountLedgerBo bo) {
        return toAjax(finAccountLedgerService.updateByBo(bo));
    }

    /**
     * 删除账户收支流水
     *
     * @param ledgerIds 主键串
     */
    @SaCheckPermission("erp:finAccountLedger:remove")
    @Log(title = "账户收支流水", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ledgerIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ledgerIds) {
        return toAjax(finAccountLedgerService.deleteWithValidByIds(List.of(ledgerIds), true));
    }
}
