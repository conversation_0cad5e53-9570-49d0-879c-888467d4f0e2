package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售出库对象 erp_sale_outbound
 *
 * <AUTHOR>
 * @date 2025/05/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_sale_outbound")
public class SaleOutbound extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 出库单ID
     */
    @TableId(value = "outbound_id")
    private Long outboundId;

    /**
     * 出库单编号
     */
    private String outboundCode;

    /**
     * 出库单名称
     */
    private String outboundName;

    /**
     * 销售订单ID
     */
    private Long orderId;

    /**
     * 销售订单编号
     */
    private String orderCode;

    /**
     * 销售订单名称
     */
    private String orderName;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 出库日期
     */
    private LocalDate outboundDate;

    /**
     * 出库状态
     */
    private SaleOutboundStatus outboundStatus;

    /**
     * 发货负责人ID
     */
    private Long handlerId;

    /**
     * 发货负责人
     */
    private String handlerName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 明细
     */
    @TableField(exist = false)
    private List<SaleOutboundItem> items;

    // ==================== 临时变量：汇总字段 ====================
    // TODO: 待数据库结构完善后，这些字段应该持久化到数据库

    /**
     * 总数量（临时变量）
     * TODO: 需要在数据库中添加 total_quantity DECIMAL(15,4) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalQuantity;

    /**
     * 总金额（临时变量）
     * TODO: 需要在数据库中添加 total_amount DECIMAL(15,2) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalAmount;
}
