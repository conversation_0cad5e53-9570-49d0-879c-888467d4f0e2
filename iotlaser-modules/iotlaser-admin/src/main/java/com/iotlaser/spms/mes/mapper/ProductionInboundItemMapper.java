package com.iotlaser.spms.mes.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.mes.domain.ProductionInboundItem;
import com.iotlaser.spms.mes.domain.vo.ProductionInboundItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 生产入库明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/23
 */
public interface ProductionInboundItemMapper extends BaseMapperPlus<ProductionInboundItem, ProductionInboundItemVo> {


    /**
     * 查询生产入库明细表及其关联信息
     */
    ProductionInboundItemVo queryByIdWith(@Param("itemId") Long itemId);

    /**
     * 分页查询生产入库明细表及其关联信息
     */
    List<ProductionInboundItemVo> queryPageListWith(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<ProductionInboundItem> wrapper);


}
