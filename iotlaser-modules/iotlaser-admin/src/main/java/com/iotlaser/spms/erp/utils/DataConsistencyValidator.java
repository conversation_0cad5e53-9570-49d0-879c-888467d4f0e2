package com.iotlaser.spms.erp.utils;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 数据一致性校验工具类
 * 用于校验ERP系统中各单据间的数据一致性
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Slf4j
public class DataConsistencyValidator {

    /**
     * 产品信息一致性校验
     * 校验采购订单、入库单、应付发票中的产品信息是否一致
     *
     * @param orderProductId     订单产品ID
     * @param orderProductCode   订单产品编码
     * @param orderProductName   订单产品名称
     * @param orderUnitId        订单计量单位ID
     * @param inboundProductId   入库产品ID
     * @param inboundProductCode 入库产品编码
     * @param inboundProductName 入库产品名称
     * @param inboundUnitId      入库计量单位ID
     * @param invoiceProductId   发票产品ID
     * @param invoiceProductCode 发票产品编码
     * @param invoiceProductName 发票产品名称
     * @param invoiceUnitId      发票计量单位ID
     * @return 校验结果
     */
    public static ValidationResult validateProductConsistency(
        Long orderProductId, String orderProductCode, String orderProductName, Long orderUnitId,
        Long inboundProductId, String inboundProductCode, String inboundProductName, Long inboundUnitId,
        Long invoiceProductId, String invoiceProductCode, String invoiceProductName, Long invoiceUnitId) {

        ValidationResult result = new ValidationResult();

        try {
            // 1. 产品ID一致性校验
            if (!Objects.equals(orderProductId, inboundProductId)) {
                result.addError(String.format("订单与入库产品ID不一致: 订单[%d] vs 入库[%d]", orderProductId, inboundProductId));
            }
            if (!Objects.equals(orderProductId, invoiceProductId)) {
                result.addError(String.format("订单与发票产品ID不一致: 订单[%d] vs 发票[%d]", orderProductId, invoiceProductId));
            }
            if (!Objects.equals(inboundProductId, invoiceProductId)) {
                result.addError(String.format("入库与发票产品ID不一致: 入库[%d] vs 发票[%d]", inboundProductId, invoiceProductId));
            }

            // 2. 产品编码一致性校验
            if (!Objects.equals(orderProductCode, inboundProductCode)) {
                result.addWarning(String.format("订单与入库产品编码不一致: 订单[%s] vs 入库[%s]", orderProductCode, inboundProductCode));
            }
            if (!Objects.equals(orderProductCode, invoiceProductCode)) {
                result.addWarning(String.format("订单与发票产品编码不一致: 订单[%s] vs 发票[%s]", orderProductCode, invoiceProductCode));
            }

            // 3. 产品名称一致性校验（允许轻微差异）
            if (!isSimilarProductName(orderProductName, inboundProductName)) {
                result.addWarning(String.format("订单与入库产品名称差异较大: 订单[%s] vs 入库[%s]", orderProductName, inboundProductName));
            }
            if (!isSimilarProductName(orderProductName, invoiceProductName)) {
                result.addWarning(String.format("订单与发票产品名称差异较大: 订单[%s] vs 发票[%s]", orderProductName, invoiceProductName));
            }

            // 4. 计量单位一致性校验
            if (!Objects.equals(orderUnitId, inboundUnitId)) {
                result.addError(String.format("订单与入库计量单位不一致: 订单[%d] vs 入库[%d]", orderUnitId, inboundUnitId));
            }
            if (!Objects.equals(orderUnitId, invoiceUnitId)) {
                result.addError(String.format("订单与发票计量单位不一致: 订单[%d] vs 发票[%d]", orderUnitId, invoiceUnitId));
            }

        } catch (Exception e) {
            log.error("产品信息一致性校验失败: {}", e.getMessage(), e);
            result.addError("产品信息一致性校验异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 数量逻辑合理性校验
     * 校验订单数量、入库数量、发票数量的逻辑关系
     *
     * @param orderQuantity   订单数量
     * @param inboundQuantity 入库数量
     * @param invoiceQuantity 发票数量
     * @return 校验结果
     */
    public static ValidationResult validateQuantityLogic(
        BigDecimal orderQuantity, BigDecimal inboundQuantity, BigDecimal invoiceQuantity) {

        ValidationResult result = new ValidationResult();

        try {
            // 1. 数量非空校验
            if (orderQuantity == null || orderQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                result.addError("订单数量必须大于0");
            }
            if (inboundQuantity == null || inboundQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                result.addError("入库数量必须大于0");
            }
            if (invoiceQuantity == null || invoiceQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                result.addError("发票数量必须大于0");
            }

            // 如果基础校验失败，不继续后续校验
            if (!result.isValid()) {
                return result;
            }

            // 2. 入库数量不能超过订单数量
            if (AmountCalculationUtils.safeCompare(inboundQuantity, orderQuantity) > 0) {
                result.addError(String.format("入库数量(%s)不能超过订单数量(%s)",
                    inboundQuantity.toString(), orderQuantity.toString()));
            }

            // 3. 发票数量应该等于入库数量（严格匹配）
            if (AmountCalculationUtils.safeCompare(invoiceQuantity, inboundQuantity) != 0) {
                result.addWarning(String.format("发票数量(%s)与入库数量(%s)不一致",
                    invoiceQuantity.toString(), inboundQuantity.toString()));
            }

            // 4. 发票数量不能超过订单数量
            if (AmountCalculationUtils.safeCompare(invoiceQuantity, orderQuantity) > 0) {
                result.addError(String.format("发票数量(%s)不能超过订单数量(%s)",
                    invoiceQuantity.toString(), orderQuantity.toString()));
            }

            // 5. 数量差异分析
            BigDecimal orderInboundDiff = AmountCalculationUtils.safeSubtract(orderQuantity, inboundQuantity);
            BigDecimal inboundInvoiceDiff = AmountCalculationUtils.safeSubtract(inboundQuantity, invoiceQuantity);

            if (orderInboundDiff.compareTo(BigDecimal.ZERO) > 0) {
                result.addWarning(String.format("订单未完全入库，差异数量: %s", orderInboundDiff.toString()));
            }
            if (inboundInvoiceDiff.abs().compareTo(BigDecimal.ZERO) > 0) {
                result.addWarning(String.format("入库与发票数量差异: %s", inboundInvoiceDiff.toString()));
            }

        } catch (Exception e) {
            log.error("数量逻辑合理性校验失败: {}", e.getMessage(), e);
            result.addError("数量逻辑合理性校验异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 金额一致性校验
     * 校验订单、入库、发票的金额计算一致性
     *
     * @param orderAmount     订单金额
     * @param orderQuantity   订单数量
     * @param orderPrice      订单单价
     * @param inboundAmount   入库金额
     * @param inboundQuantity 入库数量
     * @param inboundPrice    入库单价
     * @param invoiceAmount   发票金额
     * @param invoiceQuantity 发票数量
     * @param invoicePrice    发票单价
     * @return 校验结果
     */
    public static ValidationResult validateAmountConsistency(
        BigDecimal orderAmount, BigDecimal orderQuantity, BigDecimal orderPrice,
        BigDecimal inboundAmount, BigDecimal inboundQuantity, BigDecimal inboundPrice,
        BigDecimal invoiceAmount, BigDecimal invoiceQuantity, BigDecimal invoicePrice) {

        ValidationResult result = new ValidationResult();

        try {
            // 1. 订单金额一致性校验
            if (orderAmount != null && orderQuantity != null && orderPrice != null) {
                BigDecimal calculatedOrderAmount = AmountCalculationUtils.calculateLineAmount(orderQuantity, orderPrice);
                if (!AmountCalculationUtils.isWithinTolerance(orderAmount, calculatedOrderAmount, new BigDecimal("0.01"))) {
                    result.addError(String.format("订单金额计算不一致: 实际[%s] vs 计算[%s]",
                        AmountCalculationUtils.formatAmount(orderAmount),
                        AmountCalculationUtils.formatAmount(calculatedOrderAmount)));
                }
            }

            // 2. 入库金额一致性校验
            if (inboundAmount != null && inboundQuantity != null && inboundPrice != null) {
                BigDecimal calculatedInboundAmount = AmountCalculationUtils.calculateLineAmount(inboundQuantity, inboundPrice);
                if (!AmountCalculationUtils.isWithinTolerance(inboundAmount, calculatedInboundAmount, new BigDecimal("0.01"))) {
                    result.addError(String.format("入库金额计算不一致: 实际[%s] vs 计算[%s]",
                        AmountCalculationUtils.formatAmount(inboundAmount),
                        AmountCalculationUtils.formatAmount(calculatedInboundAmount)));
                }
            }

            // 3. 发票金额一致性校验
            if (invoiceAmount != null && invoiceQuantity != null && invoicePrice != null) {
                BigDecimal calculatedInvoiceAmount = AmountCalculationUtils.calculateLineAmount(invoiceQuantity, invoicePrice);
                if (!AmountCalculationUtils.isWithinTolerance(invoiceAmount, calculatedInvoiceAmount, new BigDecimal("0.01"))) {
                    result.addError(String.format("发票金额计算不一致: 实际[%s] vs 计算[%s]",
                        AmountCalculationUtils.formatAmount(invoiceAmount),
                        AmountCalculationUtils.formatAmount(calculatedInvoiceAmount)));
                }
            }

            // 4. 单价一致性校验
            if (orderPrice != null && inboundPrice != null) {
                if (!AmountCalculationUtils.isWithinTolerance(orderPrice, inboundPrice, new BigDecimal("1"))) {
                    result.addWarning(String.format("订单与入库单价差异较大: 订单[%s] vs 入库[%s]",
                        AmountCalculationUtils.formatAmount(orderPrice),
                        AmountCalculationUtils.formatAmount(inboundPrice)));
                }
            }

            if (orderPrice != null && invoicePrice != null) {
                if (!AmountCalculationUtils.isWithinTolerance(orderPrice, invoicePrice, new BigDecimal("1"))) {
                    result.addWarning(String.format("订单与发票单价差异较大: 订单[%s] vs 发票[%s]",
                        AmountCalculationUtils.formatAmount(orderPrice),
                        AmountCalculationUtils.formatAmount(invoicePrice)));
                }
            }

        } catch (Exception e) {
            log.error("金额一致性校验失败: {}", e.getMessage(), e);
            result.addError("金额一致性校验异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 供应商一致性校验
     *
     * @param orderSupplierId   订单供应商ID
     * @param inboundSupplierId 入库供应商ID
     * @param invoiceSupplierId 发票供应商ID
     * @return 校验结果
     */
    public static ValidationResult validateSupplierConsistency(
        Long orderSupplierId, Long inboundSupplierId, Long invoiceSupplierId) {

        ValidationResult result = new ValidationResult();

        try {
            if (!Objects.equals(orderSupplierId, inboundSupplierId)) {
                result.addError(String.format("订单与入库供应商不一致: 订单[%d] vs 入库[%d]", orderSupplierId, inboundSupplierId));
            }
            if (!Objects.equals(orderSupplierId, invoiceSupplierId)) {
                result.addError(String.format("订单与发票供应商不一致: 订单[%d] vs 发票[%d]", orderSupplierId, invoiceSupplierId));
            }
            if (!Objects.equals(inboundSupplierId, invoiceSupplierId)) {
                result.addError(String.format("入库与发票供应商不一致: 入库[%d] vs 发票[%d]", inboundSupplierId, invoiceSupplierId));
            }
        } catch (Exception e) {
            log.error("供应商一致性校验失败: {}", e.getMessage(), e);
            result.addError("供应商一致性校验异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 判断产品名称是否相似
     * 允许一定程度的差异（如空格、标点符号等）
     *
     * @param name1 产品名称1
     * @param name2 产品名称2
     * @return 是否相似
     */
    private static boolean isSimilarProductName(String name1, String name2) {
        if (StringUtils.isBlank(name1) || StringUtils.isBlank(name2)) {
            return StringUtils.isBlank(name1) && StringUtils.isBlank(name2);
        }

        // 去除空格和常见标点符号后比较
        String normalized1 = name1.replaceAll("[\\s\\-_.,，。]", "").toLowerCase();
        String normalized2 = name2.replaceAll("[\\s\\-_.,，。]", "").toLowerCase();

        // 完全相同
        if (normalized1.equals(normalized2)) {
            return true;
        }

        // 包含关系（较短的名称包含在较长的名称中）
        if (normalized1.length() > normalized2.length()) {
            return normalized1.contains(normalized2);
        } else {
            return normalized2.contains(normalized1);
        }
    }

    /**
     * 抛出校验异常
     *
     * @param result 校验结果
     * @throws ServiceException 如果校验失败
     */
    public static void throwIfInvalid(ValidationResult result) throws ServiceException {
        if (!result.isValid()) {
            throw new ServiceException("数据一致性校验失败: " + result.getErrorMessage());
        }
    }

    /**
     * 记录校验警告
     *
     * @param result 校验结果
     */
    public static void logWarnings(ValidationResult result) {
        if (!result.getWarnings().isEmpty()) {
            log.warn("数据一致性校验警告: {}", result.getWarningMessage());
        }
    }

    /**
     * 数据一致性校验结果
     */
    public static class ValidationResult {
        private boolean valid;
        private List<String> errors;
        private List<String> warnings;

        public ValidationResult() {
            this.valid = true;
            this.errors = new ArrayList<>();
            this.warnings = new ArrayList<>();
        }

        public boolean isValid() {
            return valid && errors.isEmpty();
        }

        public void addError(String error) {
            this.errors.add(error);
            this.valid = false;
        }

        public void addWarning(String warning) {
            this.warnings.add(warning);
        }

        public List<String> getErrors() {
            return errors;
        }

        public List<String> getWarnings() {
            return warnings;
        }

        public String getErrorMessage() {
            return String.join("; ", errors);
        }

        public String getWarningMessage() {
            return String.join("; ", warnings);
        }
    }
}
