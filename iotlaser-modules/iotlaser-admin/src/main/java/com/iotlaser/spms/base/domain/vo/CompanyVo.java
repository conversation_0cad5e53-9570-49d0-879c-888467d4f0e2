package com.iotlaser.spms.base.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.base.domain.Company;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;

/**
 * 公司信息视图对象 base_company
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Company.class)
public class CompanyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 公司ID
     */
    @ExcelProperty(value = "公司ID")
    private Long companyId;

    /**
     * 公司类型
     */
    @ExcelProperty(value = "公司类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "base_company_type")
    private String companyType;

    /**
     * 公司编码
     */
    @ExcelProperty(value = "公司编码")
    private String companyCode;

    /**
     * 公司名称
     */
    @ExcelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司简称
     */
    @ExcelProperty(value = "公司简称")
    private String shortName;

    /**
     * 公司英文名称
     */
    @ExcelProperty(value = "公司英文名称")
    private String englishName;

    /**
     * 公司简介
     */
    @ExcelProperty(value = "公司简介")
    private String intro;

    /**
     * 公司等级
     */
    @ExcelProperty(value = "公司等级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "base_company_level")
    private String level;

    /**
     * 公司LOGO地址
     */
    @ExcelProperty(value = "公司LOGO地址")
    private String logo;

    /**
     * 公司LOGO地址Url
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "logo")
    private String logoUrl;
    /**
     * 公司地址
     */
    @ExcelProperty(value = "公司地址")
    private String address;

    /**
     * 官网地址
     */
    @ExcelProperty(value = "官网地址")
    private String website;

    /**
     * 邮箱地址
     */
    @ExcelProperty(value = "邮箱地址")
    private String email;

    /**
     * 公司电话
     */
    @ExcelProperty(value = "公司电话")
    private String tel;

    /**
     * 联系人1
     */
    @ExcelProperty(value = "联系人1")
    private String contact1;

    /**
     * 联系人1-电话
     */
    @ExcelProperty(value = "联系人1-电话")
    private String contact1Tel;

    /**
     * 联系人1-邮箱
     */
    @ExcelProperty(value = "联系人1-邮箱")
    private String contact1Email;

    /**
     * 联系人2
     */
    @ExcelProperty(value = "联系人2")
    private String contact2;

    /**
     * 联系人2-电话
     */
    @ExcelProperty(value = "联系人2-电话")
    private String contact2Tel;

    /**
     * 联系人2-邮箱
     */
    @ExcelProperty(value = "联系人2-邮箱")
    private String contact2Email;

    /**
     * 是否供应商
     */
    @ExcelProperty(value = "是否供应商", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no_num")
    private String supplierFlag;

    /**
     * 是否客户
     */
    @ExcelProperty(value = "是否客户", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no_num")
    private String customerFlag;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

}
