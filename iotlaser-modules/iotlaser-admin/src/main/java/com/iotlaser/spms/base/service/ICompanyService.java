package com.iotlaser.spms.base.service;

import com.iotlaser.spms.base.domain.bo.CompanyBo;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 公司信息Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface ICompanyService {

    /**
     * 查询公司信息
     *
     * @param companyId 主键
     * @return 公司信息
     */
    CompanyVo queryById(Long companyId);

    /**
     * 分页查询公司信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 公司信息分页列表
     */
    TableDataInfo<CompanyVo> queryPageList(CompanyBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的公司信息列表
     *
     * @param bo 查询条件
     * @return 公司信息列表
     */
    List<CompanyVo> queryList(CompanyBo bo);

    /**
     * 新增公司信息
     *
     * @param bo 公司信息
     * @return 是否新增成功
     */
    Boolean insertByBo(CompanyBo bo);

    /**
     * 修改公司信息
     *
     * @param bo 公司信息
     * @return 是否修改成功
     */
    Boolean updateByBo(CompanyBo bo);

    /**
     * 校验并批量删除公司信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 更新公司状态
     *
     * @param companyId 公司ID
     * @param status    状态（1-启用，0-禁用）
     * @return 是否更新成功
     */
    Boolean updateCompanyStatus(Long companyId, String status);

    /**
     * 启用公司
     *
     * @param companyId 公司ID
     * @return 是否启用成功
     */
    Boolean enableCompany(Long companyId);

    /**
     * 禁用公司
     *
     * @param companyId 公司ID
     * @return 是否禁用成功
     */
    Boolean disableCompany(Long companyId);

    /**
     * 获取所有启用的公司
     *
     * @return 启用的公司列表
     */
    List<CompanyVo> getActiveCompanies();

    /**
     * 根据公司类型获取公司列表
     *
     * @param companyType 公司类型
     * @return 公司列表
     */
    List<CompanyVo> getCompaniesByType(String companyType);

    /**
     * 获取供应商列表
     *
     * @return 供应商列表
     */
    List<CompanyVo> getSuppliers();

    /**
     * 获取客户列表
     *
     * @return 客户列表
     */
    List<CompanyVo> getCustomers();

    /**
     * 根据公司编码查询公司信息
     *
     * @param companyCode 公司编码
     * @return 公司信息
     */
    CompanyVo getByCompanyCode(String companyCode);

    /**
     * 批量更新公司状态
     *
     * @param companyIds 公司ID列表
     * @param status     状态
     * @return 是否更新成功
     */
    Boolean batchUpdateStatus(List<Long> companyIds, String status);
}
