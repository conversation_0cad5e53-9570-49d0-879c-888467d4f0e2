package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.common.enums.BatchProcessStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购入库批次明细对象 erp_purchase_inbound_item_batch
 *
 * <AUTHOR>
 * @date 2025/05/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_purchase_inbound_item_batch")
public class PurchaseInboundItemBatch extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 批次ID
     */
    @TableId(value = "batch_id")
    private Long batchId;

    /**
     * 明细ID
     */
    private Long itemId;

    /**
     * 入库单ID
     */
    private Long inboundId;

    /**
     * 库存ID
     */
    private Long inventoryBatchId;

    /**
     * 内部批次号/成品序列号
     */
    private String internalBatchNumber;

    /**
     * 供应商批次号
     */
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    private String serialNumber;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 单价（含税）
     */
    private BigDecimal price;

    /**
     * 单价（不含税）
     */
    private BigDecimal priceExclusiveTax;

    /**
     * 金额（含税）
     */
    private BigDecimal amount;

    /**
     * 金额（不含税）
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 生产时间
     */
    private LocalDateTime productionTime;

    /**
     * 失效时间
     */
    private LocalDateTime expiryTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    // ==================== 临时变量：批次状态管理 ====================
    // TODO: 待数据库结构完善后，这些字段应该持久化到数据库

    /**
     * 批次处理状态（临时变量）
     * TODO: 需要在数据库中添加 batch_process_status VARCHAR(20) 字段
     */
    @TableField(exist = false)
    private BatchProcessStatus batchProcessStatus;

    /**
     * 状态变更时间（临时变量）
     * TODO: 需要在数据库中添加 status_change_time DATETIME 字段
     */
    @TableField(exist = false)
    private LocalDateTime statusChangeTime;

    /**
     * 状态变更原因（临时变量）
     * TODO: 需要在数据库中添加 status_change_reason VARCHAR(500) 字段
     */
    @TableField(exist = false)
    private String statusChangeReason;

    /**
     * 状态变更操作人ID（临时变量）
     * TODO: 需要在数据库中添加 status_change_operator_id BIGINT 字段
     */
    @TableField(exist = false)
    private Long statusChangeOperatorId;

    /**
     * 状态变更操作人姓名（临时变量）
     * TODO: 需要在数据库中添加 status_change_operator_name VARCHAR(100) 字段
     */
    @TableField(exist = false)
    private String statusChangeOperatorName;

}
