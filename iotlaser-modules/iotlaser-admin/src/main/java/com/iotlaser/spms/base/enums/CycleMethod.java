package com.iotlaser.spms.base.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CycleMethod implements IDictEnum<String> {

    YEAR("YEAR", "按年", "按年周期"),
    MONTH("MONTH", "按月", "按月周期"),
    DAY("DAY", "按日", "按日周期"),
    HOUR("HOUR", "按小时", "按小时周期"),
    MINUTE("MINUTE", "按分钟", "按分钟周期"),
    OTHER("OTHER", "其他", "其他周期方式");

    public final static String DICT_CODE = "base_cycle_method";
    public final static String DICT_NAME = "周期方法";
    public final static String DICT_DESC = "定义编码生成规则中的周期重置方式，包括按年、按月、按日等不同的周期类型";
    /**
     * 周期方法值
     */
    @EnumValue
    private final String value;
    /**
     * 周期方法名称
     */
    private final String name;
    /**
     * 周期方法描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 周期方法值
     * @return 周期方法枚举
     */
    public static CycleMethod getByValue(String value) {
        for (CycleMethod cycleMethod : values()) {
            if (cycleMethod.getValue().equals(value)) {
                return cycleMethod;
            }
        }
        return OTHER;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }


}
