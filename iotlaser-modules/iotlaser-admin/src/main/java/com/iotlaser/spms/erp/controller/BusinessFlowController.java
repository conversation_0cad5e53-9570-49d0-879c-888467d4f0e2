package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import com.iotlaser.spms.erp.service.ISaleOutboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;

import static org.dromara.common.satoken.utils.LoginHelper.getLoginUser;

/**
 * 业务流程控制器
 * 提供完整的销售和采购业务流程API
 *
 * <AUTHOR>
 * @date 2024-12-22
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/erp/businessFlow")
public class BusinessFlowController extends BaseController {

    private final ISaleOutboundService saleOutboundService;
    private final IPurchaseInboundService purchaseInboundService;

    /**
     * 销售业务完整流程：从出库完成到收款入账
     *
     * @param request 销售流程请求
     * @return 完整的业务结果
     */
    @SaCheckPermission("erp:businessFlow:sale:execute")
    @PostMapping("/sale/completeFlow")
    public R<Map<String, Object>> completeSaleFlow(@RequestBody @Validated SaleFlowRequest request) {
        try {
            log.info("开始执行销售业务完整流程 - 请求: {}", request);

            Map<String, Object> result = saleOutboundService.completeSaleBusinessFlow(
                request.getOutboundId(),
                request.getReceiptAmount(),
                request.getAccountId(),
                getLoginUser().getUserId(),
                getLoginUser().getUsername()
            );

            log.info("销售业务完整流程执行完成 - 结果: {}", result);
            return R.ok(result);
        } catch (Exception e) {
            log.error("销售业务完整流程执行失败 - 请求: {}, 错误: {}", request, e.getMessage(), e);
            return R.fail("销售业务完整流程执行失败：" + e.getMessage());
        }
    }

    /**
     * 采购业务完整流程：从入库完成到付款出账
     *
     * @param request 采购流程请求
     * @return 完整的业务结果
     */
    @SaCheckPermission("erp:businessFlow:purchase:execute")
    @PostMapping("/purchase/completeFlow")
    public R<Map<String, Object>> completePurchaseFlow(@RequestBody @Validated PurchaseFlowRequest request) {
        try {
            log.info("开始执行采购业务完整流程 - 请求: {}", request);

            Map<String, Object> result = purchaseInboundService.completePurchaseBusinessFlow(
                request.getInboundId(),
                request.getPaymentAmount(),
                request.getAccountId(),
                getLoginUser().getUserId(),
                getLoginUser().getUsername()
            );

            log.info("采购业务完整流程执行完成 - 结果: {}", result);
            return R.ok(result);
        } catch (Exception e) {
            log.error("采购业务完整流程执行失败 - 请求: {}, 错误: {}", request, e.getMessage(), e);
            return R.fail("采购业务完整流程执行失败：" + e.getMessage());
        }
    }

    /**
     * 销售流程请求DTO
     */
    @lombok.Data
    public static class SaleFlowRequest {
        /**
         * 出库单ID
         */
        private Long outboundId;

        /**
         * 收款金额
         */
        private BigDecimal receiptAmount;

        /**
         * 账户ID
         */
        private Long accountId;
    }

    /**
     * 采购流程请求DTO
     */
    @lombok.Data
    public static class PurchaseFlowRequest {
        /**
         * 入库单ID
         */
        private Long inboundId;

        /**
         * 付款金额
         */
        private BigDecimal paymentAmount;

        /**
         * 账户ID
         */
        private Long accountId;
    }
}
