package com.iotlaser.spms.pro.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品类型枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-15
 */
@Getter
@AllArgsConstructor
public enum ProductType implements IDictEnum<String> {

    RAW_MATERIAL("raw_material", "原材料", "用于生产的原始材料"),
    SEMI_FINISHED("semi_finished", "半成品", "生产过程中的中间产品"),
    FINISHED_PRODUCT("finished_product", "成品", "最终完成的产品"),
    CONSUMABLE("consumable", "耗材", "生产过程中消耗的材料"),
    TOOL("tool", "工具", "生产用工具设备"),
    SPARE_PART("spare_part", "备件", "设备维修用备件"),
    VIRTUAL("virtual", "虚拟件", "用于管理的虚拟产品");

    public final static String DICT_CODE = "pro_product_type";
    public final static String DICT_NAME = "产品类型";
    public final static String DICT_DESC = "定义产品在生产流程中的分类，包括原材料、半成品、成品等不同阶段的产品类型";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 产品类型枚举
     */
    public static ProductType getByValue(String value) {
        for (ProductType type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }


}
