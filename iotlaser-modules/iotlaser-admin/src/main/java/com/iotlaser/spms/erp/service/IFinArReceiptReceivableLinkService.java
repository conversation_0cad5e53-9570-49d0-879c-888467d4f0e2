package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinArReceiptReceivableLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptReceivableLinkVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 收款单与应收单核销关系Service接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
public interface IFinArReceiptReceivableLinkService {

    /**
     * 查询收款单与应收单核销关系
     *
     * @param linkId 主键
     * @return 收款单与应收单核销关系
     */
    FinArReceiptReceivableLinkVo queryById(Long linkId);

    /**
     * 分页查询收款单与应收单核销关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 收款单与应收单核销关系分页列表
     */
    TableDataInfo<FinArReceiptReceivableLinkVo> queryPageList(FinArReceiptReceivableLinkBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的收款单与应收单核销关系列表
     *
     * @param bo 查询条件
     * @return 收款单与应收单核销关系列表
     */
    List<FinArReceiptReceivableLinkVo> queryList(FinArReceiptReceivableLinkBo bo);

    /**
     * 新增收款单与应收单核销关系
     *
     * @param bo 收款单与应收单核销关系
     * @return 是否新增成功
     */
    Boolean insertByBo(FinArReceiptReceivableLinkBo bo);

    /**
     * 修改收款单与应收单核销关系
     *
     * @param bo 收款单与应收单核销关系
     * @return 是否修改成功
     */
    Boolean updateByBo(FinArReceiptReceivableLinkBo bo);

    /**
     * 校验并批量删除收款单与应收单核销关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取应收单已核销金额
     *
     * @param receivableId 应收单ID
     * @return 已核销金额
     */
    BigDecimal getAppliedAmountByReceivableId(Long receivableId);

    /**
     * 获取收款单已核销金额
     *
     * @param receiptId 收款单ID
     * @return 已核销金额
     */
    BigDecimal getAppliedAmountByReceiptId(Long receiptId);

    /**
     * 收款单与应收单主单据级核销
     *
     * @param receiptId     收款单ID
     * @param receivableId  应收单ID
     * @param appliedAmount 核销金额
     * @param remark        备注
     * @return 是否核销成功
     */
    Boolean applyReceiptToReceivable(Long receiptId, Long receivableId,
                                     BigDecimal appliedAmount, String remark);

    /**
     * 撤销收款单与应收单核销
     *
     * @param linkId 核销关系ID
     * @param reason 撤销原因
     * @return 是否撤销成功
     */
    Boolean cancelReceiptReceivableLink(Long linkId, String reason);

    /**
     * 查询应收单的核销状态
     *
     * @param receivableId 应收单ID
     * @return 核销记录列表
     */
    List<FinArReceiptReceivableLinkVo> queryReceivableApplyStatus(Long receivableId);

    /**
     * 查询收款单的核销记录
     *
     * @param receiptId 收款单ID
     * @return 核销记录列表
     */
    List<FinArReceiptReceivableLinkVo> queryReceiptApplyRecords(Long receiptId);

    /**
     * 检查收款单是否存在核销记录
     *
     * @param receiptId 收款单ID
     * @return 是否存在核销记录
     */
    Boolean existsByReceiptId(Long receiptId);
}
