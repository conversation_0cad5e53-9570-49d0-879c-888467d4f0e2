package com.iotlaser.spms.pro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.pro.domain.Process;
import com.iotlaser.spms.pro.domain.bo.ProcessBo;
import com.iotlaser.spms.pro.domain.vo.ProcessVo;
import com.iotlaser.spms.pro.enums.ProcessCategory;
import com.iotlaser.spms.pro.mapper.ProcessMapper;
import com.iotlaser.spms.pro.service.IProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.PRO_PROCESS_CODE;


/**
 * 工序Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProcessServiceImpl implements IProcessService {

    private final ProcessMapper baseMapper;
    private final Gen gen;

    /**
     * 查询工序
     *
     * @param processId 主键
     * @return 工序
     */
    @Override
    public ProcessVo queryById(Long processId) {
        return baseMapper.selectVoById(processId);
    }

    /**
     * 分页查询工序列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工序分页列表
     */
    @Override
    public TableDataInfo<ProcessVo> queryPageList(ProcessBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Process> lqw = buildQueryWrapper(bo);
        Page<ProcessVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的工序列表
     *
     * @param bo 查询条件
     * @return 工序列表
     */
    @Override
    public List<ProcessVo> queryList(ProcessBo bo) {
        LambdaQueryWrapper<Process> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Process> buildQueryWrapper(ProcessBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Process> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Process::getProcessId);
        lqw.eq(StringUtils.isNotBlank(bo.getProcessCode()), Process::getProcessCode, bo.getProcessCode());
        lqw.like(StringUtils.isNotBlank(bo.getProcessName()), Process::getProcessName, bo.getProcessName());
        lqw.eq(StringUtils.isNotBlank(bo.getProcessCategory()), Process::getProcessCategory, bo.getProcessCategory());
        lqw.eq(bo.getStandardDuration() != null, Process::getStandardDuration, bo.getStandardDuration());
        lqw.eq(bo.getStandardPrice() != null, Process::getStandardPrice, bo.getStandardPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getCheckFlag()), Process::getCheckFlag, bo.getCheckFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getReportType()), Process::getReportType, bo.getReportType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Process::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增工序
     *
     * @param bo 工序
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(ProcessBo bo) {
        if (StringUtils.isEmpty(bo.getProcessCode())) {
            bo.setProcessCode(gen.code(PRO_PROCESS_CODE));
        }
        Process add = MapstructUtils.convert(bo, Process.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setProcessId(add.getProcessId());
            log.info("新增工序成功，工序编码：{}", add.getProcessCode());
        }
        return flag;
    }

    /**
     * 修改工序
     *
     * @param bo 工序
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(ProcessBo bo) {
        Process update = MapstructUtils.convert(bo, Process.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            log.info("修改工序成功，工序编码：{}", update.getProcessCode());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Process entity) {
        // 校验工序编码唯一性
        if (StringUtils.isNotBlank(entity.getProcessCode())) {
            LambdaQueryWrapper<Process> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Process::getProcessCode, entity.getProcessCode());
            if (entity.getProcessId() != null) {
                wrapper.ne(Process::getProcessId, entity.getProcessId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("工序编码已存在：" + entity.getProcessCode());
            }
        }

        // 校验工序类型（保留核心业务逻辑校验）
        if (StringUtils.isNotBlank(entity.getProcessCategory())) {
            ProcessCategory processType = ProcessCategory.getByCode(entity.getProcessCategory());
            if (processType == null) {
                throw new ServiceException("无效的工序类别：" + entity.getProcessCategory());
            }
        }

        // 校验必填字段
        if (StringUtils.isBlank(entity.getProcessName())) {
            throw new ServiceException("工序名称不能为空");
        }

        // ✅ 启用：完善格式校验，确保数据完整性
        // 工序名称校验
        if (StringUtils.isBlank(entity.getProcessName())) {
            throw new ServiceException("工序名称不能为空");
        }
        if (entity.getProcessName().length() > 100) {
            throw new ServiceException("工序名称长度不能超过100个字符");
        }

        // 工序描述校验
        // TODO: 在Process实体中添加getDescription()方法
        // if (StringUtils.isNotBlank(entity.getDescription()) && entity.getDescription().length() > 500) {
        //     throw new ServiceException("工序描述长度不能超过500个字符");
        // }
    }

    /**
     * 校验并批量删除工序信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验工序是否被工艺路线引用
            List<Process> processes = baseMapper.selectByIds(ids);
            for (Process process : processes) {
                // 检查是否被工艺路线、生产订单等引用
                log.info("删除工序，工序编码：{}", process.getProcessCode());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
