package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举（通用）
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Getter
@AllArgsConstructor
public enum OrderStatus {

    DRAFT("draft", "草稿", "订单正在编制中"),
    PENDING_APPROVAL("pending_approval", "待审核", "订单已提交，等待审核"),
    APPROVED("approved", "已审核", "订单已审核通过"),
    CONFIRMED("confirmed", "已确认", "订单已确认，等待执行"),
    IN_PROGRESS("in_progress", "执行中", "订单正在执行中"),
    PARTIALLY_COMPLETED("partially_completed", "部分完成", "订单部分完成"),
    COMPLETED("completed", "已完成", "订单已完成"),
    CANCELLED("cancelled", "已取消", "订单已取消"),
    CLOSED("closed", "已关闭", "订单已关闭");

    /**
     * 状态值
     */
    @EnumValue
    private final String value;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 订单状态枚举
     */
    public static OrderStatus getByValue(String value) {
        for (OrderStatus orderStatus : values()) {
            if (orderStatus.getValue().equals(value)) {
                return orderStatus;
            }
        }
        return null;
    }

    /**
     * 判断是否为可编辑状态
     *
     * @return 是否可编辑
     */
    public boolean isEditable() {
        return this == DRAFT || this == PENDING_APPROVAL;
    }

    /**
     * 判断是否为可删除状态
     *
     * @return 是否可删除
     */
    public boolean isDeletable() {
        return this == DRAFT;
    }

    /**
     * 判断是否为已完成状态
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return this == COMPLETED || this == CLOSED;
    }
}
