package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.ProductionReturnItemBatchBo;
import com.iotlaser.spms.mes.domain.vo.ProductionReturnItemBatchVo;
import com.iotlaser.spms.mes.service.IProductionReturnItemBatchService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产退料批次明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/productionReturnItemBatch")
public class ProductionReturnItemBatchController extends BaseController {

    private final IProductionReturnItemBatchService productionReturnItemBatchService;

    /**
     * 查询生产退料批次明细列表
     */
    @SaCheckPermission("mes:productionReturnItemBatch:list")
    @GetMapping("/list")
    public TableDataInfo<ProductionReturnItemBatchVo> list(ProductionReturnItemBatchBo bo, PageQuery pageQuery) {
        return productionReturnItemBatchService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产退料批次明细列表
     */
    @SaCheckPermission("mes:productionReturnItemBatch:export")
    @Log(title = "生产退料批次明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductionReturnItemBatchBo bo, HttpServletResponse response) {
        List<ProductionReturnItemBatchVo> list = productionReturnItemBatchService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产退料批次明细", ProductionReturnItemBatchVo.class, response);
    }

    /**
     * 获取生产退料批次明细详细信息
     *
     * @param batchId 主键
     */
    @SaCheckPermission("mes:productionReturnItemBatch:query")
    @GetMapping("/{batchId}")
    public R<ProductionReturnItemBatchVo> getInfo(@NotNull(message = "主键不能为空")
                                                  @PathVariable Long batchId) {
        return R.ok(productionReturnItemBatchService.queryById(batchId));
    }

    /**
     * 新增生产退料批次明细
     */
    @SaCheckPermission("mes:productionReturnItemBatch:add")
    @Log(title = "生产退料批次明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductionReturnItemBatchBo bo) {
        return toAjax(productionReturnItemBatchService.insertByBo(bo));
    }

    /**
     * 修改生产退料批次明细
     */
    @SaCheckPermission("mes:productionReturnItemBatch:edit")
    @Log(title = "生产退料批次明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductionReturnItemBatchBo bo) {
        return toAjax(productionReturnItemBatchService.updateByBo(bo));
    }

    /**
     * 删除生产退料批次明细
     *
     * @param batchIds 主键串
     */
    @SaCheckPermission("mes:productionReturnItemBatch:remove")
    @Log(title = "生产退料批次明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{batchIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] batchIds) {
        return toAjax(productionReturnItemBatchService.deleteWithValidByIds(List.of(batchIds), true));
    }
}
