package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinApPaymentOrderBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentOrderVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 付款单Service接口
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface IFinApPaymentOrderService {

    /**
     * 查询付款单
     *
     * @param paymentId 主键
     * @return 付款单
     */
    FinApPaymentOrderVo queryById(Long paymentId);

    /**
     * 分页查询付款单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 付款单分页列表
     */
    TableDataInfo<FinApPaymentOrderVo> queryPageList(FinApPaymentOrderBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的付款单列表
     *
     * @param bo 查询条件
     * @return 付款单列表
     */
    List<FinApPaymentOrderVo> queryList(FinApPaymentOrderBo bo);

    /**
     * 新增付款单
     *
     * @param bo 付款单
     * @return 是否新增成功
     */
    Boolean insertByBo(FinApPaymentOrderBo bo);

    /**
     * 修改付款单
     *
     * @param bo 付款单
     * @return 是否修改成功
     */
    Boolean updateByBo(FinApPaymentOrderBo bo);

    /**
     * 校验并批量删除付款单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 付款单核销到发票
     *
     * @param paymentOrderId 付款单ID
     * @param invoiceId      发票ID
     * @param writeoffAmount 核销金额
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 是否核销成功
     */
    Boolean applyToInvoice(Long paymentOrderId, Long invoiceId, BigDecimal writeoffAmount,
                           Long operatorId, String operatorName);

    /**
     * 批量核销付款单到发票
     *
     * @param writeoffItems 核销明细列表
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 批量核销结果
     */
    Map<String, Object> batchApplyToInvoices(List<WriteoffItem> writeoffItems,
                                             Long operatorId, String operatorName);

    /**
     * 撤销付款核销
     *
     * @param writeoffId   核销记录ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否撤销成功
     */
    Boolean cancelWriteoff(Long writeoffId, Long operatorId, String operatorName);

    /**
     * 查询付款单的核销记录
     *
     * @param paymentOrderId 付款单ID
     * @return 核销记录列表
     */
    List<Map<String, Object>> getWriteoffRecords(Long paymentOrderId);

    /**
     * 获取可核销的发票列表
     *
     * @param supplierId 供应商ID
     * @param amount     付款金额
     * @return 可核销的发票列表
     */
    List<FinApInvoiceVo> getWriteoffableInvoices(Long supplierId, BigDecimal amount);

    /**
     * 核销明细内部类
     */
    class WriteoffItem {
        private Long paymentOrderId;
        private Long invoiceId;
        private BigDecimal writeoffAmount;
        private String remark;

        // getters and setters
        public Long getPaymentOrderId() {
            return paymentOrderId;
        }

        public void setPaymentOrderId(Long paymentOrderId) {
            this.paymentOrderId = paymentOrderId;
        }

        public Long getInvoiceId() {
            return invoiceId;
        }

        public void setInvoiceId(Long invoiceId) {
            this.invoiceId = invoiceId;
        }

        public BigDecimal getWriteoffAmount() {
            return writeoffAmount;
        }

        public void setWriteoffAmount(BigDecimal writeoffAmount) {
            this.writeoffAmount = writeoffAmount;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
}
