package com.iotlaser.spms.mes.service;

import java.util.List;
import java.util.Map;

/**
 * 工序流转自动化服务接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/06/21
 */
public interface IWorkflowAutomationService {

    /**
     * 自动流转到下一工序
     *
     * @param instanceId    产品实例ID
     * @param currentStepId 当前工序ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 流转结果
     */
    Map<String, Object> autoFlowToNextStep(Long instanceId, Long currentStepId,
                                           Long operatorId, String operatorName);

    /**
     * 检查工序流转条件
     *
     * @param instanceId 产品实例ID
     * @param stepId     工序ID
     * @return 条件检查结果
     */
    Map<String, Object> checkStepFlowConditions(Long instanceId, Long stepId);

    /**
     * 获取下一可执行工序
     *
     * @param instanceId    产品实例ID
     * @param currentStepId 当前工序ID
     * @return 下一工序列表
     */
    List<WorkflowStep> getNextAvailableSteps(Long instanceId, Long currentStepId);

    /**
     * 批量工序流转
     *
     * @param flowRequests 流转请求列表
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 批量流转结果
     */
    Map<String, Object> batchFlowSteps(List<StepFlowRequest> flowRequests,
                                       Long operatorId, String operatorName);

    /**
     * 工序回退
     *
     * @param instanceId     产品实例ID
     * @param targetStepId   目标工序ID
     * @param rollbackReason 回退原因
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 是否回退成功
     */
    Boolean rollbackToStep(Long instanceId, Long targetStepId, String rollbackReason,
                           Long operatorId, String operatorName);

    /**
     * 获取工序流转历史
     *
     * @param instanceId 产品实例ID
     * @return 流转历史列表
     */
    List<Map<String, Object>> getStepFlowHistory(Long instanceId);

    /**
     * 设置工序自动流转规则
     *
     * @param stepId       工序ID
     * @param autoFlowRule 自动流转规则
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否设置成功
     */
    Boolean setAutoFlowRule(Long stepId, AutoFlowRule autoFlowRule,
                            Long operatorId, String operatorName);

    /**
     * 工序步骤内部类
     */
    class WorkflowStep {
        private Long stepId;
        private String stepCode;
        private String stepName;
        private String stepType;
        private Integer sequence;
        private String status;
        private boolean canExecute;
        private List<String> conditions;

        // getters and setters
        public Long getStepId() {
            return stepId;
        }

        public void setStepId(Long stepId) {
            this.stepId = stepId;
        }

        public String getStepCode() {
            return stepCode;
        }

        public void setStepCode(String stepCode) {
            this.stepCode = stepCode;
        }

        public String getStepName() {
            return stepName;
        }

        public void setStepName(String stepName) {
            this.stepName = stepName;
        }

        public String getStepType() {
            return stepType;
        }

        public void setStepType(String stepType) {
            this.stepType = stepType;
        }

        public Integer getSequence() {
            return sequence;
        }

        public void setSequence(Integer sequence) {
            this.sequence = sequence;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public boolean isCanExecute() {
            return canExecute;
        }

        public void setCanExecute(boolean canExecute) {
            this.canExecute = canExecute;
        }

        public List<String> getConditions() {
            return conditions;
        }

        public void setConditions(List<String> conditions) {
            this.conditions = conditions;
        }
    }

    /**
     * 工序流转请求内部类
     */
    class StepFlowRequest {
        private Long instanceId;
        private Long currentStepId;
        private Long targetStepId;
        private String remark;

        // getters and setters
        public Long getInstanceId() {
            return instanceId;
        }

        public void setInstanceId(Long instanceId) {
            this.instanceId = instanceId;
        }

        public Long getCurrentStepId() {
            return currentStepId;
        }

        public void setCurrentStepId(Long currentStepId) {
            this.currentStepId = currentStepId;
        }

        public Long getTargetStepId() {
            return targetStepId;
        }

        public void setTargetStepId(Long targetStepId) {
            this.targetStepId = targetStepId;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }

    /**
     * 自动流转规则内部类
     */
    class AutoFlowRule {
        private String triggerCondition;
        private String targetStepId;
        private boolean enabled;
        private String description;

        // getters and setters
        public String getTriggerCondition() {
            return triggerCondition;
        }

        public void setTriggerCondition(String triggerCondition) {
            this.triggerCondition = triggerCondition;
        }

        public String getTargetStepId() {
            return targetStepId;
        }

        public void setTargetStepId(String targetStepId) {
            this.targetStepId = targetStepId;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }
}
