package com.iotlaser.spms.mes.service;

import java.util.List;
import java.util.Map;

/**
 * 工艺路线集成Service接口
 * 高优先级功能：工艺路线集成
 *
 * <AUTHOR> <PERSON>
 * @date 2025/06/16
 */
public interface IRoutingIntegrationService {

    /**
     * 获取下一工序信息
     *
     * @param routingId     工艺路线ID
     * @param currentStepId 当前工序ID
     * @return 下一工序信息
     */
    Map<String, Object> getNextRoutingStep(Long routingId, Long currentStepId);

    /**
     * 校验工序流转规则
     *
     * @param instanceCode 产品实例编码
     * @param fromStepId   源工序ID
     * @param toStepId     目标工序ID
     * @return 是否允许流转
     */
    Boolean validateStepTransition(String instanceCode, Long fromStepId, Long toStepId);

    /**
     * 获取并行工序信息
     *
     * @param routingId      工艺路线ID
     * @param sequenceNumber 工序序号
     * @return 并行工序列表
     */
    List<Map<String, Object>> getParallelSteps(Long routingId, Integer sequenceNumber);

    /**
     * 自动流转到下一工序
     *
     * @param instanceCode  产品实例编码
     * @param currentStepId 当前工序ID
     * @return 流转结果
     */
    Map<String, Object> autoTransitionToNextStep(String instanceCode, Long currentStepId);

    /**
     * 获取工序流转历史
     *
     * @param instanceCode 产品实例编码
     * @return 流转历史记录
     */
    List<Map<String, Object>> getStepTransitionHistory(String instanceCode);

    /**
     * 检查工序执行条件
     *
     * @param instanceCode 产品实例编码
     * @param stepId       工序ID
     * @return 检查结果
     */
    Map<String, Object> checkStepExecutionConditions(String instanceCode, Long stepId);
}
