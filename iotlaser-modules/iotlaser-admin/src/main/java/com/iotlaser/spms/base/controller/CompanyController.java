package com.iotlaser.spms.base.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.base.domain.bo.CompanyBo;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公司信息
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/base/company")
public class CompanyController extends BaseController {

    private final ICompanyService baseCompanyService;

    /**
     * 查询公司信息列表
     */
    @SaCheckPermission("base:company:list")
    @GetMapping("/list")
    public TableDataInfo<CompanyVo> list(CompanyBo bo, PageQuery pageQuery) {
        return baseCompanyService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出公司信息列表
     */
    @SaCheckPermission("base:company:export")
    @Log(title = "公司信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CompanyBo bo, HttpServletResponse response) {
        List<CompanyVo> list = baseCompanyService.queryList(bo);
        ExcelUtil.exportExcel(list, "公司信息", CompanyVo.class, response);
    }

    /**
     * 获取公司信息详细信息
     *
     * @param companyId 主键
     */
    @SaCheckPermission("base:company:query")
    @GetMapping("/{companyId}")
    public R<CompanyVo> getInfo(@NotNull(message = "主键不能为空")
                                @PathVariable Long companyId) {
        return R.ok(baseCompanyService.queryById(companyId));
    }

    /**
     * 新增公司信息
     */
    @SaCheckPermission("base:company:add")
    @Log(title = "公司信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CompanyBo bo) {
        return toAjax(baseCompanyService.insertByBo(bo));
    }

    /**
     * 修改公司信息
     */
    @SaCheckPermission("base:company:edit")
    @Log(title = "公司信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CompanyBo bo) {
        return toAjax(baseCompanyService.updateByBo(bo));
    }

    /**
     * 删除公司信息
     *
     * @param companyIds 主键串
     */
    @SaCheckPermission("base:company:remove")
    @Log(title = "公司信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{companyIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] companyIds) {
        return toAjax(baseCompanyService.deleteWithValidByIds(List.of(companyIds), true));
    }
}
