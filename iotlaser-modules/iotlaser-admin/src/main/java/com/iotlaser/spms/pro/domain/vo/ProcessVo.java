package com.iotlaser.spms.pro.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.pro.domain.Process;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;


/**
 * 工序视图对象 pro_process
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Process.class)
public class ProcessVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 工序ID
     */
    @ExcelProperty(value = "工序ID")
    private Long processId;

    /**
     * 工序编码
     */
    @ExcelProperty(value = "工序编码")
    private String processCode;

    /**
     * 工序名称
     */
    @ExcelProperty(value = "工序名称")
    private String processName;

    /**
     * 工序类别
     */
    @ExcelProperty(value = "工序类别")
    private String processCategory;

    /**
     * 标准工时(分钟)
     */
    @ExcelProperty(value = "标准工时(分钟) ")
    private Long standardDuration;

    /**
     * 标准计件单价
     */
    @ExcelProperty(value = "标准计件单价")
    private Long standardPrice;

    /**
     * 是否质检
     */
    @ExcelProperty(value = "是否质检")
    private String checkFlag;

    /**
     * 报工类型
     */
    @ExcelProperty(value = "报工类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "pro_process_report_type")
    private String reportType;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
