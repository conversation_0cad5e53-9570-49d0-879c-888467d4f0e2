package com.iotlaser.spms.pro.domain.bo;

import com.iotlaser.spms.pro.domain.RoutingStep;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 工艺路线工序业务对象 pro_routing_step
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = RoutingStep.class, reverseConvertGenerate = false)
public class RoutingStepBo extends BaseEntity {

    /**
     * 步骤ID
     */
    @NotNull(message = "步骤ID不能为空", groups = {EditGroup.class})
    private Long stepId;

    /**
     * 路线ID
     */
    @NotNull(message = "路线ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long routingId;

    /**
     * 工序ID
     */
    @NotNull(message = "工序ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long processId;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 下一步骤ID
     */
    private Long nextStepId;

    /**
     * 返工步骤ID
     */
    private Long reworkStepId;

    /**
     * 准备时间(分钟)
     */
    private Long setupTime;

    /**
     * 加工时间(分钟)
     */
    private Long processingTime;

    /**
     * 质检要求
     */
    private String qualityCheckSpecs;

    /**
     * 工作重心
     */
    private String reportType;

    /**
     * 工序顺序
     */
    private Long orderNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
