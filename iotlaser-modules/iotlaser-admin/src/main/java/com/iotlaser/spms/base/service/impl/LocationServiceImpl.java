package com.iotlaser.spms.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.iotlaser.spms.base.domain.Location;
import com.iotlaser.spms.base.domain.bo.LocationBo;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.mapper.LocationMapper;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.base.strategy.Gen;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.iotlaser.spms.base.enums.GenCodeType.BASE_LOCATION_CODE;

/**
 * 位置库位Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LocationServiceImpl implements ILocationService {

    private final LocationMapper baseMapper;
    private final Gen gen;

    /**
     * 查询位置库位
     *
     * @param locationId 主键
     * @return 位置库位
     */
    @Override
    public LocationVo queryById(Long locationId) {
        return baseMapper.selectVoById(locationId);
    }

    /**
     * 查询符合条件的位置库位列表
     *
     * @param bo 查询条件
     * @return 位置库位列表
     */
    @Override
    public List<LocationVo> queryList(LocationBo bo) {
        LambdaQueryWrapper<Location> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Location> buildQueryWrapper(LocationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Location> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Location::getLocationId);
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), Location::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), Location::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationType()), Location::getLocationType, bo.getLocationType());
        lqw.eq(bo.getParentId() != null, Location::getParentId, bo.getParentId());
        // ✅ 优化：移除排序号的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getOrderNum() != null, Location::getOrderNum, bo.getOrderNum());
        // TODO: 如需要可以后续添加排序号的范围查询支持
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Location::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增位置库位
     *
     * @param bo 位置库位
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(LocationBo bo) {
        try {
            if (StringUtils.isEmpty(bo.getLocationCode())) {
                bo.setLocationCode(gen.code(BASE_LOCATION_CODE));
            }
            Location add = MapstructUtils.convert(bo, Location.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增位置库位失败");
            }

            bo.setLocationId(add.getLocationId());
            log.info("新增位置库位成功：{}", add.getLocationName());
            return true;
        } catch (Exception e) {
            log.error("新增位置库位失败：{}", e.getMessage(), e);
            throw new ServiceException("新增位置库位失败：" + e.getMessage());
        }
    }

    /**
     * 修改位置库位
     *
     * @param bo 位置库位
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(LocationBo bo) {
        try {
            Location update = MapstructUtils.convert(bo, Location.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改位置库位失败：位置不存在或数据未变更");
            }

            log.info("修改位置库位成功：{}", update.getLocationName());
            return true;
        } catch (Exception e) {
            log.error("修改位置库位失败：{}", e.getMessage(), e);
            throw new ServiceException("修改位置库位失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Location entity) {
        // 校验位置编码唯一性
        if (StringUtils.isNotBlank(entity.getLocationCode())) {
            LambdaQueryWrapper<Location> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Location::getLocationCode, entity.getLocationCode());
            if (entity.getLocationId() != null) {
                wrapper.ne(Location::getLocationId, entity.getLocationId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("位置编码已存在：" + entity.getLocationCode());
            }
        }

        // 校验位置名称唯一性（同级下）
        if (StringUtils.isNotBlank(entity.getLocationName())) {
            LambdaQueryWrapper<Location> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Location::getLocationName, entity.getLocationName());
            wrapper.eq(Location::getParentId, entity.getParentId());
            if (entity.getLocationId() != null) {
                wrapper.ne(Location::getLocationId, entity.getLocationId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同级位置名称已存在：" + entity.getLocationName());
            }
        }

        // 校验必填字段
        if (StringUtils.isBlank(entity.getLocationName())) {
            throw new ServiceException("位置名称不能为空");
        }
        if (StringUtils.isBlank(entity.getLocationType())) {
            throw new ServiceException("位置类型不能为空");
        }

        // TODO: 暂时注释掉格式校验，只保留核心业务逻辑校验
        // 其他格式校验（如名称非空、长度限制等）已注释
    }

    /**
     * 校验并批量删除位置库位信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验位置是否有子位置
            List<Location> locations = baseMapper.selectByIds(ids);
            for (Location location : locations) {
                // 检查是否有子位置
                LambdaQueryWrapper<Location> wrapper = Wrappers.lambdaQuery();
                wrapper.eq(Location::getParentId, location.getLocationId());
                if (baseMapper.exists(wrapper)) {
                    throw new ServiceException("位置【" + location.getLocationName() + "】存在子位置，不能删除");
                }
                // 检查是否有关联的库存记录
                log.info("删除库位校验：{}", location.getLocationName());
                // 例如：检查是否有库存、是否有业务单据关联等
                log.info("删除位置校验：{}", location.getLocationName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除位置库位成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除位置库位失败：{}", e.getMessage(), e);
            throw new ServiceException("删除位置库位失败：" + e.getMessage());
        }
    }

    /**
     * 获取位置树结构
     *
     * @param warehouseCode 仓库编码（可选）
     * @return 位置树列表
     */
    @Override
    public List<LocationVo> getLocationTree(String warehouseCode) {
        try {
            LambdaQueryWrapper<Location> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Location::getStatus, "1"); // 只查询启用的位置
            if (StringUtils.isNotBlank(warehouseCode)) {
                // 如果有仓库编码，可以根据仓库过滤（这里假设位置编码包含仓库信息）
                wrapper.like(Location::getLocationCode, warehouseCode);
            }
            wrapper.orderByAsc(Location::getParentId, Location::getOrderNum);

            List<LocationVo> allLocations = baseMapper.selectVoList(wrapper);

            // 构建树结构
            List<LocationVo> result = buildLocationTree(allLocations, null);
            log.debug("获取位置树成功，仓库：{}，节点数：{}", warehouseCode, result.size());
            return result;
        } catch (Exception e) {
            log.error("获取位置树失败，仓库：{}，错误：{}", warehouseCode, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建位置树结构
     */
    private List<LocationVo> buildLocationTree(List<LocationVo> allLocations, Long parentId) {
        return allLocations.stream()
            .filter(location -> {
                if (parentId == null) {
                    return location.getParentId() == null || location.getParentId().equals(0L);
                } else {
                    return parentId.equals(location.getParentId());
                }
            })
            .peek(location -> {
                List<LocationVo> children = buildLocationTree(allLocations, location.getLocationId());
                // 这里需要在LocationVo中添加children字段，暂时用注释表示
                // location.setChildren(children);
            })
            .collect(Collectors.toList());
    }

    /**
     * 获取子位置列表
     *
     * @param parentId 父位置ID
     * @return 子位置列表
     */
    @Override
    public List<LocationVo> getSubLocations(Long parentId) {
        try {
            LambdaQueryWrapper<Location> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Location::getParentId, parentId);
            wrapper.eq(Location::getStatus, "1");
            wrapper.orderByAsc(Location::getOrderNum);

            List<LocationVo> result = baseMapper.selectVoList(wrapper);
            log.debug("获取子位置成功，父位置ID：{}，数量：{}", parentId, result.size());
            return result;
        } catch (Exception e) {
            log.error("获取子位置失败，父位置ID：{}，错误：{}", parentId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 更新位置状态
     *
     * @param locationId 位置ID
     * @param status     状态（1-启用，0-禁用）
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateLocationStatus(Long locationId, String status) {
        try {
            if (locationId == null) {
                throw new ServiceException("位置ID不能为空");
            }
            if (StringUtils.isBlank(status)) {
                throw new ServiceException("状态不能为空");
            }
            if (!"0".equals(status) && !"1".equals(status)) {
                throw new ServiceException("状态值无效，只能是0或1");
            }

            // 检查位置是否存在
            Location location = baseMapper.selectById(locationId);
            if (location == null) {
                throw new ServiceException("位置不存在");
            }

            // 更新状态
            Location updateEntity = new Location();
            updateEntity.setLocationId(locationId);
            updateEntity.setStatus(status);

            int result = baseMapper.updateById(updateEntity);
            if (result <= 0) {
                throw new ServiceException("更新位置状态失败");
            }

            String statusText = "1".equals(status) ? "启用" : "禁用";
            log.info("更新位置状态成功：{} - {}", location.getLocationName(), statusText);
            return true;
        } catch (Exception e) {
            log.error("更新位置状态失败：{}", e.getMessage(), e);
            throw new ServiceException("更新位置状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用位置列表
     *
     * @param warehouseCode    仓库编码
     * @param requiredCapacity 所需容量（可选）
     * @return 可用位置列表
     */
    @Override
    public List<LocationVo> getAvailableLocations(String warehouseCode, BigDecimal requiredCapacity) {
        try {
            LambdaQueryWrapper<Location> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Location::getStatus, "1"); // 启用状态
            if (StringUtils.isNotBlank(warehouseCode)) {
                wrapper.like(Location::getLocationCode, warehouseCode);
            }
            // TODO: 这里需要根据实际的容量字段进行过滤
            // if (requiredCapacity != null) {
            //     wrapper.ge(Location::getAvailableCapacity, requiredCapacity);
            // }
            wrapper.orderByAsc(Location::getLocationCode);

            List<LocationVo> result = baseMapper.selectVoList(wrapper);
            log.debug("获取可用位置成功，仓库：{}，所需容量：{}，数量：{}", warehouseCode, requiredCapacity, result.size());
            return result;
        } catch (Exception e) {
            log.error("获取可用位置失败，仓库：{}，错误：{}", warehouseCode, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据位置类型获取位置列表
     *
     * @param locationType 位置类型
     * @return 位置列表
     */
    @Override
    public List<LocationVo> getLocationsByType(String locationType) {
        try {
            if (StringUtils.isBlank(locationType)) {
                return new ArrayList<>();
            }

            LambdaQueryWrapper<Location> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Location::getLocationType, locationType);
            wrapper.eq(Location::getStatus, "1");
            wrapper.orderByAsc(Location::getLocationCode);

            List<LocationVo> result = baseMapper.selectVoList(wrapper);
            log.debug("根据类型获取位置成功，类型：{}，数量：{}", locationType, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据类型获取位置失败，类型：{}，错误：{}", locationType, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据位置编码查询位置信息
     *
     * @param locationCode 位置编码
     * @return 位置信息
     */
    @Override
    public LocationVo getByLocationCode(String locationCode) {
        try {
            if (StringUtils.isBlank(locationCode)) {
                return null;
            }

            LambdaQueryWrapper<Location> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Location::getLocationCode, locationCode);
            wrapper.eq(Location::getStatus, "1");

            LocationVo result = baseMapper.selectVoOne(wrapper);
            log.debug("根据编码查询位置：{}", locationCode);
            return result;
        } catch (Exception e) {
            log.error("根据编码查询位置失败，编码：{}，错误：{}", locationCode, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 设置位置维护状态
     *
     * @param locationId 位置ID
     * @param reason     维护原因
     * @return 是否设置成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setLocationMaintenance(Long locationId, String reason) {
        try {
            if (locationId == null) {
                throw new ServiceException("位置ID不能为空");
            }

            // 检查位置是否存在
            Location location = baseMapper.selectById(locationId);
            if (location == null) {
                throw new ServiceException("位置不存在");
            }

            // 更新为维护状态
            Location updateEntity = new Location();
            updateEntity.setLocationId(locationId);
            updateEntity.setStatus("2"); // 假设2表示维护状态
            if (StringUtils.isNotBlank(reason)) {
                updateEntity.setRemark("维护原因：" + reason);
            }

            int result = baseMapper.updateById(updateEntity);
            if (result <= 0) {
                throw new ServiceException("设置位置维护状态失败");
            }

            log.info("设置位置维护状态成功：{} - 原因：{}", location.getLocationName(), reason);
            return true;
        } catch (Exception e) {
            log.error("设置位置维护状态失败：{}", e.getMessage(), e);
            throw new ServiceException("设置位置维护状态失败：" + e.getMessage());
        }
    }
}
