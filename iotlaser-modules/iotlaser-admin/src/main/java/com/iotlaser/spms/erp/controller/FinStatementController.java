package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinStatementBo;
import com.iotlaser.spms.erp.domain.vo.FinStatementVo;
import com.iotlaser.spms.erp.service.IFinStatementService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 对账单
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finStatement")
public class FinStatementController extends BaseController {

    private final IFinStatementService finStatementService;

    /**
     * 查询对账单列表
     */
    @SaCheckPermission("erp:finStatement:list")
    @GetMapping("/list")
    public TableDataInfo<FinStatementVo> list(FinStatementBo bo, PageQuery pageQuery) {
        return finStatementService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出对账单列表
     */
    @SaCheckPermission("erp:finStatement:export")
    @Log(title = "对账单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinStatementBo bo, HttpServletResponse response) {
        List<FinStatementVo> list = finStatementService.queryList(bo);
        ExcelUtil.exportExcel(list, "对账单", FinStatementVo.class, response);
    }

    /**
     * 获取对账单详细信息
     *
     * @param statementId 主键
     */
    @SaCheckPermission("erp:finStatement:query")
    @GetMapping("/{statementId}")
    public R<FinStatementVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long statementId) {
        return R.ok(finStatementService.queryById(statementId));
    }

    /**
     * 新增对账单
     */
    @SaCheckPermission("erp:finStatement:add")
    @Log(title = "对账单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinStatementBo bo) {
        return toAjax(finStatementService.insertByBo(bo));
    }

    /**
     * 修改对账单
     */
    @SaCheckPermission("erp:finStatement:edit")
    @Log(title = "对账单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinStatementBo bo) {
        return toAjax(finStatementService.updateByBo(bo));
    }

    /**
     * 删除对账单
     *
     * @param statementIds 主键串
     */
    @SaCheckPermission("erp:finStatement:remove")
    @Log(title = "对账单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{statementIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] statementIds) {
        return toAjax(finStatementService.deleteWithValidByIds(List.of(statementIds), true));
    }
}
