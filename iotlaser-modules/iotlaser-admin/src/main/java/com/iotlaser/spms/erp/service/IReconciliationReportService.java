package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.vo.ReconciliationReportVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 对账报表服务接口
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
public interface IReconciliationReportService {

    /**
     * 生成汇总对账报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 汇总报表
     */
    ReconciliationReportVo generateSummaryReport(LocalDate startDate, LocalDate endDate);

    /**
     * 生成明细对账报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 明细报表
     */
    ReconciliationReportVo generateDetailReport(LocalDate startDate, LocalDate endDate);

    /**
     * 生成差异对账报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 差异报表
     */
    ReconciliationReportVo generateDifferenceReport(LocalDate startDate, LocalDate endDate);

    /**
     * 生成客户对账报表
     *
     * @param customerId 客户ID（可选，为空时生成所有客户）
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return 客户对账报表
     */
    ReconciliationReportVo generateCustomerReport(Long customerId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取对账异常提醒
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 异常提醒列表
     */
    List<ReconciliationReportVo.ReconciliationAlert> getReconciliationAlerts(LocalDate startDate, LocalDate endDate);

    /**
     * 检查大额差异提醒
     *
     * @param threshold 差异金额阈值
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 大额差异提醒列表
     */
    List<ReconciliationReportVo.ReconciliationAlert> checkLargeDifferenceAlerts(
        java.math.BigDecimal threshold, LocalDate startDate, LocalDate endDate);

    /**
     * 检查长期逾期提醒
     *
     * @param overdueDays 逾期天数阈值
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return 长期逾期提醒列表
     */
    List<ReconciliationReportVo.ReconciliationAlert> checkOverdueAlerts(
        Integer overdueDays, LocalDate startDate, LocalDate endDate);

    /**
     * 检查缺失发票提醒
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 缺失发票提醒列表
     */
    List<ReconciliationReportVo.ReconciliationAlert> checkMissingInvoiceAlerts(LocalDate startDate, LocalDate endDate);

    /**
     * 导出对账报表到Excel
     *
     * @param reportType 报表类型
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return Excel文件字节数组
     */
    byte[] exportReportToExcel(ReconciliationReportVo.ReportType reportType, LocalDate startDate, LocalDate endDate);

    /**
     * 发送对账异常邮件提醒
     *
     * @param alerts     异常提醒列表
     * @param recipients 收件人邮箱列表
     * @return 是否发送成功
     */
    Boolean sendAlertEmail(List<ReconciliationReportVo.ReconciliationAlert> alerts, List<String> recipients);

    /**
     * 获取对账KPI指标
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return KPI指标
     */
    ReconciliationKPI getReconciliationKPI(LocalDate startDate, LocalDate endDate);

    /**
     * 对账KPI指标
     */
    class ReconciliationKPI {
        /**
         * 对账及时率
         */
        private java.math.BigDecimal timelyRate;

        /**
         * 对账准确率
         */
        private java.math.BigDecimal accuracyRate;

        /**
         * 平均对账周期（天）
         */
        private java.math.BigDecimal averageCycleDays;

        /**
         * 差异处理及时率
         */
        private java.math.BigDecimal differenceResolveRate;

        /**
         * 收款及时率
         */
        private java.math.BigDecimal receivedTimelyRate;

        /**
         * 开票及时率
         */
        private java.math.BigDecimal invoiceTimelyRate;

        // Getters and Setters
        public java.math.BigDecimal getTimelyRate() {
            return timelyRate;
        }

        public void setTimelyRate(java.math.BigDecimal timelyRate) {
            this.timelyRate = timelyRate;
        }

        public java.math.BigDecimal getAccuracyRate() {
            return accuracyRate;
        }

        public void setAccuracyRate(java.math.BigDecimal accuracyRate) {
            this.accuracyRate = accuracyRate;
        }

        public java.math.BigDecimal getAverageCycleDays() {
            return averageCycleDays;
        }

        public void setAverageCycleDays(java.math.BigDecimal averageCycleDays) {
            this.averageCycleDays = averageCycleDays;
        }

        public java.math.BigDecimal getDifferenceResolveRate() {
            return differenceResolveRate;
        }

        public void setDifferenceResolveRate(java.math.BigDecimal differenceResolveRate) {
            this.differenceResolveRate = differenceResolveRate;
        }

        public java.math.BigDecimal getReceivedTimelyRate() {
            return receivedTimelyRate;
        }

        public void setReceivedTimelyRate(java.math.BigDecimal receivedTimelyRate) {
            this.receivedTimelyRate = receivedTimelyRate;
        }

        public java.math.BigDecimal getInvoiceTimelyRate() {
            return invoiceTimelyRate;
        }

        public void setInvoiceTimelyRate(java.math.BigDecimal invoiceTimelyRate) {
            this.invoiceTimelyRate = invoiceTimelyRate;
        }
    }
}
