package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 账户对象 erp_fin_account
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_account")
public class FinAccount extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账户ID
     */
    @TableId(value = "account_id")
    private Long accountId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 银行账号/支付账号
     */
    private String accountNumber;

    /**
     * 币种
     */
    private String currency;

    /**
     * 期初余额
     */
    private BigDecimal initialBalance;

    /**
     * 当前余额(由流水实时更新)
     */
    private BigDecimal currentBalance;

    /**
     * 账户状态
     */
    private String accountStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
