package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.mes.domain.ProductionInboundItemBatch;
import com.iotlaser.spms.mes.domain.bo.ProductionInboundItemBatchBo;
import com.iotlaser.spms.mes.domain.vo.ProductionInboundItemBatchVo;
import com.iotlaser.spms.mes.mapper.ProductionInboundItemBatchMapper;
import com.iotlaser.spms.mes.service.IProductionInboundItemBatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 生产入库批次明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductionInboundItemBatchServiceImpl implements IProductionInboundItemBatchService {

    private final ProductionInboundItemBatchMapper baseMapper;

    /**
     * 查询生产入库批次明细
     *
     * @param batchId 主键
     * @return 生产入库批次明细
     */
    @Override
    public ProductionInboundItemBatchVo queryById(Long batchId) {
        return baseMapper.selectVoById(batchId);
    }

    /**
     * 分页查询生产入库批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产入库批次明细分页列表
     */
    @Override
    public TableDataInfo<ProductionInboundItemBatchVo> queryPageList(ProductionInboundItemBatchBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductionInboundItemBatch> lqw = buildQueryWrapper(bo);
        Page<ProductionInboundItemBatchVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产入库批次明细列表
     *
     * @param bo 查询条件
     * @return 生产入库批次明细列表
     */
    @Override
    public List<ProductionInboundItemBatchVo> queryList(ProductionInboundItemBatchBo bo) {
        LambdaQueryWrapper<ProductionInboundItemBatch> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductionInboundItemBatch> buildQueryWrapper(ProductionInboundItemBatchBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductionInboundItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductionInboundItemBatch::getBatchId);
        lqw.eq(bo.getItemId() != null, ProductionInboundItemBatch::getItemId, bo.getItemId());
        lqw.eq(bo.getReturnId() != null, ProductionInboundItemBatch::getReturnId, bo.getReturnId());
        lqw.eq(bo.getInventoryBatchId() != null, ProductionInboundItemBatch::getInventoryBatchId, bo.getInventoryBatchId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), ProductionInboundItemBatch::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), ProductionInboundItemBatch::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), ProductionInboundItemBatch::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getProductId() != null, ProductionInboundItemBatch::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductionInboundItemBatch::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), ProductionInboundItemBatch::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, ProductionInboundItemBatch::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), ProductionInboundItemBatch::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), ProductionInboundItemBatch::getUnitName, bo.getUnitName());

        // ✅ 优化：移除数量和价格的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, ProductionInboundItemBatch::getQuantity, bo.getQuantity());
        // 原代码：lqw.eq(bo.getPrice() != null, ProductionInboundItemBatch::getPrice, bo.getPrice());
        // TODO: 如需要可以后续添加数量和价格的范围查询支持

        lqw.eq(bo.getLocationId() != null, ProductionInboundItemBatch::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), ProductionInboundItemBatch::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), ProductionInboundItemBatch::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductionInboundItemBatch::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增生产入库批次明细
     *
     * @param bo 生产入库批次明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProductionInboundItemBatchBo bo) {
        ProductionInboundItemBatch add = MapstructUtils.convert(bo, ProductionInboundItemBatch.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBatchId(add.getBatchId());
        }
        return flag;
    }

    /**
     * 修改生产入库批次明细
     *
     * @param bo 生产入库批次明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProductionInboundItemBatchBo bo) {
        ProductionInboundItemBatch update = MapstructUtils.convert(bo, ProductionInboundItemBatch.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductionInboundItemBatch entity) {
        // 校验必填字段
        if (entity.getItemId() == null) {
            throw new ServiceException("入库明细不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("入库数量必须大于0");
        }
        if (StringUtils.isBlank(entity.getInternalBatchNumber())) {
            throw new ServiceException("内部批次号不能为空");
        }
        if (entity.getLocationId() == null) {
            throw new ServiceException("库位不能为空");
        }

        // 校验批次号唯一性
        if (StringUtils.isNotBlank(entity.getInternalBatchNumber())) {
            LambdaQueryWrapper<ProductionInboundItemBatch> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ProductionInboundItemBatch::getInternalBatchNumber, entity.getInternalBatchNumber());
            if (entity.getBatchId() != null) {
                wrapper.ne(ProductionInboundItemBatch::getBatchId, entity.getBatchId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("内部批次号已存在：" + entity.getInternalBatchNumber());
            }
        }
    }

    /**
     * 校验并批量删除生产入库批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验批次明细是否可以删除
            List<ProductionInboundItemBatch> batches = baseMapper.selectByIds(ids);
            for (ProductionInboundItemBatch batch : batches) {
                log.info("删除生产入库批次明细，批次号：{}", batch.getInternalBatchNumber());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
