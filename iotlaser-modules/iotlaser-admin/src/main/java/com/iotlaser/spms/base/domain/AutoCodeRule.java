package com.iotlaser.spms.base.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 编码生成规则对象 base_auto_code_rule
 *
 * <AUTHOR> <PERSON>
 * @date 2025/03/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("base_auto_code_rule")
public class AutoCodeRule extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 规则ID
     */
    @TableId(value = "rule_id")
    private Long ruleId;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 描述
     */
    private String ruleDesc;

    /**
     * 最大长度
     */
    private Integer maxLength;

    /**
     * 是否补齐
     */
    private String isPadded;

    /**
     * 补齐字符
     */
    private String paddedChar;

    /**
     * 补齐方式
     */
    private String paddedMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 （0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0存在 1删除）
     */
    @TableLogic
    private String delFlag;

}
