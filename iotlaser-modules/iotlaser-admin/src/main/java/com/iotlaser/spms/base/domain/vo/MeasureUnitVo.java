package com.iotlaser.spms.base.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.base.domain.MeasureUnit;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 计量单位视图对象 base_measure_unit
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MeasureUnit.class)
public class MeasureUnitVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 单位ID
     */
    @ExcelProperty(value = "单位ID")
    private Long unitId;

    /**
     * 上级节点
     */
    @ExcelProperty(value = "上级节点")
    private Long parentId;

    /**
     * 单位编码
     */
    @ExcelProperty(value = "单位编码")
    private String unitCode;

    /**
     * 单位名称
     */
    @ExcelProperty(value = "单位名称")
    private String unitName;

    /**
     * 换算比例
     */
    @ExcelProperty(value = "换算比例")
    private BigDecimal unitRatio;

    /**
     * 是否主单位
     */
    @ExcelProperty(value = "是否主单位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String primaryFlag;

    /**
     * 排列顺序
     */
    @ExcelProperty(value = "排列顺序")
    private Long orderNum;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

}
