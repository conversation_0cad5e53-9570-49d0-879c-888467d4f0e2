package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinStatementItemBo;
import com.iotlaser.spms.erp.domain.vo.FinStatementItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 对账单明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
public interface IFinStatementItemService {

    /**
     * 查询对账单明细
     *
     * @param itemId 主键
     * @return 对账单明细
     */
    FinStatementItemVo queryById(Long itemId);

    /**
     * 分页查询对账单明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 对账单明细分页列表
     */
    TableDataInfo<FinStatementItemVo> queryPageList(FinStatementItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的对账单明细列表
     *
     * @param bo 查询条件
     * @return 对账单明细列表
     */
    List<FinStatementItemVo> queryList(FinStatementItemBo bo);

    /**
     * 新增对账单明细
     *
     * @param bo 对账单明细
     * @return 是否新增成功
     */
    Boolean insertByBo(FinStatementItemBo bo);

    /**
     * 修改对账单明细
     *
     * @param bo 对账单明细
     * @return 是否修改成功
     */
    Boolean updateByBo(FinStatementItemBo bo);

    /**
     * 校验并批量删除对账单明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
