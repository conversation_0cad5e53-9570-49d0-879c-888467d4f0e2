package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.mes.domain.ProductionIssueItem;
import com.iotlaser.spms.mes.domain.bo.ProductionIssueItemBo;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueItemVo;
import com.iotlaser.spms.mes.mapper.ProductionIssueItemMapper;
import com.iotlaser.spms.mes.service.IProductionIssueItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 生产领料明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductionIssueItemServiceImpl implements IProductionIssueItemService {

    private final ProductionIssueItemMapper baseMapper;

    /**
     * 查询生产领料明细
     *
     * @param itemId 主键
     * @return 生产领料明细
     */
    @Override
    public ProductionIssueItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询生产领料明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产领料明细分页列表
     */
    @Override
    public TableDataInfo<ProductionIssueItemVo> queryPageList(ProductionIssueItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductionIssueItem> lqw = buildQueryWrapper(bo);
        Page<ProductionIssueItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产领料明细列表
     *
     * @param bo 查询条件
     * @return 生产领料明细列表
     */
    @Override
    public List<ProductionIssueItemVo> queryList(ProductionIssueItemBo bo) {
        LambdaQueryWrapper<ProductionIssueItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductionIssueItem> buildQueryWrapper(ProductionIssueItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductionIssueItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductionIssueItem::getItemId);
        lqw.eq(bo.getIssueId() != null, ProductionIssueItem::getIssueId, bo.getIssueId());
        lqw.eq(bo.getProductId() != null, ProductionIssueItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductionIssueItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), ProductionIssueItem::getProductName, bo.getProductName());
        lqw.eq(bo.getQuantity() != null, ProductionIssueItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getFinishQuantity() != null, ProductionIssueItem::getFinishQuantity, bo.getFinishQuantity());
        lqw.eq(bo.getLocationId() != null, ProductionIssueItem::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), ProductionIssueItem::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), ProductionIssueItem::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductionIssueItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增生产领料明细
     *
     * @param bo 生产领料明细
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(ProductionIssueItemBo bo) {
        try {
            ProductionIssueItem add = MapstructUtils.convert(bo, ProductionIssueItem.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增生产领料明细失败");
            }

            bo.setItemId(add.getItemId());
            log.info("新增生产领料明细成功：产品【{}】数量【{}】", add.getProductName(), add.getQuantity());
            return true;
        } catch (Exception e) {
            log.error("新增生产领料明细失败：{}", e.getMessage(), e);
            throw new ServiceException("新增生产领料明细失败：" + e.getMessage());
        }
    }

    /**
     * 修改生产领料明细
     *
     * @param bo 生产领料明细
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ProductionIssueItemBo bo) {
        try {
            ProductionIssueItem update = MapstructUtils.convert(bo, ProductionIssueItem.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改生产领料明细失败：明细不存在或数据未变更");
            }

            log.info("修改生产领料明细成功：产品【{}】", update.getProductName());
            return true;
        } catch (Exception e) {
            log.error("修改生产领料明细失败：{}", e.getMessage(), e);
            throw new ServiceException("修改生产领料明细失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductionIssueItem entity) {
        // 校验必填字段
        if (entity.getIssueId() == null) {
            throw new ServiceException("生产领料单不能为空");
        }
        if (entity.getProductId() == null) {
            throw new ServiceException("产品不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("领料数量必须大于0");
        }

        // 校验同一领料单中产品不能重复
        if (entity.getIssueId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<ProductionIssueItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ProductionIssueItem::getIssueId, entity.getIssueId());
            wrapper.eq(ProductionIssueItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(ProductionIssueItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一生产领料单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除生产领料明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验明细是否可以删除
            List<ProductionIssueItem> items = baseMapper.selectByIds(ids);
            for (ProductionIssueItem item : items) {
                // 检查关联的生产领料单状态
                log.info("删除生产领料明细，产品：{}", item.getProductName());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 查询生产领料明细表及其关联信息
     *
     * @param itemId 主键
     * @return 生产领料明细表
     */
    @Override
    public ProductionIssueItemVo queryByIdWith(Long itemId) {
        return baseMapper.queryByIdWith(itemId);
    }

    /**
     * 分页查询生产领料明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产领料明细表分页列表
     */
    @Override
    public TableDataInfo<ProductionIssueItemVo> queryPageListWith(ProductionIssueItemBo bo, PageQuery pageQuery) {
        QueryWrapper<ProductionIssueItem> queryWrapper = buildQueryWrapperWith(bo);
        List<ProductionIssueItemVo> result = baseMapper.queryPageListWith(pageQuery.build(), queryWrapper);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<ProductionIssueItem> buildQueryWrapperWith(ProductionIssueItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<ProductionIssueItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getIssueId() != null, "item.issue_id", bo.getIssueId());
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        wrapper.eq(bo.getLocationId() != null, "item.location_id", bo.getLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getLocationCode()), "item.location_code", bo.getLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getLocationName()), "item.location_name", bo.getLocationName());
        wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        wrapper.eq(bo.getFinishQuantity() != null, "item.finish_quantity", bo.getFinishQuantity());
        wrapper.eq(bo.getPrice() != null, "item.price", bo.getPrice());
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }

    /**
     * 批量新增生产领料明细
     *
     * @param items 生产领料明细列表
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<ProductionIssueItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }
        List<ProductionIssueItem> entities = MapstructUtils.convert(items, ProductionIssueItem.class);
        return baseMapper.insertBatch(entities);
    }

    /**
     * 根据领料单ID查询明细ID列表
     *
     * @param issueId 领料单ID
     * @return 生产领料明细ID列表
     */
    @Override
    public List<Long> selectItemIdsByIssueId(Long issueId) {
        LambdaQueryWrapper<ProductionIssueItem> wrapper = Wrappers.lambdaQuery();
        wrapper.select(ProductionIssueItem::getItemId)
            .eq(ProductionIssueItem::getIssueId, issueId);
        return baseMapper.selectObjs(wrapper);
    }

    /**
     * 批量新增或更新生产领料明细
     *
     * @param items 生产领料明细列表
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<ProductionIssueItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }
        List<ProductionIssueItem> entities = MapstructUtils.convert(items, ProductionIssueItem.class);
        return baseMapper.insertOrUpdateBatch(entities);
    }

    /**
     * 根据ID集合删除生产领料明细
     *
     * @param ids ID集合
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
