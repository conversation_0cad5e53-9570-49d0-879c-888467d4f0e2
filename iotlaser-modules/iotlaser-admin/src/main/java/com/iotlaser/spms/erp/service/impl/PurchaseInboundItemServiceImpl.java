package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.common.service.PriceCalculationService;
import com.iotlaser.spms.erp.domain.PurchaseInboundItem;
import com.iotlaser.spms.erp.domain.PurchaseInboundItemBatch;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.erp.mapper.PurchaseInboundItemMapper;
import com.iotlaser.spms.erp.service.IPurchaseInboundItemBatchService;
import com.iotlaser.spms.erp.service.IPurchaseInboundItemService;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采购入库明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseInboundItemServiceImpl implements IPurchaseInboundItemService {

    private final PurchaseInboundItemMapper baseMapper;
    @Lazy
    @Autowired
    private IPurchaseInboundService purchaseInboundService;
    private final IPurchaseInboundItemBatchService batchService;
    private final PriceCalculationService priceCalculationService;

    /**
     * 查询采购入库明细
     *
     * @param itemId 主键
     * @return 采购入库明细
     */
    @Override
    public PurchaseInboundItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询采购入库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购入库明细分页列表
     */
    @Override
    public TableDataInfo<PurchaseInboundItemVo> queryPageList(PurchaseInboundItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseInboundItem> lqw = buildQueryWrapper(bo);
        Page<PurchaseInboundItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购入库明细列表
     *
     * @param bo 查询条件
     * @return 采购入库明细列表
     */
    @Override
    public List<PurchaseInboundItemVo> queryList(PurchaseInboundItemBo bo) {
        LambdaQueryWrapper<PurchaseInboundItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseInboundItem> buildQueryWrapper(PurchaseInboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseInboundItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseInboundItem::getItemId);
        lqw.eq(bo.getInboundId() != null, PurchaseInboundItem::getInboundId, bo.getInboundId());
        lqw.eq(bo.getProductId() != null, PurchaseInboundItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), PurchaseInboundItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), PurchaseInboundItem::getProductName, bo.getProductName());
        lqw.eq(bo.getQuantity() != null, PurchaseInboundItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getFinishQuantity() != null, PurchaseInboundItem::getFinishQuantity, bo.getFinishQuantity());
        lqw.eq(bo.getPrice() != null, PurchaseInboundItem::getPrice, bo.getPrice());
        lqw.eq(bo.getLocationId() != null, PurchaseInboundItem::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), PurchaseInboundItem::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), PurchaseInboundItem::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseInboundItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增采购入库明细
     *
     * @param bo 采购入库明细
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(PurchaseInboundItemBo bo) {
        // 计算价格信息
        calculatePriceFields(bo);

        PurchaseInboundItem add = MapstructUtils.convert(bo, PurchaseInboundItem.class);
        validEntityBeforeSave(add);
        return baseMapper.insert(add) > 0;
    }

    /**
     * 修改采购入库明细
     *
     * @param bo 采购入库明细
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(PurchaseInboundItemBo bo) {
        // 计算价格信息
        calculatePriceFields(bo);

        PurchaseInboundItem update = MapstructUtils.convert(bo, PurchaseInboundItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PurchaseInboundItem entity) {
        // 保留核心业务逻辑校验
        if (entity.getFinishQuantity() != null && entity.getQuantity() != null
            && entity.getFinishQuantity().compareTo(entity.getQuantity()) > 0) {
            throw new ServiceException("已完成数量不能大于待完成数量");
        }
    }

    /**
     * 校验并批量删除采购入库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验采购入库明细是否可以删除
            List<PurchaseInboundItem> items = baseMapper.selectByIds(ids);
            for (PurchaseInboundItem item : items) {
                // 1. 检查主表状态，只有草稿状态的入库明细才能删除
                PurchaseInboundVo inbound = purchaseInboundService.queryById(item.getInboundId());
                if (inbound != null && PurchaseInboundStatus.DRAFT != inbound.getInboundStatus()) {
                    throw new ServiceException("入库明细所属采购入库单【" + inbound.getInboundName() +
                        "】状态为【" + inbound.getInboundStatus() + "】，不允许删除明细");
                }

                // 2. 级联删除采购入库批次
                List<PurchaseInboundItemBatch> batches = batchService.queryByItemId(item.getItemId());
                if (!batches.isEmpty()) {
                    List<Long> batchIds = batches.stream()
                        .map(PurchaseInboundItemBatch::getBatchId)
                        .collect(Collectors.toList());
                    batchService.deleteWithValidByIds(batchIds, false);
                    log.info("级联删除采购入库批次，明细：{}，批次数量：{}", item.getProductName(), batchIds.size());
                }

                log.info("删除采购入库明细校验通过：产品【{}】", item.getProductName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除采购入库明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除采购入库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除采购入库明细失败：" + e.getMessage());
        }
    }


    /**
     * 查询采购入库明细表及其关联信息
     *
     * @param itemId 主键
     * @return 采购入库明细表
     */
    @Override
    public PurchaseInboundItemVo queryByIdWith(Long itemId) {
        return MapstructUtils.convert(baseMapper.queryByIdWith(itemId), PurchaseInboundItemVo.class);
    }

    /**
     * 分页查询采购入库明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购入库明细表分页列表
     */
    @Override
    public TableDataInfo<PurchaseInboundItemVo> queryPageListWith(PurchaseInboundItemBo bo, PageQuery pageQuery) {
        QueryWrapper<PurchaseInboundItem> queryWrapper = buildQueryWrapperWith(bo);
        List<PurchaseInboundItemVo> result = MapstructUtils.convert(baseMapper.queryPageListWith(pageQuery.build(), queryWrapper), PurchaseInboundItemVo.class);
        return TableDataInfo.build(result);
    }

    /**
     * 查询采购入库明细表列表及其关联信息
     *
     * @param bo 查询条件
     * @return 采购入库明细表列表
     */
    @Override
    public List<PurchaseInboundItemVo> queryListWith(PurchaseInboundItemBo bo) {
        QueryWrapper<PurchaseInboundItem> queryWrapper = buildQueryWrapperWith(bo);
        return MapstructUtils.convert(baseMapper.queryPageListWith(null, queryWrapper), PurchaseInboundItemVo.class);
    }

    /**
     * 批量插入采购入库明细表
     *
     * @param items 明细
     * @return 是否插入成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertOrUpdateBatch(List<PurchaseInboundItemBo> items) {
        List<PurchaseInboundItem> entities = MapstructUtils.convert(items, PurchaseInboundItem.class);
        return baseMapper.insertOrUpdateBatch(entities);
    }

    /**
     * 根据采购入库单id查询采购入库明细表id集合
     *
     * @param inboundId 采购入库单id
     * @return 采购入库明细表id集合
     */
    @Override
    public List<Long> selectItemIdsByInboundId(Long inboundId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(PurchaseInboundItem.class)
            .select(PurchaseInboundItem::getItemId)
            .eq(PurchaseInboundItem::getInboundId, inboundId)
        ).stream().map(PurchaseInboundItem::getItemId).collect(Collectors.toList());
    }

    private QueryWrapper<PurchaseInboundItem> buildQueryWrapperWith(PurchaseInboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<PurchaseInboundItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getInboundId() != null, "item.inbound_id", bo.getInboundId());
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        wrapper.eq(bo.getFinishQuantity() != null, "item.finish_quantity", bo.getFinishQuantity());
        wrapper.eq(bo.getPrice() != null, "item.price", bo.getPrice());
        wrapper.eq(bo.getLocationId() != null, "item.location_id", bo.getLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getLocationCode()), "item.location_code", bo.getLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getLocationName()), "item.location_name", bo.getLocationName());
        wrapper.eq(bo.getProductionTime() != null, "item.production_time", bo.getProductionTime());
        wrapper.eq(bo.getExpiryTime() != null, "item.expiry_time", bo.getExpiryTime());
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }

    /**
     * TODO 计算价格相关字段
     *
     * @param bo 采购入库明细业务对象
     */
    private void calculatePriceFields(PurchaseInboundItemBo bo) {
        if (bo.getPrice() != null && bo.getTaxRate() != null && bo.getQuantity() != null) {
            // 从含税价计算其他价格字段
            //PriceCalculationService.PriceCalculationResult result = priceCalculationService.calculateFromInclusivePrice(bo.getPrice(), bo.getTaxRate(), bo.getQuantity());
            //bo.setPriceExclusiveTax(result.getExclusiveTaxPrice());
            //bo.setAmountExclusiveTax(result.getExclusiveTaxAmount());
            //bo.setTaxAmount(result.getTaxAmount());

            log.info("采购入库明细价格计算完成 - 含税价: {}, 税率: {}%, 不含税价: {}, 金额（不含税）: {}, 税额: {}",
                bo.getPrice(), bo.getTaxRate(), bo.getPriceExclusiveTax(), bo.getAmountExclusiveTax(), bo.getTaxAmount());
        }
    }

}
