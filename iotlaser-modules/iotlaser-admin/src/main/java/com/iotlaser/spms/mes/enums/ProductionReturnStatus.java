package com.iotlaser.spms.mes.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生产退料单状态
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Getter
@AllArgsConstructor
public enum ProductionReturnStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "已创建"),
    PENDING_WAREHOUSE("pending_warehouse", "待入库", "等待入库"),
    COMPLETED("completed", "已入库", "已完成入库"),
    CANCELLED("cancelled", "已取消", "已取消");

    public final static String DICT_CODE = "mes_production_return_status";
    public final static String DICT_NAME = "生产退料单状态";
    public final static String DICT_DESC = "管理生产过程中退料单的状态流转，从创建、待入库到完成入库的完整生命周期";

    @EnumValue
    private final String value;
    private final String name;
    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static ProductionReturnStatus getByValue(String value) {
        for (ProductionReturnStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
