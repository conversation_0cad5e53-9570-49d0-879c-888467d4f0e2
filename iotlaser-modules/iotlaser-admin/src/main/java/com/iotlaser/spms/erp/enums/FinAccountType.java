package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账户类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinAccountType implements IDictEnum<String> {

    BANK_ACCOUNT("bank_account", "银行账户", "银行存款账户"),
    CASH_ACCOUNT("cash_account", "现金账户", "现金库存账户"),
    CREDIT_CARD("credit_card", "信用卡账户", "信用卡账户"),
    ONLINE_PAYMENT("online_payment", "在线支付账户", "支付宝、微信等在线支付账户"),
    FOREIGN_CURRENCY("foreign_currency", "外币账户", "外币银行账户"),
    ESCROW("escrow", "托管账户", "第三方托管账户"),
    PETTY_CASH("petty_cash", "备用金账户", "日常备用金账户"),
    DEPOSIT("deposit", "保证金账户", "各类保证金账户"),
    OTHER("other", "其他账户", "其他类型账户");

    public final static String DICT_CODE = "erp_fin_account_type";
    public final static String DICT_NAME = "财务账户类型";
    public final static String DICT_DESC = "管理财务账户的分类类型，包括银行账户、现金账户、信用卡等不同类型的账户";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 账户类型枚举
     */
    public static FinAccountType getByValue(String value) {
        for (FinAccountType accountType : values()) {
            if (accountType.getValue().equals(value)) {
                return accountType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否为银行类账户
     *
     * @return 是否为银行类账户
     */
    public boolean isBankType() {
        return this == BANK_ACCOUNT || this == FOREIGN_CURRENCY;
    }

    /**
     * 判断是否为现金类账户
     *
     * @return 是否为现金类账户
     */
    public boolean isCashType() {
        return this == CASH_ACCOUNT || this == PETTY_CASH;
    }

    /**
     * 判断是否为电子账户
     *
     * @return 是否为电子账户
     */
    public boolean isElectronicType() {
        return this == CREDIT_CARD || this == ONLINE_PAYMENT;
    }

    /**
     * 判断是否需要银行对账
     *
     * @return 是否需要银行对账
     */
    public boolean requiresBankReconciliation() {
        return this == BANK_ACCOUNT || this == FOREIGN_CURRENCY || this == CREDIT_CARD;
    }

    /**
     * 判断是否有余额限制
     *
     * @return 是否有余额限制
     */
    public boolean hasBalanceLimit() {
        return this == PETTY_CASH || this == DEPOSIT;
    }

    /**
     * 获取默认币种
     *
     * @return 默认币种
     */
    public String getDefaultCurrency() {
        switch (this) {
            case FOREIGN_CURRENCY:
                return "USD"; // 外币账户默认美元
            case BANK_ACCOUNT:
            case CASH_ACCOUNT:
            case CREDIT_CARD:
            case ONLINE_PAYMENT:
            case PETTY_CASH:
            case DEPOSIT:
            case ESCROW:
            case OTHER:
            default:
                return "CNY"; // 其他账户默认人民币
        }
    }
}
