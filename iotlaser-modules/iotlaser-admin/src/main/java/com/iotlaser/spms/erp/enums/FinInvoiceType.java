package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发票类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinInvoiceType implements IDictEnum<String> {

    PURCHASE("purchase", "采购发票", "供应商开具的采购商品或服务的发票"),
    SERVICE("service", "服务发票", "供应商提供服务的发票"),
    FREIGHT("freight", "运费发票", "物流运输费用发票"),
    UTILITIES("utilities", "水电费发票", "水电气等公用事业费用发票"),
    RENT("rent", "租金发票", "房屋、设备租赁费用发票"),
    CONSULTING("consulting", "咨询费发票", "咨询、顾问服务费用发票"),
    MAINTENANCE("maintenance", "维修费发票", "设备维修保养费用发票"),
    INSURANCE("insurance", "保险费发票", "各类保险费用发票"),
    TAX("tax", "税费发票", "各种税费缴纳发票"),
    OTHER("other", "其他发票", "其他类型的发票");

    public final static String DICT_CODE = "erp_fin_invoice_type";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 发票类型枚举
     */
    public static FinInvoiceType getByValue(String value) {
        for (FinInvoiceType invoiceType : values()) {
            if (invoiceType.getValue().equals(value)) {
                return invoiceType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    /**
     * 判断是否需要特殊审批
     *
     * @return 是否需要特殊审批
     */
    public boolean requiresSpecialApproval() {
        return this == CONSULTING || this == INSURANCE || this == TAX;
    }

    /**
     * 判断是否为经常性费用
     *
     * @return 是否为经常性费用
     */
    public boolean isRecurring() {
        return this == UTILITIES || this == RENT || this == INSURANCE;
    }

    /**
     * 获取默认付款期限（天）
     *
     * @return 默认付款期限
     */
    public int getDefaultPaymentTerms() {
        switch (this) {
            case PURCHASE:
                return 30;
            case SERVICE:
            case CONSULTING:
                return 15;
            case FREIGHT:
                return 7;
            case UTILITIES:
            case RENT:
                return 10;
            case MAINTENANCE:
                return 20;
            case INSURANCE:
            case TAX:
                return 5;
            case OTHER:
            default:
                return 30;
        }
    }
}
