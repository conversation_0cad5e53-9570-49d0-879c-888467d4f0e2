package com.iotlaser.spms.wms.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 物料预留管理Service接口
 * <p>
 * 功能：
 * 1. 物料预留创建
 * 2. 预留状态管理
 * 3. 预留释放处理
 * 4. 预留查询统计
 *
 * <AUTHOR>
 * @date 2025/06/16
 */
public interface IMaterialReservationService {

    /**
     * 为生产订单预留物料
     *
     * @param orderId  生产订单ID
     * @param bomId    BOM清单ID
     * @param quantity 生产数量
     * @return 预留结果
     */
    Map<String, Object> reserveMaterialsForOrder(Long orderId, Long bomId, BigDecimal quantity);

    /**
     * 创建单个物料预留
     *
     * @param materialId      物料ID
     * @param quantity        预留数量
     * @param orderId         关联订单ID
     * @param reservationType 预留类型
     * @return 预留记录ID
     */
    Long createMaterialReservation(Long materialId, BigDecimal quantity, Long orderId, String reservationType);

    /**
     * 释放物料预留
     *
     * @param reservationId   预留记录ID
     * @param releaseQuantity 释放数量
     * @param reason          释放原因
     * @return 是否成功
     */
    Boolean releaseMaterialReservation(Long reservationId, BigDecimal releaseQuantity, String reason);

    /**
     * 批量释放订单的物料预留
     *
     * @param orderId 生产订单ID
     * @param reason  释放原因
     * @return 释放结果
     */
    Map<String, Object> releaseOrderReservations(Long orderId, String reason);

    /**
     * 查询物料的预留情况
     *
     * @param materialId 物料ID
     * @return 预留情况
     */
    Map<String, Object> getMaterialReservationStatus(Long materialId);

    /**
     * 查询订单的预留情况
     *
     * @param orderId 生产订单ID
     * @return 订单预留情况
     */
    List<Map<String, Object>> getOrderReservations(Long orderId);

    /**
     * 检查物料可预留数量
     *
     * @param materialId       物料ID
     * @param requiredQuantity 需求数量
     * @return 可预留数量检查结果
     */
    Map<String, Object> checkReservableQuantity(Long materialId, BigDecimal requiredQuantity);

    /**
     * 转移物料预留
     *
     * @param fromOrderId      源订单ID
     * @param toOrderId        目标订单ID
     * @param materialId       物料ID
     * @param transferQuantity 转移数量
     * @return 转移结果
     */
    Map<String, Object> transferReservation(Long fromOrderId, Long toOrderId, Long materialId, BigDecimal transferQuantity);

    /**
     * 获取预留统计报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 预留统计
     */
    Map<String, Object> getReservationStatistics(String startDate, String endDate);

    /**
     * 自动释放过期预留
     *
     * @return 释放结果
     */
    Map<String, Object> autoReleaseExpiredReservations();
}
