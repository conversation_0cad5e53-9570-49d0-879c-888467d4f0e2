package com.iotlaser.spms.pro.service;

import com.iotlaser.spms.pro.domain.bo.ProcessBo;
import com.iotlaser.spms.pro.domain.vo.ProcessVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 工序Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-15
 */
public interface IProcessService {

    /**
     * 查询工序
     *
     * @param processId 主键
     * @return 工序
     */
    ProcessVo queryById(Long processId);

    /**
     * 分页查询工序列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工序分页列表
     */
    TableDataInfo<ProcessVo> queryPageList(ProcessBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的工序列表
     *
     * @param bo 查询条件
     * @return 工序列表
     */
    List<ProcessVo> queryList(ProcessBo bo);

    /**
     * 新增工序
     *
     * @param bo 工序
     * @return 是否新增成功
     */
    Boolean insertByBo(ProcessBo bo);

    /**
     * 修改工序
     *
     * @param bo 工序
     * @return 是否修改成功
     */
    Boolean updateByBo(ProcessBo bo);

    /**
     * 校验并批量删除工序信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
