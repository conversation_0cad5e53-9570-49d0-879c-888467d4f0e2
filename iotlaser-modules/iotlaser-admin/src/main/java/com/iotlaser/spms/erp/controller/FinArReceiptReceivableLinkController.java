package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinArReceiptReceivableLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptReceivableLinkVo;
import com.iotlaser.spms.erp.service.IFinArReceiptReceivableLinkService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收款单与应收单核销关系
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finArReceiptReceivableLink")
public class FinArReceiptReceivableLinkController extends BaseController {

    private final IFinArReceiptReceivableLinkService finArReceiptReceivableLinkService;

    /**
     * 查询收款单与应收单核销关系列表
     */
    @SaCheckPermission("erp:finArReceiptReceivableLink:list")
    @GetMapping("/list")
    public TableDataInfo<FinArReceiptReceivableLinkVo> list(FinArReceiptReceivableLinkBo bo, PageQuery pageQuery) {
        return finArReceiptReceivableLinkService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出收款单与应收单核销关系列表
     */
    @SaCheckPermission("erp:finArReceiptReceivableLink:export")
    @Log(title = "收款单与应收单核销关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinArReceiptReceivableLinkBo bo, HttpServletResponse response) {
        List<FinArReceiptReceivableLinkVo> list = finArReceiptReceivableLinkService.queryList(bo);
        ExcelUtil.exportExcel(list, "收款单与应收单核销关系", FinArReceiptReceivableLinkVo.class, response);
    }

    /**
     * 获取收款单与应收单核销关系详细信息
     *
     * @param linkId 主键
     */
    @SaCheckPermission("erp:finArReceiptReceivableLink:query")
    @GetMapping("/{linkId}")
    public R<FinArReceiptReceivableLinkVo> getInfo(@NotNull(message = "主键不能为空")
                                                   @PathVariable Long linkId) {
        return R.ok(finArReceiptReceivableLinkService.queryById(linkId));
    }

    /**
     * 新增收款单与应收单核销关系
     */
    @SaCheckPermission("erp:finArReceiptReceivableLink:add")
    @Log(title = "收款单与应收单核销关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinArReceiptReceivableLinkBo bo) {
        return toAjax(finArReceiptReceivableLinkService.insertByBo(bo));
    }

    /**
     * 修改收款单与应收单核销关系
     */
    @SaCheckPermission("erp:finArReceiptReceivableLink:edit")
    @Log(title = "收款单与应收单核销关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinArReceiptReceivableLinkBo bo) {
        return toAjax(finArReceiptReceivableLinkService.updateByBo(bo));
    }

    /**
     * 删除收款单与应收单核销关系
     *
     * @param linkIds 主键串
     */
    @SaCheckPermission("erp:finArReceiptReceivableLink:remove")
    @Log(title = "收款单与应收单核销关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{linkIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] linkIds) {
        return toAjax(finArReceiptReceivableLinkService.deleteWithValidByIds(List.of(linkIds), true));
    }
}
