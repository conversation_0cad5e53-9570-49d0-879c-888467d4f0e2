package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinArReceivableBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import com.iotlaser.spms.erp.service.IFinArReceivableService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应收单
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finArReceivable")
public class FinArReceivableController extends BaseController {

    private final IFinArReceivableService finArReceivableService;

    /**
     * 查询应收单列表
     */
    @SaCheckPermission("erp:finArReceivable:list")
    @GetMapping("/list")
    public TableDataInfo<FinArReceivableVo> list(FinArReceivableBo bo, PageQuery pageQuery) {
        return finArReceivableService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出应收单列表
     */
    @SaCheckPermission("erp:finArReceivable:export")
    @Log(title = "应收单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinArReceivableBo bo, HttpServletResponse response) {
        List<FinArReceivableVo> list = finArReceivableService.queryList(bo);
        ExcelUtil.exportExcel(list, "应收单", FinArReceivableVo.class, response);
    }

    /**
     * 获取应收单详细信息
     *
     * @param receivableId 主键
     */
    @SaCheckPermission("erp:finArReceivable:query")
    @GetMapping("/{receivableId}")
    public R<FinArReceivableVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long receivableId) {
        return R.ok(finArReceivableService.queryById(receivableId));
    }

    /**
     * 新增应收单
     */
    @SaCheckPermission("erp:finArReceivable:add")
    @Log(title = "应收单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinArReceivableBo bo) {
        return toAjax(finArReceivableService.insertByBo(bo));
    }

    /**
     * 修改应收单
     */
    @SaCheckPermission("erp:finArReceivable:edit")
    @Log(title = "应收单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinArReceivableBo bo) {
        return toAjax(finArReceivableService.updateByBo(bo));
    }

    /**
     * 删除应收单
     *
     * @param receivableIds 主键串
     */
    @SaCheckPermission("erp:finArReceivable:remove")
    @Log(title = "应收单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{receivableIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] receivableIds) {
        return toAjax(finArReceivableService.deleteWithValidByIds(List.of(receivableIds), true));
    }
}
