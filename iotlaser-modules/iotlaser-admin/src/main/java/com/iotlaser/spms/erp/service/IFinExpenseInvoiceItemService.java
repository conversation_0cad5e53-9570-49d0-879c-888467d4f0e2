package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinExpenseInvoiceItemBo;
import com.iotlaser.spms.erp.domain.vo.FinExpenseInvoiceItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 管理费用明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-20
 */
public interface IFinExpenseInvoiceItemService {

    /**
     * 查询管理费用明细
     *
     * @param itemId 主键
     * @return 管理费用明细
     */
    FinExpenseInvoiceItemVo queryById(Long itemId);

    /**
     * 分页查询管理费用明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 管理费用明细分页列表
     */
    TableDataInfo<FinExpenseInvoiceItemVo> queryPageList(FinExpenseInvoiceItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的管理费用明细列表
     *
     * @param bo 查询条件
     * @return 管理费用明细列表
     */
    List<FinExpenseInvoiceItemVo> queryList(FinExpenseInvoiceItemBo bo);

    /**
     * 新增管理费用明细
     *
     * @param bo 管理费用明细
     * @return 是否新增成功
     */
    Boolean insertByBo(FinExpenseInvoiceItemBo bo);

    /**
     * 修改管理费用明细
     *
     * @param bo 管理费用明细
     * @return 是否修改成功
     */
    Boolean updateByBo(FinExpenseInvoiceItemBo bo);

    /**
     * 校验并批量删除管理费用明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
