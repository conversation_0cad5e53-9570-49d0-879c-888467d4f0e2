package com.iotlaser.spms.common.service;

import java.math.BigDecimal;

/**
 * 价格计算服务接口
 * 提供统一的价格计算功能
 *
 * <AUTHOR> <PERSON>
 * @date 2025/06/24
 */
public interface PriceCalculationService {

    /**
     * 计算含税金额
     *
     * @param quantity 数量
     * @param price    单价（含税）
     * @return 含税金额
     */
    BigDecimal calculateAmount(BigDecimal quantity, BigDecimal price);

    /**
     * 计算不含税金额
     *
     * @param quantity          数量
     * @param priceExclusiveTax 单价（不含税）
     * @return 不含税金额
     */
    BigDecimal calculateAmountExclusiveTax(BigDecimal quantity, BigDecimal priceExclusiveTax);

    /**
     * 计算税额
     *
     * @param amountExclusiveTax 不含税金额
     * @param taxRate            税率
     * @return 税额
     */
    BigDecimal calculateTaxAmount(BigDecimal amountExclusiveTax, BigDecimal taxRate);

    /**
     * 根据含税单价和税率计算不含税单价
     *
     * @param price   含税单价
     * @param taxRate 税率
     * @return 不含税单价
     */
    BigDecimal calculatePriceExclusiveTax(BigDecimal price, BigDecimal taxRate);

    /**
     * 根据不含税单价和税率计算含税单价
     *
     * @param priceExclusiveTax 不含税单价
     * @param taxRate           税率
     * @return 含税单价
     */
    BigDecimal calculatePrice(BigDecimal priceExclusiveTax, BigDecimal taxRate);

    /**
     * 校验价格计算的一致性
     *
     * @param quantity           数量
     * @param price              含税单价
     * @param priceExclusiveTax  不含税单价
     * @param amount             含税金额
     * @param amountExclusiveTax 不含税金额
     * @param taxRate            税率
     * @param taxAmount          税额
     * @return 是否一致
     */
    boolean validatePriceConsistency(BigDecimal quantity, BigDecimal price, BigDecimal priceExclusiveTax,
                                     BigDecimal amount, BigDecimal amountExclusiveTax,
                                     BigDecimal taxRate, BigDecimal taxAmount);
}
