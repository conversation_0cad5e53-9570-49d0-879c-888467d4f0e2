package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存记录单上游类型枚举
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Getter
@AllArgsConstructor
public enum DirectSourceType implements IDictEnum<String> {

    INIT_INBOUND("init_inbound", "初始化入库", "系统初始化库存入库"),
    PURCHASE_ORDER_INBOUND("purchase_order_inbound", "采购订单入库", "采购订单直接入库"),
    PURCHASE_INBOUND("purchase_inbound", "采购入库", "采购入库单入库"),
    PURCHASE_RETURN_OUTBOUND("purchase_return_outbound", "采购退货", "采购退货出库"),
    PRODUCTION_INBOUND("production_inbound", "生产入库", "生产完工入库"),
    PRODUCTION_ISSUE_OUTBOUND("production_issue_outbound", "生产领料", "生产领料出库"),
    PRODUCTION_RETURN_INBOUND("production_return_inbound", "生产退料", "生产退料入库"),
    TRANSFER_OUTBOUND("transfer_outbound", "移库出库", "库位移库出库"),
    TRANSFER_INBOUND("transfer_inbound", "移库入库", "库位移库入库"),
    SALE_OUTBOUND("sale_outbound", "销售出库", "销售订单出库"),
    SALE_RETURN_INBOUND("sale_return_inbound", "销售退货", "销售退货入库"),
    OTHER_INBOUND("other_inbound", "其他入库", "其他原因入库"),
    OTHER_OUTBOUND("other_outbound", "其他出库", "其他原因出库");

    public final static String DICT_CODE = "wms_direct_source_type";
    public final static String DICT_NAME = "直接来源类型";
    public final static String DICT_DESC = "定义库存变动的直接来源类型，包括采购入库、销售出库、生产领料等具体业务类型";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 直接来源类型枚举
     */
    public static DirectSourceType getByValue(String value) {
        for (DirectSourceType sourceType : values()) {
            if (sourceType.getValue().equals(value)) {
                return sourceType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
