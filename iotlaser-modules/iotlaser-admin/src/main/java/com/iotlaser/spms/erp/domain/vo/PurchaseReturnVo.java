package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.PurchaseReturn;
import com.iotlaser.spms.erp.enums.PurchaseReturnStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 采购退货视图对象 erp_purchase_return
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PurchaseReturn.class)
public class PurchaseReturnVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退货单ID
     */
    @ExcelProperty(value = "退货单ID")
    private Long returnId;

    /**
     * 退货单编号
     */
    @ExcelProperty(value = "退货单编号")
    private String returnCode;

    /**
     * 退货单名称
     */
    @ExcelProperty(value = "退货单名称")
    private String returnName;

    /**
     * 采购订单ID
     */
    @ExcelProperty(value = "采购订单ID")
    private Long orderId;

    /**
     * 采购订单编码
     */
    @ExcelProperty(value = "采购订单编码")
    private String orderCode;

    /**
     * 采购订单名称
     */
    @ExcelProperty(value = "采购订单名称")
    private String orderName;

    /**
     * 入库单ID
     */
    @ExcelProperty(value = "入库单ID")
    private Long inboundId;

    /**
     * 入库单编号
     */
    @ExcelProperty(value = "入库单编号")
    private String inboundCode;

    /**
     * 入库单名称
     */
    @ExcelProperty(value = "入库单名称")
    private String inboundName;

    /**
     * 检验单ID
     */
    @ExcelProperty(value = "检验单ID")
    private Long inspectionId;

    /**
     * 检验单编号
     */
    @ExcelProperty(value = "检验单编号")
    private String inspectionCode;

    /**
     * 检验单名称
     */
    @ExcelProperty(value = "检验单名称")
    private String inspectionName;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long supplierId;

    /**
     * 供应商编码
     */
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 退货日期
     */
    @ExcelProperty(value = "退货日期")
    private LocalDate returnDate;

    /**
     * 退货状态
     */
    @ExcelProperty(value = "退货状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_purchase_return_status")
    private PurchaseReturnStatus returnStatus;

    /**
     * 退货申请人ID
     */
    @ExcelProperty(value = "退货申请人ID")
    private Long applicantId;

    /**
     * 退货申请人
     */
    @ExcelProperty(value = "退货申请人")
    private String applicantName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

    /**
     * 明细
     */
    private List<PurchaseReturnItemVo> items;
}
