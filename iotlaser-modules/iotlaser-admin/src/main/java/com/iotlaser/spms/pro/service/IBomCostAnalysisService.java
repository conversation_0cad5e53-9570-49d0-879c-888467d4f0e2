package com.iotlaser.spms.pro.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * BOM成本分析Service接口
 * <p>
 * 功能：
 * 1. BOM成本计算
 * 2. 成本构成分析
 * 3. 成本趋势分析
 * 4. 成本优化建议
 *
 * <AUTHOR>
 * @date 2025/06/16
 */
public interface IBomCostAnalysisService {

    /**
     * 计算BOM总成本
     *
     * @param bomId    BOM清单ID
     * @param quantity 生产数量
     * @return BOM成本信息
     */
    Map<String, Object> calculateBomCost(Long bomId, BigDecimal quantity);

    /**
     * 获取BOM成本构成分析
     *
     * @param bomId    BOM清单ID
     * @param quantity 生产数量
     * @return 成本构成分析
     */
    Map<String, Object> analyzeCostComposition(Long bomId, BigDecimal quantity);

    /**
     * 获取物料成本排行
     *
     * @param bomId    BOM清单ID
     * @param quantity 生产数量
     * @return 物料成本排行
     */
    List<Map<String, Object>> getMaterialCostRanking(Long bomId, BigDecimal quantity);

    /**
     * 分析BOM成本趋势
     *
     * @param bomId     BOM清单ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 成本趋势分析
     */
    Map<String, Object> analyzeCostTrend(Long bomId, String startDate, String endDate);

    /**
     * 生成成本优化建议
     *
     * @param bomId    BOM清单ID
     * @param quantity 生产数量
     * @return 成本优化建议
     */
    List<Map<String, Object>> generateCostOptimizationSuggestions(Long bomId, BigDecimal quantity);

    /**
     * 比较多个BOM的成本
     *
     * @param bomIds   BOM清单ID列表
     * @param quantity 生产数量
     * @return BOM成本比较
     */
    Map<String, Object> compareBomCosts(List<Long> bomIds, BigDecimal quantity);

    /**
     * 计算BOM成本敏感性分析
     *
     * @param bomId              BOM清单ID
     * @param quantity           生产数量
     * @param priceChangePercent 价格变动百分比
     * @return 敏感性分析结果
     */
    Map<String, Object> analyzeCostSensitivity(Long bomId, BigDecimal quantity, BigDecimal priceChangePercent);
}
