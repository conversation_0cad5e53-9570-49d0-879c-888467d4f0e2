package com.iotlaser.spms.pro.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 工序对象 pro_process
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pro_process")
public class Process extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 工序ID
     */
    @TableId(value = "process_id")
    private Long processId;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 工序类别
     */
    private String processCategory;

    /**
     * 标准工时(分钟)
     */
    private Long standardDuration;

    /**
     * 标准计件单价
     */
    private Long standardPrice;

    /**
     * 是否质检
     */
    private String checkFlag;

    /**
     * 报工类型
     */
    private String reportType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
