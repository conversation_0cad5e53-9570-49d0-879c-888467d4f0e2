package com.iotlaser.spms.base.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 公司类型枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-15
 */
@Getter
@AllArgsConstructor
public enum CompanyType implements IDictEnum<String> {

    SUPPLIER("supplier", "供应商", "提供原材料、零部件或服务的企业"),
    CUSTOMER("customer", "客户", "购买产品或服务的企业"),
    BOTH("both", "供应商+客户", "既是供应商又是客户的企业"),
    PARTNER("partner", "合作伙伴", "战略合作伙伴企业"),
    SUBSIDIARY("subsidiary", "子公司", "下属子公司"),
    BRANCH("branch", "分公司", "分支机构"),
    HEADQUARTERS("headquarters", "总公司", "总部公司"),
    OTHER("other", "其他", "其他类型的公司");

    public final static String DICT_CODE = "base_company_type";
    public final static String DICT_NAME = "公司类型";
    public final static String DICT_DESC = "定义企业合作关系类型，包括供应商、客户、合作伙伴等不同业务关系";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 公司类型枚举
     */
    public static CompanyType getByValue(String value) {
        for (CompanyType type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
