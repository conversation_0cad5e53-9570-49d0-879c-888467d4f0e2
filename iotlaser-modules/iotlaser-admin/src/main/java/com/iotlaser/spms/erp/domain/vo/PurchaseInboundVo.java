package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 采购入库视图对象 erp_purchase_inbound
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PurchaseInbound.class)
public class PurchaseInboundVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 入库单ID
     */
    @ExcelProperty(value = "入库单ID")
    private Long inboundId;

    /**
     * 入库单编号
     */
    @ExcelProperty(value = "入库单编号")
    private String inboundCode;

    /**
     * 入库单名称
     */
    @ExcelProperty(value = "入库单名称")
    private String inboundName;

    /**
     * 采购订单ID
     */
    @ExcelProperty(value = "采购订单ID")
    private Long orderId;

    /**
     * 采购订单编码
     */
    @ExcelProperty(value = "采购订单编码")
    private String orderCode;

    /**
     * 采购订单名称
     */
    @ExcelProperty(value = "采购订单名称")
    private String orderName;

    /**
     * 检验单ID
     */
    @ExcelProperty(value = "检验单ID")
    private Long inspectionId;

    /**
     * 检验单编号
     */
    @ExcelProperty(value = "检验单编号")
    private String inspectionCode;

    /**
     * 检验单名称
     */
    @ExcelProperty(value = "检验单名称")
    private String inspectionName;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long supplierId;

    /**
     * 供应商编码
     */
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 入库日期
     */
    @ExcelProperty(value = "入库日期")
    private Date inboundDate;

    /**
     * 入库状态
     */
    @ExcelProperty(value = "入库状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_purchase_inbound_status")
    private PurchaseInboundStatus inboundStatus;

    /**
     * 收货负责人ID
     */
    @ExcelProperty(value = "收货负责人ID")
    private Long handlerId;

    /**
     * 收货负责人
     */
    @ExcelProperty(value = "收货负责人")
    private String handlerName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


    /**
     * 明细
     */
    private List<PurchaseInboundItemVo> items;

    /**
     * 采购订单
     */
    private PurchaseOrderVo purchaseOrderVo;
}
