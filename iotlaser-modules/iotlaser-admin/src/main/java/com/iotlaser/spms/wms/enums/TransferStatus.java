package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 移库状态枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025/4/30
 */
@Getter
@AllArgsConstructor
public enum TransferStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "移库单已创建，但未提交"),
    PENDING("pending", "待执行", "移库指令已创建"),
    CONFIRMED("confirmed", "已确认", "移库指令已确认，准备执行"),
    IN_PROGRESS("in_progress", "执行中", "货物已从源库位下架，在途"),
    COMPLETED("completed", "已完成", "货物已上架到目标库位");

    public final static String DICT_CODE = "wms_transfer_status";
    public final static String DICT_NAME = "移库状态";
    public final static String DICT_DESC = "管理库位间货物移库的流程状态，从移库指令到移库完成的完整流程";

    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 移库状态枚举
     */
    public static TransferStatus getByValue(String value) {
        for (TransferStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
