package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应收账款状态枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinArReceivableStatus implements IDictEnum<String> {

    PENDING("pending", "待确认", "应收账款已生成，等待确认"),
    CONFIRMED("confirmed", "已确认", "应收账款已确认，等待收款"),
    PARTIALLY_PAID("partially_paid", "部分收款", "已收到部分款项"),
    FULLY_PAID("fully_paid", "完全收款", "应收账款已全部收回"),
    OVERDUE("overdue", "已逾期", "应收账款已超过收款期限"),
    CANCELLED("cancelled", "已取消", "应收账款被取消");

    public final static String DICT_CODE = "erp_fin_ar_receivable_status";
    public final static String DICT_NAME = "应收账款状态";
    public final static String DICT_DESC = "管理应收账款的收款流程状态，从生成、确认到收款完成的完整业务流程";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 应收账款状态枚举
     */
    public static FinArReceivableStatus getByValue(String value) {
        for (FinArReceivableStatus receivableStatus : values()) {
            if (receivableStatus.getValue().equals(value)) {
                return receivableStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否为可编辑状态
     *
     * @return 是否可编辑
     */
    public boolean isEditable() {
        return this == PENDING;
    }

    /**
     * 判断是否为可删除状态
     *
     * @return 是否可删除
     */
    public boolean isDeletable() {
        return this == PENDING;
    }

    /**
     * 判断是否为已完成状态
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return this == FULLY_PAID || this == CANCELLED;
    }

    /**
     * 判断是否可以收款
     *
     * @return 是否可以收款
     */
    public boolean isCollectable() {
        return this == CONFIRMED || this == PARTIALLY_PAID || this == OVERDUE;
    }

    /**
     * 判断是否为逾期状态
     *
     * @return 是否逾期
     */
    public boolean isOverdue() {
        return this == OVERDUE;
    }

    /**
     * 获取下一个可能的状态
     *
     * @return 下一个可能的状态列表
     */
    public FinArReceivableStatus[] getNextPossibleStates() {
        switch (this) {
            case PENDING:
                return new FinArReceivableStatus[]{CONFIRMED, CANCELLED};
            case CONFIRMED:
                return new FinArReceivableStatus[]{PARTIALLY_PAID, OVERDUE, CANCELLED};
            case PARTIALLY_PAID:
                return new FinArReceivableStatus[]{FULLY_PAID, OVERDUE};
            case OVERDUE:
                return new FinArReceivableStatus[]{PARTIALLY_PAID, FULLY_PAID};
            case FULLY_PAID:
            case CANCELLED:
            default:
                return new FinArReceivableStatus[]{};
        }
    }
}
