package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.mes.domain.ProductionReturnItemBatch;
import com.iotlaser.spms.mes.domain.bo.ProductionReturnItemBatchBo;
import com.iotlaser.spms.mes.domain.vo.ProductionReturnItemBatchVo;
import com.iotlaser.spms.mes.mapper.ProductionReturnItemBatchMapper;
import com.iotlaser.spms.mes.service.IProductionReturnItemBatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 生产退料批次明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductionReturnItemBatchServiceImpl implements IProductionReturnItemBatchService {

    private final ProductionReturnItemBatchMapper baseMapper;

    /**
     * 查询生产退料批次明细
     *
     * @param batchId 主键
     * @return 生产退料批次明细
     */
    @Override
    public ProductionReturnItemBatchVo queryById(Long batchId) {
        return baseMapper.selectVoById(batchId);
    }

    /**
     * 分页查询生产退料批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产退料批次明细分页列表
     */
    @Override
    public TableDataInfo<ProductionReturnItemBatchVo> queryPageList(ProductionReturnItemBatchBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductionReturnItemBatch> lqw = buildQueryWrapper(bo);
        Page<ProductionReturnItemBatchVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产退料批次明细列表
     *
     * @param bo 查询条件
     * @return 生产退料批次明细列表
     */
    @Override
    public List<ProductionReturnItemBatchVo> queryList(ProductionReturnItemBatchBo bo) {
        LambdaQueryWrapper<ProductionReturnItemBatch> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductionReturnItemBatch> buildQueryWrapper(ProductionReturnItemBatchBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductionReturnItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductionReturnItemBatch::getBatchId);
        lqw.eq(bo.getItemId() != null, ProductionReturnItemBatch::getItemId, bo.getItemId());
        lqw.eq(bo.getReturnId() != null, ProductionReturnItemBatch::getReturnId, bo.getReturnId());
        lqw.eq(bo.getInventoryBatchId() != null, ProductionReturnItemBatch::getInventoryBatchId, bo.getInventoryBatchId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), ProductionReturnItemBatch::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), ProductionReturnItemBatch::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), ProductionReturnItemBatch::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getProductId() != null, ProductionReturnItemBatch::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductionReturnItemBatch::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), ProductionReturnItemBatch::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, ProductionReturnItemBatch::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), ProductionReturnItemBatch::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), ProductionReturnItemBatch::getUnitName, bo.getUnitName());
        // ✅ 优化：移除数量和价格的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, ProductionReturnItemBatch::getQuantity, bo.getQuantity());
        // 原代码：lqw.eq(bo.getPrice() != null, ProductionReturnItemBatch::getPrice, bo.getPrice());
        // TODO: 如需要可以后续添加数量和价格的范围查询支持
        lqw.eq(bo.getLocationId() != null, ProductionReturnItemBatch::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), ProductionReturnItemBatch::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), ProductionReturnItemBatch::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductionReturnItemBatch::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增生产退料批次明细
     *
     * @param bo 生产退料批次明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProductionReturnItemBatchBo bo) {
        ProductionReturnItemBatch add = MapstructUtils.convert(bo, ProductionReturnItemBatch.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBatchId(add.getBatchId());
        }
        return flag;
    }

    /**
     * 修改生产退料批次明细
     *
     * @param bo 生产退料批次明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProductionReturnItemBatchBo bo) {
        ProductionReturnItemBatch update = MapstructUtils.convert(bo, ProductionReturnItemBatch.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductionReturnItemBatch entity) {
        // 校验必填字段
        if (entity.getItemId() == null) {
            throw new ServiceException("退料明细不能为空");
        }
        if (entity.getInventoryBatchId() == null) {
            throw new ServiceException("库存批次不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("退料数量必须大于0");
        }
        if (StringUtils.isBlank(entity.getInternalBatchNumber())) {
            throw new ServiceException("内部批次号不能为空");
        }
    }

    /**
     * 校验并批量删除生产退料批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验批次明细是否可以删除
            List<ProductionReturnItemBatch> batches = baseMapper.selectByIds(ids);
            for (ProductionReturnItemBatch batch : batches) {
                log.info("删除生产退料批次明细，批次号：{}", batch.getInternalBatchNumber());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
