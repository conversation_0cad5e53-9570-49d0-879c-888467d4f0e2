package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 收款单对象 erp_fin_ar_receipt_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_ar_receipt_order")
public class FinArReceiptOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收款ID
     */
    @TableId(value = "receipt_id")
    private Long receiptId;

    /**
     * 收款编号
     */
    private String receiptCode;

    /**
     * 收款名称
     */
    private String receiptName;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 收款日期
     */
    private Date paymentDate;

    /**
     * 收款金额
     */
    private BigDecimal paymentAmount;

    /**
     * 收款方式
     */
    private String paymentMethod;

    /**
     * 银行交易流水
     */
    private String bankSerialNumber;

    /**
     * 已核销金额
     */
    private BigDecimal appliedAmount;

    /**
     * 未核销金额
     */
    private BigDecimal unappliedAmount;

    /**
     * 收款状态
     */
    private String receiptStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;

    // ==================== 来源关联字段（临时变量） ====================
    // TODO: 待数据库结构完善后，这些字段应该持久化到数据库
    // 用于建立收款单与来源业务单据的关联关系，提升业务追溯能力

    /**
     * 来源订单ID（临时变量）
     * TODO: 需要在数据库中添加 source_order_id BIGINT COMMENT '来源订单ID' 字段
     * 用途：关联销售订单，支持从订单到收款的完整追溯
     */
    @TableField(exist = false)
    private Long sourceOrderId;

    /**
     * 来源订单编号（临时变量）
     * TODO: 需要在数据库中添加 source_order_code VARCHAR(100) COMMENT '来源订单编号' 字段
     * 用途：冗余存储订单编号，提升查询性能和历史数据稳定性
     */
    @TableField(exist = false)
    private String sourceOrderCode;

    /**
     * 来源订单类型（临时变量）
     * TODO: 需要在数据库中添加 source_order_type VARCHAR(50) DEFAULT 'SALE_ORDER' COMMENT '来源订单类型' 字段
     * 用途：支持多种来源类型（销售订单、服务订单等），提升系统扩展性
     * 枚举值：SALE_ORDER(销售订单)、SERVICE_ORDER(服务订单)、OTHER(其他)
     */
    @TableField(exist = false)
    private String sourceOrderType;

    /**
     * 来源订单名称（临时变量）
     * TODO: 需要在数据库中添加 source_order_name VARCHAR(200) COMMENT '来源订单名称' 字段
     * 用途：冗余存储订单名称，便于业务人员快速识别
     */
    @TableField(exist = false)
    private String sourceOrderName;

    // ==================== 经办人信息字段（临时变量） ====================
    // TODO: 待数据库结构完善后，这些字段应该持久化到数据库
    // 用于记录收款单的经办人信息，提升审计追踪能力

    /**
     * 经办人ID（临时变量）
     * TODO: 需要在数据库中添加 handler_id BIGINT COMMENT '经办人ID' 字段
     * 用途：记录收款单的实际经办人，用于责任追踪和审计
     */
    @TableField(exist = false)
    private Long handlerId;

    /**
     * 经办人姓名（临时变量）
     * TODO: 需要在数据库中添加 handler_name VARCHAR(100) COMMENT '经办人姓名' 字段
     * 用途：冗余存储经办人姓名，提升查询性能和历史数据稳定性
     */
    @TableField(exist = false)
    private String handlerName;

    /**
     * 经办时间（临时变量）
     * TODO: 需要在数据库中添加 handle_time DATETIME COMMENT '经办时间' 字段
     * 用途：记录收款单的实际经办时间，用于业务分析和审计
     */
    @TableField(exist = false)
    private LocalDateTime handleTime;


}
