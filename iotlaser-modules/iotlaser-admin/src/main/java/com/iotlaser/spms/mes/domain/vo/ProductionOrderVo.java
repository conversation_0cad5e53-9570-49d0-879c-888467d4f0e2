package com.iotlaser.spms.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.mes.domain.ProductionOrder;
import com.iotlaser.spms.mes.enums.ProductionOrderStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产订单视图对象 mes_production_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductionOrder.class)
public class ProductionOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 生产订单ID
     */
    @ExcelProperty(value = "生产订单ID")
    private Long orderId;

    /**
     * 生产订单编码
     */
    @ExcelProperty(value = "生产订单编码")
    private String orderCode;

    /**
     * 生产订单名称
     */
    @ExcelProperty(value = "生产订单名称")
    private String orderName;

    /**
     * 生产订单类型
     */
    @ExcelProperty(value = "生产订单类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_production_order_type")
    private String orderType;

    /**
     * 销售订单ID
     */
    @ExcelProperty(value = "销售订单ID")
    private Long saleOrderId;

    /**
     * 销售订单编码
     */
    @ExcelProperty(value = "销售订单编码")
    private String saleOrderCode;

    /**
     * 销售订单名称
     */
    @ExcelProperty(value = "销售订单名称")
    private String saleOrderName;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * BOMID
     */
    @ExcelProperty(value = "BOMID")
    private String bomId;

    /**
     * BOM编码
     */
    @ExcelProperty(value = "BOM编码")
    private String bomCode;

    /**
     * BOM名称
     */
    @ExcelProperty(value = "BOM名称")
    private String bomName;

    /**
     * 生产数量
     */
    @ExcelProperty(value = "生产数量")
    private BigDecimal quantity;

    /**
     * 已完成数量
     */
    @ExcelProperty(value = "已完成数量")
    private BigDecimal finishQuantity;

    /**
     * 计划开始时间
     */
    @ExcelProperty(value = "计划开始时间")
    private Date plannedStartTime;

    /**
     * 计划结束时间
     */
    @ExcelProperty(value = "计划结束时间")
    private Date plannedEndTime;

    /**
     * 实际开始时间
     */
    @ExcelProperty(value = "实际开始时间")
    private Date actualStartTime;

    /**
     * 实际完成时间
     */
    @ExcelProperty(value = "实际完成时间")
    private Date actualEndTime;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_production_order_status")
    private ProductionOrderStatus orderStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

}
