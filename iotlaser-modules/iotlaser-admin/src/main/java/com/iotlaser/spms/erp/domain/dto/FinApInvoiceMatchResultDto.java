package com.iotlaser.spms.erp.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 应付发票匹配结果DTO
 * <p>
 * 用于应付发票与采购订单的匹配结果
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FinApInvoiceMatchResultDto {

    /**
     * 采购订单ID
     */
    private Long purchaseOrderId;

    /**
     * 入库单ID
     */
    private Long inboundId;

    /**
     * 匹配金额
     */
    private BigDecimal matchedAmount;

    /**
     * 匹配分数
     */
    private Double matchScore;

    /**
     * 构造方法 - 用于创建匹配结果
     *
     * @param purchaseOrderId 采购订单ID
     * @param inboundId       入库单ID
     * @param matchedAmount   匹配金额
     * @param matchScore      匹配分数
     */
    public static FinApInvoiceMatchResultDto of(Long purchaseOrderId, Long inboundId,
                                                BigDecimal matchedAmount, Double matchScore) {
        return new FinApInvoiceMatchResultDto(purchaseOrderId, inboundId, matchedAmount, matchScore);
    }

    /**
     * 检查是否为有效匹配
     *
     * @return 是否有效匹配
     */
    public boolean isValidMatch() {
        return matchScore != null && matchScore > 0.0 &&
            matchedAmount != null && matchedAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 检查是否为高质量匹配
     *
     * @return 是否高质量匹配
     */
    public boolean isHighQualityMatch() {
        return isValidMatch() && matchScore >= 0.8;
    }
}
