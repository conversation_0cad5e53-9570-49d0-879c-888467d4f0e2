package com.iotlaser.spms.mes.service;

import com.iotlaser.spms.mes.domain.bo.ProductionReturnItemBatchBo;
import com.iotlaser.spms.mes.domain.vo.ProductionReturnItemBatchVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 生产退料批次明细Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
public interface IProductionReturnItemBatchService {

    /**
     * 查询生产退料批次明细
     *
     * @param batchId 主键
     * @return 生产退料批次明细
     */
    ProductionReturnItemBatchVo queryById(Long batchId);

    /**
     * 分页查询生产退料批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产退料批次明细分页列表
     */
    TableDataInfo<ProductionReturnItemBatchVo> queryPageList(ProductionReturnItemBatchBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的生产退料批次明细列表
     *
     * @param bo 查询条件
     * @return 生产退料批次明细列表
     */
    List<ProductionReturnItemBatchVo> queryList(ProductionReturnItemBatchBo bo);

    /**
     * 新增生产退料批次明细
     *
     * @param bo 生产退料批次明细
     * @return 是否新增成功
     */
    Boolean insertByBo(ProductionReturnItemBatchBo bo);

    /**
     * 修改生产退料批次明细
     *
     * @param bo 生产退料批次明细
     * @return 是否修改成功
     */
    Boolean updateByBo(ProductionReturnItemBatchBo bo);

    /**
     * 校验并批量删除生产退料批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
