package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 应收单对象 erp_fin_ar_receivable
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_ar_receivable")
public class FinArReceivable extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 应收ID
     */
    @TableId(value = "receivable_id")
    private Long receivableId;

    /**
     * 应收编号
     */
    private String receivableCode;

    /**
     * 应收名称
     */
    private String receivableName;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 上游来源ID
     */
    private Long directSourceId;

    /**
     * 上游来源编号
     */
    private String directSourceCode;

    /**
     * 上游来源名称
     */
    private String directSourceName;

    /**
     * 上游来源类型
     */
    private String directSourceType;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 来源编号
     */
    private String sourceCode;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 开票日期
     */
    private LocalDate invoiceDate;

    /**
     * 金额（不含税）
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 总税额
     */
    private BigDecimal taxAmount;

    /**
     * 金额（含税）
     */
    private BigDecimal amount;

    /**
     * 应收状态
     */
    private String receivableStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
