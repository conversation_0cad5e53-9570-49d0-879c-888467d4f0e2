package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.PurchaseReturnItemBatch;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购退货批次明细视图对象 erp_purchase_return_item_batch
 *
 * <AUTHOR>
 * @date 2025/05/10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PurchaseReturnItemBatch.class)
public class PurchaseReturnItemBatchVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 批次ID
     */
    @ExcelProperty(value = "批次ID")
    private Long batchId;

    /**
     * 明细ID
     */
    @ExcelProperty(value = "明细ID")
    private Long itemId;

    /**
     * 退货单ID
     */
    @ExcelProperty(value = "退货单ID")
    private Long returnId;

    /**
     * 库存ID
     */
    @ExcelProperty(value = "库存ID")
    private Long inventoryBatchId;

    /**
     * 内部批次号/成品序列号
     */
    @ExcelProperty(value = "内部批次号/成品序列号")
    private String internalBatchNumber;

    /**
     * 供应商批次号
     */
    @ExcelProperty(value = "供应商批次号")
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    @ExcelProperty(value = "单品序列号")
    private String serialNumber;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位ID")
    private Long unitId;

    /**
     * 计量单位编码
     */
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    private String unitName;

    /**
     * 位置库位ID
     */
    @ExcelProperty(value = "位置库位ID")
    private Long locationId;

    /**
     * 位置库位编码
     */
    @ExcelProperty(value = "位置库位编码")
    private String locationCode;

    /**
     * 位置库位名称
     */
    @ExcelProperty(value = "位置库位名称")
    private String locationName;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 单价 (单价（含税）)
     */
    @ExcelProperty(value = "单价（含税）")
    private BigDecimal price;

    /**
     * 单价（不含税）
     */
    @ExcelProperty(value = "单价（不含税）")
    private BigDecimal priceExclusiveTax;

    /**
     * 金额（含税）
     */
    @ExcelProperty(value = "金额（含税）")
    private BigDecimal amount;

    /**
     * 金额（不含税）
     */
    @ExcelProperty(value = "金额（不含税）")
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    @ExcelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

}
