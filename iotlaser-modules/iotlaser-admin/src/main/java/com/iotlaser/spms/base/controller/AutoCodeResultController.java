package com.iotlaser.spms.base.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.base.domain.bo.AutoCodeResultBo;
import com.iotlaser.spms.base.domain.vo.AutoCodeResultVo;
import com.iotlaser.spms.base.service.IAutoCodeResultService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 编码生成记录
 *
 * <AUTHOR> Kai
 * @date 2025/03/11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/base/autoCodeResult")
public class AutoCodeResultController extends BaseController {

    private final IAutoCodeResultService sysAutoCodeResultService;

    /**
     * 查询编码生成记录列表
     */
    @SaCheckPermission("base:autoCodeResult:list")
    @GetMapping("/list")
    public TableDataInfo<AutoCodeResultVo> list(AutoCodeResultBo bo, PageQuery pageQuery) {
        return sysAutoCodeResultService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出编码生成记录列表
     */
    @SaCheckPermission("base:autoCodeResult:export")
    @Log(title = "编码生成记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AutoCodeResultBo bo, HttpServletResponse response) {
        List<AutoCodeResultVo> list = sysAutoCodeResultService.queryList(bo);
        ExcelUtil.exportExcel(list, "编码生成记录", AutoCodeResultVo.class, response);
    }

    /**
     * 获取编码生成记录详细信息
     *
     * @param codeId 主键
     */
    @SaCheckPermission("base:autoCodeResult:query")
    @GetMapping("/{codeId}")
    public R<AutoCodeResultVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long codeId) {
        return R.ok(sysAutoCodeResultService.queryById(codeId));
    }

    /**
     * 新增编码生成记录
     */
    @SaCheckPermission("base:autoCodeResult:add")
    @Log(title = "编码生成记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AutoCodeResultBo bo) {
        return toAjax(sysAutoCodeResultService.insertByBo(bo));
    }

    /**
     * 修改编码生成记录
     */
    @SaCheckPermission("base:autoCodeResult:edit")
    @Log(title = "编码生成记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AutoCodeResultBo bo) {
        return toAjax(sysAutoCodeResultService.updateByBo(bo));
    }

    /**
     * 删除编码生成记录
     *
     * @param codeIds 主键串
     */
    @SaCheckPermission("base:autoCodeResult:remove")
    @Log(title = "编码生成记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{codeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] codeIds) {
        return toAjax(sysAutoCodeResultService.deleteWithValidByIds(List.of(codeIds), true));
    }
}
