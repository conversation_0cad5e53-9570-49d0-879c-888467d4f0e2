package com.iotlaser.spms.mes.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生产订单状态
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Getter
@AllArgsConstructor
public enum ProductionOrderStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "计划员创建，待审核"),
    CONFIRMED("confirmed", "已确认", "订单已确认，待下达"),
    RELEASED("released", "已下达", "订单已下达到车间，可以开始领料和生产"),
    IN_PROGRESS("in_progress", "生产中", "已领料或首道工序已开始"),
    ON_HOLD("on_hold", "挂起", "工单暂停一切活动，不再参与排程，也不能进行报工"),
    PARTIALLY_COMPLETED("partially_completed", "部分完工", "已有部分产成品入库"),
    COMPLETED("completed", "已完工", "订单要求数量的产成品已全部生产入库"),
    CANCELLED("cancelled", "已取消", "系统在执行取消操作时，必须检查工单的当前状态。如果已在IN_PROGRESS，则需要有配套的业务逻辑来处理已投产的在制品和已领用的物料"),
    CLOSED("closed", "已关闭", "订单完工且成本核算等结束，订单归档");

    public final static String DICT_CODE = "mes_production_order_status";
    public final static String DICT_NAME = "生产订单状态";
    public final static String DICT_DESC = "管理生产订单从创建、确认、下达、生产到完工关闭的完整生命周期状态";

    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 生产订单状态枚举
     */
    public static ProductionOrderStatus getByValue(String value) {
        for (ProductionOrderStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
