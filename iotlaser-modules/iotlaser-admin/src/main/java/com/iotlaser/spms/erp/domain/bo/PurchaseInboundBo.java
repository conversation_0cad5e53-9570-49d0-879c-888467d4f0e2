package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 采购入库业务对象 erp_purchase_inbound
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PurchaseInbound.class, reverseConvertGenerate = false)
public class PurchaseInboundBo extends BaseEntity {

    /**
     * 入库单ID
     */
    @NotNull(message = "入库单ID不能为空", groups = {EditGroup.class})
    private Long inboundId;

    /**
     * 入库单编号
     */
    @NotBlank(message = "入库单编号不能为空", groups = {EditGroup.class})
    private String inboundCode;

    /**
     * 入库单名称
     */
    @NotBlank(message = "入库单名称不能为空", groups = {EditGroup.class})
    private String inboundName;

    /**
     * 采购订单ID
     */
    private Long orderId;

    /**
     * 采购订单编码
     */
    private String orderCode;

    /**
     * 采购订单名称
     */
    private String orderName;

    /**
     * 检验单ID
     */
    private Long inspectionId;

    /**
     * 检验单编号
     */
    private String inspectionCode;

    /**
     * 检验单名称
     */
    private String inspectionName;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 入库日期
     */
    private Date inboundDate;

    /**
     * 入库状态
     */
    private PurchaseInboundStatus inboundStatus;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人
     */
    private String applicantName;

    /**
     * 收货负责人ID
     */
    private Long handlerId;

    /**
     * 收货负责人
     */
    private String handlerName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 明细
     */
    private List<PurchaseInboundItemBo> items;
}
