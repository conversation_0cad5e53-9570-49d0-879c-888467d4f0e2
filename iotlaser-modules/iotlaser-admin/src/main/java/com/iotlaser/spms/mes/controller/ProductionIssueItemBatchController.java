package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.ProductionIssueItemBatchBo;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueItemBatchVo;
import com.iotlaser.spms.mes.service.IProductionIssueItemBatchService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产领料批次明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/productionIssueItemBatch")
public class ProductionIssueItemBatchController extends BaseController {

    private final IProductionIssueItemBatchService productionIssueItemBatchService;

    /**
     * 查询生产领料批次明细列表
     */
    @SaCheckPermission("mes:productionIssueItemBatch:list")
    @GetMapping("/list")
    public TableDataInfo<ProductionIssueItemBatchVo> list(ProductionIssueItemBatchBo bo, PageQuery pageQuery) {
        return productionIssueItemBatchService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产领料批次明细列表
     */
    @SaCheckPermission("mes:productionIssueItemBatch:export")
    @Log(title = "生产领料批次明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductionIssueItemBatchBo bo, HttpServletResponse response) {
        List<ProductionIssueItemBatchVo> list = productionIssueItemBatchService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产领料批次明细", ProductionIssueItemBatchVo.class, response);
    }

    /**
     * 获取生产领料批次明细详细信息
     *
     * @param batchId 主键
     */
    @SaCheckPermission("mes:productionIssueItemBatch:query")
    @GetMapping("/{batchId}")
    public R<ProductionIssueItemBatchVo> getInfo(@NotNull(message = "主键不能为空")
                                                 @PathVariable Long batchId) {
        return R.ok(productionIssueItemBatchService.queryById(batchId));
    }

    /**
     * 新增生产领料批次明细
     */
    @SaCheckPermission("mes:productionIssueItemBatch:add")
    @Log(title = "生产领料批次明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductionIssueItemBatchBo bo) {
        return toAjax(productionIssueItemBatchService.insertByBo(bo));
    }

    /**
     * 修改生产领料批次明细
     */
    @SaCheckPermission("mes:productionIssueItemBatch:edit")
    @Log(title = "生产领料批次明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductionIssueItemBatchBo bo) {
        return toAjax(productionIssueItemBatchService.updateByBo(bo));
    }

    /**
     * 删除生产领料批次明细
     *
     * @param batchIds 主键串
     */
    @SaCheckPermission("mes:productionIssueItemBatch:remove")
    @Log(title = "生产领料批次明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{batchIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] batchIds) {
        return toAjax(productionIssueItemBatchService.deleteWithValidByIds(List.of(batchIds), true));
    }
}
