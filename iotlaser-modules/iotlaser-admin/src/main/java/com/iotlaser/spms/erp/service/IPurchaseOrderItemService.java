package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.PurchaseOrderItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 采购订单明细Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface IPurchaseOrderItemService {

    /**
     * 查询采购订单明细
     *
     * @param itemId 主键
     * @return 采购订单明细
     */
    PurchaseOrderItemVo queryById(Long itemId);

    /**
     * 分页查询采购订单明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购订单明细分页列表
     */
    TableDataInfo<PurchaseOrderItemVo> queryPageList(PurchaseOrderItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的采购订单明细列表
     *
     * @param bo 查询条件
     * @return 采购订单明细列表
     */
    List<PurchaseOrderItemVo> queryList(PurchaseOrderItemBo bo);

    /**
     * 新增采购订单明细
     *
     * @param bo 采购订单明细
     * @return 是否新增成功
     */
    Boolean insertByBo(PurchaseOrderItemBo bo);

    /**
     * 修改采购订单明细
     *
     * @param bo 采购订单明细
     * @return 是否修改成功
     */
    Boolean updateByBo(PurchaseOrderItemBo bo);

    /**
     * 校验并批量删除采购订单明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 查询采购订单明细表及其关联信息
     *
     * @param itemId 主键
     * @return 采购订单明细表
     */
    PurchaseOrderItemVo queryByIdWith(Long itemId);

    /**
     * 分页查询采购订单明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购订单明细表分页列表
     */
    TableDataInfo<PurchaseOrderItemVo> queryPageListWith(PurchaseOrderItemBo bo, PageQuery pageQuery);

    /**
     * 批量新增或更新采购订单明细
     *
     * @param items 采购订单明细列表
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<PurchaseOrderItemBo> items);

    /**
     * 根据订单ID查询明细列表
     *
     * @param orderId 订单ID
     * @return 明细列表
     */
    List<PurchaseOrderItemVo> queryByOrderId(Long orderId);

    /**
     * 根据订单ID查询明细列表（内部使用）
     * ✅ 修正：返回VO而非Entity，用于内部业务逻辑
     *
     * @param orderId 订单ID
     * @return 明细VO列表
     */
    List<PurchaseOrderItemVo> selectListByOrderId(Long orderId);

    /**
     * 根据BO更新明细Entity（兼容性方法）
     * ✅ 修正：使用BO进行更新，内部转换为Entity
     *
     * @param bo 明细Bo
     * @return 是否更新成功
     */
    Boolean updateByBoEntity(PurchaseOrderItemBo bo);

}
