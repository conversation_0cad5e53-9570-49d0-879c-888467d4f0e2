package com.iotlaser.spms.mes.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.mes.enums.ProductionInboundStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 生产入库对象 mes_production_inbound
 *
 * <AUTHOR> Kai
 * @date 2025/05/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mes_production_inbound")
public class ProductionInbound extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 入库单ID
     */
    @TableId(value = "inbound_id")
    private Long inboundId;

    /**
     * 入库单编号
     */
    private String inboundCode;

    /**
     * 入库单名称
     */
    private String inboundName;

    /**
     * 生产订单ID
     */
    private Long orderId;

    /**
     * 生产订单编码
     */
    private String orderCode;

    /**
     * 生产订单名称
     */
    private String orderName;

    /**
     * 检验单ID
     */
    private Long inspectionId;

    /**
     * 检验单编号
     */
    private String inspectionCode;

    /**
     * 检验单名称
     */
    private String inspectionName;

    /**
     * 入库时间
     */
    private Date inboundTime;

    /**
     * 入库状态
     */
    private ProductionInboundStatus inboundStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
