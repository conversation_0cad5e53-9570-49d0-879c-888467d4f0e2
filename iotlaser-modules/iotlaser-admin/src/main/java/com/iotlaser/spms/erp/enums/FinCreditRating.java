package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 信用评级枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinCreditRating implements IDictEnum<String> {

    AAA("aaa", "AAA级", "信用极好，风险极低", 1000000, 0.01),
    AA("aa", "AA级", "信用很好，风险很低", 800000, 0.02),
    A("a", "A级", "信用良好，风险较低", 600000, 0.05),
    BBB("bbb", "BBB级", "信用一般，风险中等", 400000, 0.10),
    BB("bb", "BB级", "信用较差，风险较高", 200000, 0.15),
    B("b", "B级", "信用差，风险高", 100000, 0.25),
    CCC("ccc", "CCC级", "信用很差，风险很高", 50000, 0.40),
    CC("cc", "CC级", "信用极差，风险极高", 20000, 0.60),
    C("c", "C级", "几乎无信用，风险极高", 10000, 0.80),
    D("d", "D级", "无信用，禁止交易", 0, 1.00);

    /**
     * 评级值
     */
    @EnumValue
    private final String value;

    /**
     * 评级名称
     */
    private final String name;

    /**
     * 评级描述
     */
    private final String desc;

    /**
     * 建议信用额度
     */
    private final int suggestedCreditLimit;

    /**
     * 风险系数（0-1）
     */
    private final double riskFactor;

    /**
     * 根据值获取枚举
     *
     * @param value 评级值
     * @return 信用评级枚举
     */
    public static FinCreditRating getByValue(String value) {
        for (FinCreditRating creditRating : values()) {
            if (creditRating.getValue().equals(value)) {
                return creditRating;
            }
        }
        return null;
    }

    /**
     * 判断是否为投资级别
     *
     * @return 是否为投资级别
     */
    public boolean isInvestmentGrade() {
        return this.ordinal() <= BBB.ordinal();
    }

    /**
     * 判断是否为投机级别
     *
     * @return 是否为投机级别
     */
    public boolean isSpeculativeGrade() {
        return this.ordinal() > BBB.ordinal() && this.ordinal() < D.ordinal();
    }

    /**
     * 判断是否为违约级别
     *
     * @return 是否为违约级别
     */
    public boolean isDefaultGrade() {
        return this == D;
    }

    /**
     * 判断是否可以交易
     *
     * @return 是否可以交易
     */
    public boolean canTrade() {
        return this != D;
    }

    /**
     * 获取付款期限（天）
     *
     * @return 付款期限
     */
    public int getPaymentTerms() {
        switch (this) {
            case AAA:
            case AA:
                return 60; // 60天
            case A:
                return 45; // 45天
            case BBB:
                return 30; // 30天
            case BB:
                return 15; // 15天
            case B:
                return 7; // 7天
            case CCC:
            case CC:
            case C:
                return 0; // 现款现货
            case D:
            default:
                return -1; // 禁止交易
        }
    }

    /**
     * 获取下一个更好的评级
     *
     * @return 下一个更好的评级，如果已是最高则返回null
     */
    public FinCreditRating getNextBetterRating() {
        int currentIndex = this.ordinal();
        if (currentIndex > 0) {
            return values()[currentIndex - 1];
        }
        return null;
    }

    /**
     * 获取下一个更差的评级
     *
     * @return 下一个更差的评级，如果已是最低则返回null
     */
    public FinCreditRating getNextWorseRating() {
        int currentIndex = this.ordinal();
        if (currentIndex < values().length - 1) {
            return values()[currentIndex + 1];
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return "";
    }
}
