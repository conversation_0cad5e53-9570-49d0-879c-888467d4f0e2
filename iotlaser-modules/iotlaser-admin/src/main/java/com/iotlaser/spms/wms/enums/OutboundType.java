package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 出库类型枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Getter
@AllArgsConstructor
public enum OutboundType implements IDictEnum<String> {

    PURCHASE_RETURN_OUTBOUND("purchase_return_outbound", "采购退货出库", "采购退货单出库"),
    PRODUCTION_ISSUE_OUTBOUND("production_issue_outbound", "生产领料出库", "生产领料单出库"),
    SALE_OUTBOUND("sale_outbound", "销售出库", "销售出库单出库"),
    TRANSFER_OUTBOUND("transfer_outbound", "移库出库", "库位移库出库"),
    OTHER_OUTBOUND("other_outbound", "其他出库", "其他原因出库");

    public final static String DICT_CODE = "wms_outbound_type";
    public final static String DICT_NAME = "出库类型";
    public final static String DICT_DESC = "定义不同业务场景的出库类型，包括销售出库、生产领料、退货出库等";

    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 出库类型枚举
     */
    public static OutboundType getByValue(String value) {
        for (OutboundType type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
