package com.iotlaser.spms.base.domain.bo;

import com.iotlaser.spms.base.domain.MeasureUnit;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 计量单位业务对象 base_measure_unit
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MeasureUnit.class, reverseConvertGenerate = false)
public class MeasureUnitBo extends BaseEntity {

    /**
     * 单位ID
     */
    @NotNull(message = "单位ID不能为空", groups = {EditGroup.class})
    private Long unitId;

    /**
     * 上级节点
     */
    @NotNull(message = "上级节点不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long parentId;

    /**
     * 单位编码
     */
    @NotBlank(message = "单位编码不能为空", groups = {EditGroup.class})
    private String unitCode;

    /**
     * 单位名称
     */
    @NotBlank(message = "单位名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String unitName;

    /**
     * 换算比例
     */
    private BigDecimal unitRatio;

    /**
     * 是否主单位
     */
    @NotBlank(message = "是否主单位不能为空", groups = {AddGroup.class, EditGroup.class})
    private String primaryFlag;

    /**
     * 排列顺序
     */
    private Long orderNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

}
