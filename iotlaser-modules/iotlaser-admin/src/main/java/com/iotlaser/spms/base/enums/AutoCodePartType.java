package com.iotlaser.spms.base.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 编码生成规则组成类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Getter
@AllArgsConstructor
public enum AutoCodePartType implements IDictEnum<String> {

    FIXED("fixed", "固定字符", "固定不变的字符串"),
    DATE("date", "日期时间", "按指定格式的日期时间"),
    SERIAL("serial", "流水号", "自增的流水号"),
    INPUT("input", "输入字符", "用户输入的字符"),
    COMPANY("company", "公司编码", "当前公司的编码"),
    DEPT("dept", "部门编码", "当前部门的编码"),
    USER("user", "用户编码", "当前用户的编码"),
    RANDOM("random", "随机字符", "随机生成的字符"),
    CUSTOM("custom", "自定义", "自定义规则生成");

    public final static String DICT_CODE = "base_auto_code_part_type";
    public final static String DICT_NAME = "编码规则组成类型";
    public final static String DICT_DESC = "定义自动编码生成规则中各个组成部分的类型，包括固定字符、日期时间、流水号等";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 编码部件类型枚举
     */
    public static AutoCodePartType getByValue(String value) {
        for (AutoCodePartType partType : values()) {
            if (partType.getValue().equals(value)) {
                return partType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
