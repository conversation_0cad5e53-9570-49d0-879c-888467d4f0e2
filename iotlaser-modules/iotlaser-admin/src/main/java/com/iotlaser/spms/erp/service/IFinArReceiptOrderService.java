package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinArReceiptOrderBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptOrderVo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 收款单Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-18
 */
public interface IFinArReceiptOrderService {

    /**
     * 查询收款单
     *
     * @param receiptId 主键
     * @return 收款单
     */
    FinArReceiptOrderVo queryById(Long receiptId);

    /**
     * 分页查询收款单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 收款单分页列表
     */
    TableDataInfo<FinArReceiptOrderVo> queryPageList(FinArReceiptOrderBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的收款单列表
     *
     * @param bo 查询条件
     * @return 收款单列表
     */
    List<FinArReceiptOrderVo> queryList(FinArReceiptOrderBo bo);

    /**
     * 新增收款单
     *
     * @param bo 收款单
     * @return 是否新增成功
     */
    Boolean insertByBo(FinArReceiptOrderBo bo);

    /**
     * 修改收款单
     *
     * @param bo 收款单
     * @return 是否修改成功
     */
    Boolean updateByBo(FinArReceiptOrderBo bo);

    /**
     * 校验并批量删除收款单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 收款单核销到应收账款
     *
     * @param receiptOrderId 收款单ID
     * @param receivableId   应收账款ID
     * @param writeoffAmount 核销金额
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 是否核销成功
     */
    Boolean applyToReceivable(Long receiptOrderId, Long receivableId, BigDecimal writeoffAmount,
                              Long operatorId, String operatorName);

    /**
     * 批量核销收款单到应收账款
     *
     * @param writeoffItems 核销明细列表
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 批量核销结果
     */
    Map<String, Object> batchApplyToReceivables(List<WriteoffItem> writeoffItems,
                                                Long operatorId, String operatorName);

    /**
     * 撤销收款核销
     *
     * @param writeoffId   核销记录ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否撤销成功
     */
    Boolean cancelWriteoff(Long writeoffId, Long operatorId, String operatorName);

    /**
     * 查询收款单的核销记录
     *
     * @param receiptOrderId 收款单ID
     * @return 核销记录列表
     */
    List<Map<String, Object>> getWriteoffRecords(Long receiptOrderId);

    /**
     * 获取可核销的应收账款列表
     *
     * @param customerId 客户ID
     * @param amount     收款金额
     * @return 可核销的应收账款列表
     */
    List<FinArReceivableVo> getWriteoffableReceivables(Long customerId, BigDecimal amount);

    /**
     * 更新收款单状态
     *
     * @param receiptOrderId 收款单ID
     * @param newStatus      新状态
     * @return 是否更新成功
     */
    Boolean updateReceiptOrderStatus(Long receiptOrderId, String newStatus);

    /**
     * 核销明细内部类
     */
    class WriteoffItem {
        private Long receiptOrderId;
        private Long receivableId;
        private BigDecimal writeoffAmount;
        private String remark;

        // getters and setters
        public Long getReceiptOrderId() {
            return receiptOrderId;
        }

        public void setReceiptOrderId(Long receiptOrderId) {
            this.receiptOrderId = receiptOrderId;
        }

        public Long getReceivableId() {
            return receivableId;
        }

        public void setReceivableId(Long receivableId) {
            this.receivableId = receivableId;
        }

        public BigDecimal getWriteoffAmount() {
            return writeoffAmount;
        }

        public void setWriteoffAmount(BigDecimal writeoffAmount) {
            this.writeoffAmount = writeoffAmount;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
}
