package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 缺料分析结果VO
 *
 * <AUTHOR> Assistant
 * @date 2025-06-24
 */
@Data
@ExcelIgnoreUnannotated
public class MaterialShortageVo {

    /**
     * 原材料ID
     */
    private Long materialId;

    /**
     * 原材料编码
     */
    @ExcelProperty(value = "原材料编码")
    private String materialCode;

    /**
     * 原材料名称
     */
    @ExcelProperty(value = "原材料名称")
    private String materialName;

    /**
     * 规格型号
     */
    @ExcelProperty(value = "规格型号")
    private String specification;

    /**
     * 单位ID
     */
    private Long unitId;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 单位名称
     */
    @ExcelProperty(value = "单位")
    private String unitName;

    /**
     * 需求数量
     */
    @ExcelProperty(value = "需求数量")
    private BigDecimal requiredQuantity;

    /**
     * 当前库存量
     */
    @ExcelProperty(value = "当前库存量")
    private BigDecimal currentStock;

    /**
     * 可用库存量
     */
    @ExcelProperty(value = "可用库存量")
    private BigDecimal availableStock;

    /**
     * 在途数量
     */
    @ExcelProperty(value = "在途数量")
    private BigDecimal inTransitQuantity;

    /**
     * 缺料数量
     */
    @ExcelProperty(value = "缺料数量")
    private BigDecimal shortageQuantity;

    /**
     * 缺料率（缺料数量/需求数量）
     */
    @ExcelProperty(value = "缺料率")
    private BigDecimal shortageRate;

    /**
     * 安全库存量
     */
    @ExcelProperty(value = "安全库存量")
    private BigDecimal safetyStock;

    /**
     * 缺料类型：ABSOLUTE(绝对缺料)、SAFETY(安全库存不足)、WARNING(预警)
     */
    @ExcelProperty(value = "缺料类型")
    private String shortageType;

    /**
     * 紧急程度：CRITICAL(严重)、HIGH(高)、MEDIUM(中)、LOW(低)
     */
    @ExcelProperty(value = "紧急程度")
    private String urgencyLevel;

    /**
     * ABC分类
     */
    @ExcelProperty(value = "ABC分类")
    private String abcCategory;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "主要供应商")
    private String supplierName;

    /**
     * 供应商交期（天）
     */
    @ExcelProperty(value = "供应商交期")
    private Integer supplierLeadTime;

    /**
     * 最后采购价格
     */
    @ExcelProperty(value = "最后采购价格")
    private BigDecimal lastPurchasePrice;

    /**
     * 预估采购金额
     */
    @ExcelProperty(value = "预估采购金额")
    private BigDecimal estimatedPurchaseAmount;

    /**
     * 最小采购量
     */
    @ExcelProperty(value = "最小采购量")
    private BigDecimal minPurchaseQuantity;

    /**
     * 包装规格
     */
    @ExcelProperty(value = "包装规格")
    private BigDecimal packageSize;

    /**
     * 库存周转率
     */
    @ExcelProperty(value = "库存周转率")
    private BigDecimal inventoryTurnover;

    /**
     * 最后入库时间
     */
    @ExcelProperty(value = "最后入库时间")
    private LocalDate lastInboundDate;

    /**
     * 最后出库时间
     */
    @ExcelProperty(value = "最后出库时间")
    private LocalDate lastOutboundDate;

    /**
     * 建议采购数量
     */
    @ExcelProperty(value = "建议采购数量")
    private BigDecimal suggestedPurchaseQuantity;

    /**
     * 建议交期
     */
    @ExcelProperty(value = "建议交期")
    private LocalDate suggestedDeliveryDate;

    /**
     * 影响的BOM清单
     */
    private String affectedBoms;

    /**
     * 影响的生产计划
     */
    private String affectedProductions;

    /**
     * 缺料原因分析
     */
    private String shortageReason;

    /**
     * 解决方案建议
     */
    private String solutionSuggestion;

    /**
     * 分析时间
     */
    @ExcelProperty(value = "分析时间")
    private LocalDateTime analysisTime;

    /**
     * 分析人员
     */
    @ExcelProperty(value = "分析人员")
    private String analysisByName;

    /**
     * 备注
     */
    private String remark;
}


