package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinArReceivable;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 应收单业务对象 erp_fin_ar_receivable
 *
 * <AUTHOR> Kai
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinArReceivable.class, reverseConvertGenerate = false)
public class FinArReceivableBo extends BaseEntity {

    /**
     * 应收ID
     */
    @NotNull(message = "应收ID不能为空", groups = {EditGroup.class})
    private Long receivableId;

    /**
     * 应收编号
     */
    private String receivableCode;

    /**
     * 应收名称
     */
    private String receivableName;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 上游来源ID
     */
    private Long directSourceId;

    /**
     * 上游来源编号
     */
    private String directSourceCode;

    /**
     * 上游来源名称
     */
    private String directSourceName;

    /**
     * 上游来源类型
     */
    private String directSourceType;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 来源编号
     */
    private String sourceCode;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 开票日期
     */
    private LocalDate invoiceDate;

    /**
     * 金额（不含税）
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 总税额
     */
    private BigDecimal taxAmount;

    /**
     * 金额（含税）
     */
    private BigDecimal amount;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人
     */
    private String applicantName;

    /**
     * 经办人ID
     */
    private Long handlerId;

    /**
     * 经办人
     */
    private String handlerName;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审批人
     */
    private String approverName;

    /**
     * 应收状态
     */
    private String receivableStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
