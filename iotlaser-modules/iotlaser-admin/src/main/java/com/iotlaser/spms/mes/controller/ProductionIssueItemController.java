package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.ProductionIssueItemBo;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueItemVo;
import com.iotlaser.spms.mes.service.IProductionIssueItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产领料明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/productionIssueItem")
public class ProductionIssueItemController extends BaseController {

    private final IProductionIssueItemService productionIssueItemService;

    /**
     * 查询生产领料明细列表
     */
    @SaCheckPermission("mes:productionIssueItem:list")
    @GetMapping("/list")
    public TableDataInfo<ProductionIssueItemVo> list(ProductionIssueItemBo bo, PageQuery pageQuery) {
        return productionIssueItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产领料明细列表
     */
    @SaCheckPermission("mes:productionIssueItem:export")
    @Log(title = "生产领料明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductionIssueItemBo bo, HttpServletResponse response) {
        List<ProductionIssueItemVo> list = productionIssueItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产领料明细", ProductionIssueItemVo.class, response);
    }

    /**
     * 获取生产领料明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("mes:productionIssueItem:query")
    @GetMapping("/{itemId}")
    public R<ProductionIssueItemVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long itemId) {
        return R.ok(productionIssueItemService.queryById(itemId));
    }

    /**
     * 新增生产领料明细
     */
    @SaCheckPermission("mes:productionIssueItem:add")
    @Log(title = "生产领料明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductionIssueItemBo bo) {
        return toAjax(productionIssueItemService.insertByBo(bo));
    }

    /**
     * 修改生产领料明细
     */
    @SaCheckPermission("mes:productionIssueItem:edit")
    @Log(title = "生产领料明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductionIssueItemBo bo) {
        return toAjax(productionIssueItemService.updateByBo(bo));
    }

    /**
     * 删除生产领料明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("mes:productionIssueItem:remove")
    @Log(title = "生产领料明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(productionIssueItemService.deleteWithValidByIds(List.of(itemIds), true));
    }

    /**
     * 获取生产领料明细表以及关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:productionIssueItem:query")
    @GetMapping("with/{id}")
    public R<ProductionIssueItemVo> queryByIdWith(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(productionIssueItemService.queryByIdWith(id));
    }

    /**
     * 查询生产领料明细表列表以及关联详细信息
     */
    @SaCheckPermission("mes:productionIssueItem:list")
    @GetMapping("with/list")
    public TableDataInfo<ProductionIssueItemVo> queryPageListWith(ProductionIssueItemBo bo, PageQuery pageQuery) {
        return productionIssueItemService.queryPageListWith(bo, pageQuery);
    }


}
