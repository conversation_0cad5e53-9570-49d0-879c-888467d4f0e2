package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 核销状态枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinApplyStatus implements IDictEnum<String> {

    PENDING("pending", "待核销", "等待进行核销操作"),
    APPLIED("applied", "已核销", "核销操作已完成"),
    PARTIALLY_APPLIED("partially_applied", "部分核销", "部分金额已核销"),
    REVERSED("reversed", "已撤销", "核销操作已撤销"),
    FAILED("failed", "核销失败", "核销操作失败");

    public final static String DICT_CODE = "erp_fin_apply_status";
    public final static String DICT_NAME = "核销状态";
    public final static String DICT_DESC = "管理财务核销操作的执行状态，包括待核销、已核销、部分核销等处理状态";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 核销状态枚举
     */
    public static FinApplyStatus getByValue(String value) {
        for (FinApplyStatus applyStatus : values()) {
            if (applyStatus.getValue().equals(value)) {
                return applyStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否可以撤销
     *
     * @return 是否可以撤销
     */
    public boolean isReversible() {
        return this == APPLIED || this == PARTIALLY_APPLIED;
    }

    /**
     * 判断是否已完成
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return this == APPLIED || this == REVERSED || this == FAILED;
    }

    /**
     * 判断是否可以继续核销
     *
     * @return 是否可以继续核销
     */
    public boolean canContinueApply() {
        return this == PENDING || this == PARTIALLY_APPLIED;
    }

    /**
     * 获取下一个可能的状态
     *
     * @return 下一个可能的状态列表
     */
    public FinApplyStatus[] getNextPossibleStates() {
        switch (this) {
            case PENDING:
                return new FinApplyStatus[]{APPLIED, PARTIALLY_APPLIED, FAILED};
            case PARTIALLY_APPLIED:
                return new FinApplyStatus[]{APPLIED, REVERSED};
            case APPLIED:
                return new FinApplyStatus[]{REVERSED};
            case REVERSED:
            case FAILED:
            default:
                return new FinApplyStatus[]{};
        }
    }
}
