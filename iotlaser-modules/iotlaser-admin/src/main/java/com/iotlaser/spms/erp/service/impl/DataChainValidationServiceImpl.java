package com.iotlaser.spms.erp.service.impl;

import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderItemVo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderVo;
import com.iotlaser.spms.erp.service.*;
import com.iotlaser.spms.erp.utils.AmountCalculationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 数据链路验证服务实现
 * 用于验证销售订单到财务对账完整数据链路的数据传递完整性和一致性
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataChainValidationServiceImpl implements IDataChainValidationService {

    private final ISaleOrderService saleOrderService;
    private final ISaleOrderItemService saleOrderItemService;
    private final IFinArReceivableService finArReceivableService;
    private final IFinArReceiptReceivableLinkService finArReceiptReceivableLinkService;

    /**
     * 验证销售订单主表与明细表的金额汇总一致性
     *
     * @param orderId 订单ID
     * @return 验证结果
     */
    @Override
    public DataChainValidationResult validateOrderAmountConsistency(Long orderId) {
        try {
            log.info("开始验证订单金额一致性 - 订单ID: {}", orderId);

            DataChainValidationResult result = new DataChainValidationResult();
            result.setValidationType("ORDER_AMOUNT_CONSISTENCY");
            result.setTargetId(orderId);
            result.setValidationTime(LocalDate.now());

            // 获取订单信息
            SaleOrderVo order = saleOrderService.queryById(orderId);
            if (order == null) {
                result.setValid(false);
                result.addError("订单不存在: " + orderId);
                return result;
            }

            // 获取订单明细
            List<SaleOrderItemVo> items = saleOrderItemService.queryByOrderId(orderId);
            if (items.isEmpty()) {
                result.setValid(false);
                result.addError("订单明细为空: " + orderId);
                return result;
            }

            // 计算明细汇总金额
            BigDecimal calculatedTotalQuantity = items.stream()
                .map(item -> item.getQuantity() != null ? item.getQuantity() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal calculatedTotalAmount = items.stream()
                .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal calculatedTotalAmountExclusiveTax = items.stream()
                .map(item -> item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal calculatedTotalTaxAmount = items.stream()
                .map(item -> item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 验证明细内部一致性
            validateItemInternalConsistency(items, result);

            // 验证汇总金额一致性
            BigDecimal calculatedTotal = calculatedTotalAmountExclusiveTax.add(calculatedTotalTaxAmount);
            BigDecimal difference = calculatedTotalAmount.subtract(calculatedTotal).abs();

            if (AmountCalculationUtils.safeCompare(difference, AmountCalculationUtils.getPrecisionThreshold()) > 0) {
                result.addWarning(String.format("明细汇总金额不一致 - 含税总额: %s, 计算总额: %s, 差异: %s",
                    calculatedTotalAmount, calculatedTotal, difference));
            }

            // 验证主表金额（使用临时变量）
            validateOrderMainTableConsistency(order, calculatedTotalQuantity, calculatedTotalAmount,
                calculatedTotalAmountExclusiveTax, calculatedTotalTaxAmount, result);

            // 记录验证详情
            result.addDetail("订单编号", order.getOrderCode());
            result.addDetail("明细数量", String.valueOf(items.size()));
            result.addDetail("汇总数量", calculatedTotalQuantity.toString());
            result.addDetail("汇总金额(含税)", calculatedTotalAmount.toString());
            result.addDetail("汇总金额(不含税)", calculatedTotalAmountExclusiveTax.toString());
            result.addDetail("汇总税额", calculatedTotalTaxAmount.toString());

            result.setValid(result.getErrors().isEmpty());

            log.info("订单金额一致性验证完成 - 订单: {}, 结果: {}, 错误数: {}, 警告数: {}",
                order.getOrderCode(), result.isValid(), result.getErrors().size(), result.getWarnings().size());

            return result;

        } catch (Exception e) {
            log.error("验证订单金额一致性失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            DataChainValidationResult result = new DataChainValidationResult();
            result.setValid(false);
            result.addError("验证过程异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 验证订单明细与出库单的数量、金额对应关系
     * TODO: 当前缺少出库单实体，暂时验证明细内部的数量状态一致性
     *
     * @param orderId 订单ID
     * @return 验证结果
     */
    @Override
    public DataChainValidationResult validateOrderOutboundConsistency(Long orderId) {
        try {
            log.info("开始验证订单出库一致性 - 订单ID: {}", orderId);

            DataChainValidationResult result = new DataChainValidationResult();
            result.setValidationType("ORDER_OUTBOUND_CONSISTENCY");
            result.setTargetId(orderId);
            result.setValidationTime(LocalDate.now());

            // 获取订单明细
            List<SaleOrderItemVo> items = saleOrderItemService.queryByOrderId(orderId);
            if (items.isEmpty()) {
                result.setValid(false);
                result.addError("订单明细为空: " + orderId);
                return result;
            }

            // 验证数量关系
            for (SaleOrderItemVo item : items) {
                validateItemQuantityConsistency(item, result);
            }

            // TODO: 验证与出库单的对应关系
            // 当出库单实体创建后，添加以下验证：
            // 1. 验证出库数量不超过订单数量
            // 2. 验证出库金额与订单金额的对应关系
            // 3. 验证累计出库数量与明细表shippedQuantity的一致性

            result.addDetail("验证明细数", String.valueOf(items.size()));
            result.addDetail("状态", "TODO: 出库单实体缺失，仅验证明细内部一致性");

            result.setValid(result.getErrors().isEmpty());

            log.info("订单出库一致性验证完成 - 订单ID: {}, 结果: {}", orderId, result.isValid());

            return result;

        } catch (Exception e) {
            log.error("验证订单出库一致性失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            DataChainValidationResult result = new DataChainValidationResult();
            result.setValid(false);
            result.addError("验证过程异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 验证出库单与应收发票的开票依据和金额传递
     * TODO: 当前缺少出库单实体，验证订单与应收发票的关系
     *
     * @param orderId 订单ID
     * @return 验证结果
     */
    @Override
    public DataChainValidationResult validateOutboundInvoiceConsistency(Long orderId) {
        try {
            log.info("开始验证出库开票一致性 - 订单ID: {}", orderId);

            DataChainValidationResult result = new DataChainValidationResult();
            result.setValidationType("OUTBOUND_INVOICE_CONSISTENCY");
            result.setTargetId(orderId);
            result.setValidationTime(LocalDate.now());

            // 获取订单信息
            SaleOrderVo order = saleOrderService.queryById(orderId);
            if (order == null) {
                result.setValid(false);
                result.addError("订单不存在: " + orderId);
                return result;
            }

            // 获取相关应收发票
            List<FinArReceivableVo> receivables = finArReceivableService.queryBySourceId(orderId, "SALE_ORDER");

            if (receivables.isEmpty()) {
                result.addWarning("订单尚未生成应收发票: " + order.getOrderCode());
            } else {
                // 验证应收发票与订单的一致性
                for (FinArReceivableVo receivable : receivables) {
                    validateOrderReceivableConsistency(order, receivable, result);
                }
            }

            // TODO: 验证出库单与应收发票的关系
            // 当出库单实体创建后，添加以下验证：
            // 1. 验证开票依据是否基于出库单
            // 2. 验证开票金额不超过出库金额
            // 3. 验证开票明细与出库明细的对应关系

            result.addDetail("订单编号", order.getOrderCode());
            result.addDetail("应收发票数", String.valueOf(receivables.size()));
            result.addDetail("状态", "TODO: 出库单实体缺失，仅验证订单与应收发票关系");

            result.setValid(result.getErrors().isEmpty());

            log.info("出库开票一致性验证完成 - 订单: {}, 结果: {}", order.getOrderCode(), result.isValid());

            return result;

        } catch (Exception e) {
            log.error("验证出库开票一致性失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            DataChainValidationResult result = new DataChainValidationResult();
            result.setValid(false);
            result.addError("验证过程异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 验证应收发票与对账结果的金额匹配性
     *
     * @param receivableId 应收发票ID
     * @return 验证结果
     */
    @Override
    public DataChainValidationResult validateInvoiceReconciliationConsistency(Long receivableId) {
        try {
            log.info("开始验证发票对账一致性 - 应收发票ID: {}", receivableId);

            DataChainValidationResult result = new DataChainValidationResult();
            result.setValidationType("INVOICE_RECONCILIATION_CONSISTENCY");
            result.setTargetId(receivableId);
            result.setValidationTime(LocalDate.now());

            // 获取应收发票信息
            FinArReceivableVo receivable = finArReceivableService.queryById(receivableId);
            if (receivable == null) {
                result.setValid(false);
                result.addError("应收发票不存在: " + receivableId);
                return result;
            }

            // 计算已核销金额
            BigDecimal appliedAmount = finArReceiptReceivableLinkService.getAppliedAmountByReceivableId(receivableId);
            BigDecimal unpaidAmount = receivable.getAmount().subtract(appliedAmount);

            // 验证金额逻辑
            if (appliedAmount.compareTo(BigDecimal.ZERO) < 0) {
                result.addError("已核销金额不能为负数: " + appliedAmount);
            }

            if (appliedAmount.compareTo(receivable.getAmount()) > 0) {
                result.addError(String.format("已核销金额超过应收金额 - 已核销: %s, 应收: %s",
                    appliedAmount, receivable.getAmount()));
            }

            // 验证状态一致性
            String expectedStatus = determineExpectedReceivableStatus(receivable.getAmount(), appliedAmount);
            if (!expectedStatus.equals(receivable.getReceivableStatus())) {
                result.addWarning(String.format("应收状态可能不一致 - 当前: %s, 预期: %s",
                    receivable.getReceivableStatus(), expectedStatus));
            }

            result.addDetail("应收编号", receivable.getReceivableCode());
            result.addDetail("应收金额", receivable.getAmount().toString());
            result.addDetail("已核销金额", appliedAmount.toString());
            result.addDetail("未收金额", unpaidAmount.toString());
            result.addDetail("当前状态", receivable.getReceivableStatus());
            result.addDetail("预期状态", expectedStatus);

            result.setValid(result.getErrors().isEmpty());

            log.info("发票对账一致性验证完成 - 应收: {}, 结果: {}", receivable.getReceivableCode(), result.isValid());

            return result;

        } catch (Exception e) {
            log.error("验证发票对账一致性失败 - 应收发票ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            DataChainValidationResult result = new DataChainValidationResult();
            result.setValid(false);
            result.addError("验证过程异常: " + e.getMessage());
            return result;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证明细内部一致性
     */
    private void validateItemInternalConsistency(List<SaleOrderItemVo> items, DataChainValidationResult result) {
        for (SaleOrderItemVo item : items) {
            // 验证金额计算一致性
            if (item.getAmount() != null && item.getAmountExclusiveTax() != null && item.getTaxAmount() != null) {
                BigDecimal calculatedAmount = item.getAmountExclusiveTax().add(item.getTaxAmount());

                if (!AmountCalculationUtils.isAmountEqual(item.getAmount(), calculatedAmount)) {
                    result.addWarning(String.format("明细金额计算不一致 - 明细ID: %s, 含税: %s, 计算: %s",
                        item.getItemId(), item.getAmount(), calculatedAmount));
                }
            }

            // 验证数量逻辑
            validateItemQuantityConsistency(item, result);
        }
    }

    /**
     * 验证明细数量一致性
     */
    private void validateItemQuantityConsistency(SaleOrderItemVo item, DataChainValidationResult result) {
        BigDecimal quantity = item.getQuantity() != null ? item.getQuantity() : BigDecimal.ZERO;
        BigDecimal shippedQuantity = item.getShippedQuantity() != null ? item.getShippedQuantity() : BigDecimal.ZERO;
        BigDecimal invoicedQuantity = item.getInvoicedQuantity() != null ? item.getInvoicedQuantity() : BigDecimal.ZERO;

        // 验证数量关系: 订单数量 >= 已发货数量 >= 已开票数量
        if (shippedQuantity.compareTo(quantity) > 0) {
            result.addError(String.format("已发货数量超过订单数量 - 明细ID: %s, 订单: %s, 发货: %s",
                item.getItemId(), quantity, shippedQuantity));
        }

        if (invoicedQuantity.compareTo(shippedQuantity) > 0) {
            result.addError(String.format("已开票数量超过发货数量 - 明细ID: %s, 发货: %s, 开票: %s",
                item.getItemId(), shippedQuantity, invoicedQuantity));
        }
    }

    /**
     * 验证订单与应收发票的一致性
     */
    private void validateOrderReceivableConsistency(SaleOrderVo order, FinArReceivableVo receivable,
                                                    DataChainValidationResult result) {
        // 验证客户一致性
        if (!order.getCustomerId().equals(receivable.getCustomerId())) {
            result.addError(String.format("客户不一致 - 订单客户: %s, 应收客户: %s",
                order.getCustomerName(), receivable.getCustomerName()));
        }

        // 验证来源信息一致性
        if (!order.getOrderId().equals(receivable.getSourceId())) {
            result.addError(String.format("来源订单不一致 - 订单ID: %s, 应收来源ID: %s",
                order.getOrderId(), receivable.getSourceId()));
        }

        if (!order.getOrderCode().equals(receivable.getSourceCode())) {
            result.addError(String.format("来源订单编号不一致 - 订单编号: %s, 应收来源编号: %s",
                order.getOrderCode(), receivable.getSourceCode()));
        }
    }

    /**
     * 验证订单主表与明细汇总的一致性
     */
    private void validateOrderMainTableConsistency(SaleOrderVo order,
                                                   BigDecimal calculatedTotalQuantity,
                                                   BigDecimal calculatedTotalAmount,
                                                   BigDecimal calculatedTotalAmountExclusiveTax,
                                                   BigDecimal calculatedTotalTaxAmount,
                                                   DataChainValidationResult result) {
        // 验证总数量一致性
        if (order.getTotalQuantity() != null) {
            if (!AmountCalculationUtils.isAmountEqual(order.getTotalQuantity(), calculatedTotalQuantity)) {
                result.addError(String.format("主表与明细数量不一致 - 主表: %s, 明细汇总: %s",
                    order.getTotalQuantity(), calculatedTotalQuantity));
            }
        }

        // 验证总金额一致性
        if (order.getTotalAmount() != null) {
            if (!AmountCalculationUtils.isAmountEqual(order.getTotalAmount(), calculatedTotalAmount)) {
                result.addError(String.format("主表与明细金额不一致 - 主表: %s, 明细汇总: %s",
                    order.getTotalAmount(), calculatedTotalAmount));
            }
        }

        // 验证不含税金额一致性
        if (order.getTotalAmountExclusiveTax() != null) {
            if (!AmountCalculationUtils.isAmountEqual(order.getTotalAmountExclusiveTax(), calculatedTotalAmountExclusiveTax)) {
                result.addError(String.format("主表与明细不含税金额不一致 - 主表: %s, 明细汇总: %s",
                    order.getTotalAmountExclusiveTax(), calculatedTotalAmountExclusiveTax));
            }
        }

        // 验证税额一致性
        if (order.getTotalTaxAmount() != null) {
            if (!AmountCalculationUtils.isAmountEqual(order.getTotalTaxAmount(), calculatedTotalTaxAmount)) {
                result.addError(String.format("主表与明细税额不一致 - 主表: %s, 明细汇总: %s",
                    order.getTotalTaxAmount(), calculatedTotalTaxAmount));
            }
        }
    }

    /**
     * 确定预期的应收状态
     */
    private String determineExpectedReceivableStatus(BigDecimal totalAmount, BigDecimal appliedAmount) {
        if (appliedAmount.compareTo(BigDecimal.ZERO) == 0) {
            return "PENDING";
        } else if (appliedAmount.compareTo(totalAmount) >= 0) {
            return "FULLY_PAID";
        } else {
            return "PARTIALLY_PAID";
        }
    }
}
