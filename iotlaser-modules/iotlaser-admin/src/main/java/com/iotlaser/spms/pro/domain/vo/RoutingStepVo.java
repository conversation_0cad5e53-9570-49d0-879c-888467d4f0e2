package com.iotlaser.spms.pro.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.pro.domain.RoutingStep;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;


/**
 * 工艺路线工序视图对象 pro_routing_step
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = RoutingStep.class)
public class RoutingStepVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 步骤ID
     */
    @ExcelProperty(value = "步骤ID")
    private Long stepId;

    /**
     * 路线ID
     */
    @ExcelProperty(value = "路线ID")
    private Long routingId;

    /**
     * 工序ID
     */
    @ExcelProperty(value = "工序ID")
    private Long processId;

    /**
     * 工序编码
     */
    @ExcelProperty(value = "工序编码")
    private String processCode;

    /**
     * 工序名称
     */
    @ExcelProperty(value = "工序名称")
    private String processName;

    /**
     * 下一步骤ID
     */
    @ExcelProperty(value = "下一步骤ID")
    private Long nextStepId;

    /**
     * 返工步骤ID
     */
    @ExcelProperty(value = "返工步骤ID")
    private Long reworkStepId;

    /**
     * 准备时间(分钟)
     */
    @ExcelProperty(value = "准备时间(分钟)")
    private Long setupTime;

    /**
     * 加工时间(分钟)
     */
    @ExcelProperty(value = "加工时间(分钟)")
    private Long processingTime;

    /**
     * 质检要求
     */
    @ExcelProperty(value = "质检要求")
    private String qualityCheckSpecs;

    /**
     * 工作重心
     */
    @ExcelProperty(value = "工作重心", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "pro_routing_step_report_type")
    private String reportType;

    /**
     * 工序顺序
     */
    @ExcelProperty(value = "工序顺序")
    private Long orderNum;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
