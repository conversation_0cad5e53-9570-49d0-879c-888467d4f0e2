package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.mes.domain.ProductionInboundItem;
import com.iotlaser.spms.mes.domain.bo.ProductionInboundItemBo;
import com.iotlaser.spms.mes.domain.vo.ProductionInboundItemVo;
import com.iotlaser.spms.mes.mapper.ProductionInboundItemMapper;
import com.iotlaser.spms.mes.service.IProductionInboundItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 生产入库明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductionInboundItemServiceImpl implements IProductionInboundItemService {

    private final ProductionInboundItemMapper baseMapper;

    /**
     * 查询生产入库明细
     *
     * @param itemId 主键
     * @return 生产入库明细
     */
    @Override
    public ProductionInboundItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询生产入库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产入库明细分页列表
     */
    @Override
    public TableDataInfo<ProductionInboundItemVo> queryPageList(ProductionInboundItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductionInboundItem> lqw = buildQueryWrapper(bo);
        Page<ProductionInboundItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产入库明细列表
     *
     * @param bo 查询条件
     * @return 生产入库明细列表
     */
    @Override
    public List<ProductionInboundItemVo> queryList(ProductionInboundItemBo bo) {
        LambdaQueryWrapper<ProductionInboundItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductionInboundItem> buildQueryWrapper(ProductionInboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductionInboundItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductionInboundItem::getItemId);
        lqw.eq(bo.getInboundId() != null, ProductionInboundItem::getInboundId, bo.getInboundId());
        lqw.eq(bo.getProductId() != null, ProductionInboundItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductionInboundItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), ProductionInboundItem::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, ProductionInboundItem::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), ProductionInboundItem::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), ProductionInboundItem::getUnitName, bo.getUnitName());
        lqw.eq(bo.getLocationId() != null, ProductionInboundItem::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), ProductionInboundItem::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), ProductionInboundItem::getLocationName, bo.getLocationName());
        lqw.eq(bo.getQuantity() != null, ProductionInboundItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getFinishQuantity() != null, ProductionInboundItem::getFinishQuantity, bo.getFinishQuantity());
        lqw.eq(bo.getPrice() != null, ProductionInboundItem::getPrice, bo.getPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductionInboundItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增生产入库明细
     *
     * @param bo 生产入库明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProductionInboundItemBo bo) {
        ProductionInboundItem add = MapstructUtils.convert(bo, ProductionInboundItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改生产入库明细
     *
     * @param bo 生产入库明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProductionInboundItemBo bo) {
        ProductionInboundItem update = MapstructUtils.convert(bo, ProductionInboundItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductionInboundItem entity) {
        // 校验必填字段
        if (entity.getInboundId() == null) {
            throw new ServiceException("生产入库单不能为空");
        }
        if (entity.getProductId() == null) {
            throw new ServiceException("产品不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("入库数量必须大于0");
        }

        // 校验同一入库单中产品不能重复
        if (entity.getInboundId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<ProductionInboundItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ProductionInboundItem::getInboundId, entity.getInboundId());
            wrapper.eq(ProductionInboundItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(ProductionInboundItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一生产入库单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除生产入库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验明细是否可以删除
            List<ProductionInboundItem> items = baseMapper.selectByIds(ids);
            for (ProductionInboundItem item : items) {
                log.info("删除生产入库明细，产品：{}", item.getProductName());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 查询生产入库明细表及其关联信息
     *
     * @param itemId 主键
     * @return 生产入库明细表
     */
    @Override
    public ProductionInboundItemVo queryByIdWith(Long itemId) {
        return baseMapper.queryByIdWith(itemId);
    }

    /**
     * 分页查询生产入库明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产入库明细表分页列表
     */
    @Override
    public TableDataInfo<ProductionInboundItemVo> queryPageListWith(ProductionInboundItemBo bo, PageQuery pageQuery) {
        QueryWrapper<ProductionInboundItem> queryWrapper = buildQueryWrapperWith(bo);
        List<ProductionInboundItemVo> result = baseMapper.queryPageListWith(pageQuery.build(), queryWrapper);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<ProductionInboundItem> buildQueryWrapperWith(ProductionInboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<ProductionInboundItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getInboundId() != null, "item.inbound_id", bo.getInboundId());
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        wrapper.eq(bo.getLocationId() != null, "item.location_id", bo.getLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getLocationCode()), "item.location_code", bo.getLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getLocationName()), "item.location_name", bo.getLocationName());
        wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        wrapper.eq(bo.getFinishQuantity() != null, "item.finish_quantity", bo.getFinishQuantity());
        wrapper.eq(bo.getPrice() != null, "item.price", bo.getPrice());
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }


}
