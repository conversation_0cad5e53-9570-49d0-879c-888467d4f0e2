package com.iotlaser.spms.mes.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.mes.enums.ProductionIssueStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 生产领料对象 mes_production_issue
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mes_production_issue")
public class ProductionIssue extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退货单ID
     */
    @TableId(value = "issue_id")
    private Long issueId;

    /**
     * 退货单编号
     */
    private String issueCode;

    /**
     * 退货单名称
     */
    private String issueName;

    /**
     * 生产订单ID
     */
    private Long orderId;

    /**
     * 生产订单编码
     */
    private String orderCode;

    /**
     * 生产订单名称
     */
    private String orderName;

    /**
     * 检验单ID
     */
    private Long inspectionId;

    /**
     * 检验单编号
     */
    private String inspectionCode;

    /**
     * 检验单名称
     */
    private String inspectionName;

    /**
     * 领料时间
     */
    private Date issueTime;

    /**
     * 领料状态
     */
    private ProductionIssueStatus issueStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 明细
     */
    @TableField(exist = false)
    private List<ProductionIssueItem> items;
}
