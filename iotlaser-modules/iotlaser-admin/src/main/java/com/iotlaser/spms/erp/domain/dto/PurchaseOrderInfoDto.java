package com.iotlaser.spms.erp.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 采购订单信息DTO
 * <p>
 * 用于应付发票生成时的采购订单信息传递
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderInfoDto {

    /**
     * 采购订单ID
     */
    private Long purchaseOrderId;

    /**
     * 采购订单编码
     */
    private String purchaseOrderCode;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 到期日期
     */
    private LocalDate dueDate;

    /**
     * 构造方法 - 用于创建采购订单信息
     *
     * @param purchaseOrderId   采购订单ID
     * @param purchaseOrderCode 采购订单编码
     * @param supplierId        供应商ID
     * @param supplierCode      供应商编码
     * @param supplierName      供应商名称
     * @param amount            订单金额
     * @param dueDate           到期日期
     */
    public static PurchaseOrderInfoDto of(Long purchaseOrderId, String purchaseOrderCode,
                                          Long supplierId, String supplierCode, String supplierName,
                                          BigDecimal amount, LocalDate dueDate) {
        return new PurchaseOrderInfoDto(purchaseOrderId, purchaseOrderCode, supplierId,
            supplierCode, supplierName, amount, dueDate);
    }

    /**
     * 检查订单信息是否完整
     *
     * @return 是否完整
     */
    public boolean isComplete() {
        return purchaseOrderId != null &&
            purchaseOrderCode != null && !purchaseOrderCode.trim().isEmpty() &&
            supplierId != null &&
            supplierCode != null && !supplierCode.trim().isEmpty() &&
            supplierName != null && !supplierName.trim().isEmpty();
    }

    /**
     * 检查是否已过期
     *
     * @return 是否已过期
     */
    public boolean isOverdue() {
        return dueDate != null && dueDate.isBefore(LocalDate.now());
    }

    /**
     * 获取显示名称
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        return String.format("%s - %s", purchaseOrderCode, supplierName);
    }
}
