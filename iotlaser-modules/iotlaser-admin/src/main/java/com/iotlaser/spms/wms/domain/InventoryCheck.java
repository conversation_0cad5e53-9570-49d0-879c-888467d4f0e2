package com.iotlaser.spms.wms.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 库存盘点对象 wms_inventory_check
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_inventory_check")
public class InventoryCheck extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 盘点单ID
     */
    @TableId(value = "check_id")
    private Long checkId;

    /**
     * 盘点单编号
     */
    private String checkCode;

    /**
     * 盘点单名称
     */
    private String checkName;

    /**
     * 盘点类型
     */
    private String checkType;

    /**
     * 盘点范围
     */
    private String checkScope;

    /**
     * 库位ID
     */
    private Long locationId;

    /**
     * 库位编码
     */
    private String locationCode;

    /**
     * 库位名称
     */
    private String locationName;

    /**
     * 计划开始时间
     */
    private Date plannedStartTime;

    /**
     * 计划结束时间
     */
    private Date plannedEndTime;

    /**
     * 实际开始时间
     */
    private Date actualStartTime;

    /**
     * 实际结束时间
     */
    private Date actualEndTime;

    /**
     * 盘点状态
     */
    private String checkStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 盘点明细
     */
    @TableField(exist = false)
    private List<InventoryCheckItem> items;
}
