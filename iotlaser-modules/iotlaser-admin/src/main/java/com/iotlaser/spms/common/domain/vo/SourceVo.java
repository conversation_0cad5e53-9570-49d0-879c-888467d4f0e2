package com.iotlaser.spms.common.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import com.iotlaser.spms.common.domain.SourceItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SourceItem.class)
public class SourceVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    private String code;
    private String name;
    private Date time;
    private String status;
    private List<SourceItemVo> items;
}
