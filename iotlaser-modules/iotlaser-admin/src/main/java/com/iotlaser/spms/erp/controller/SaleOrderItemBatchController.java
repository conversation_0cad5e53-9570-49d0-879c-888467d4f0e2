package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.SaleOrderItemBatchBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderItemBatchVo;
import com.iotlaser.spms.erp.service.ISaleOrderItemBatchService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售订单批次明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/saleOrderItemBatch")
public class SaleOrderItemBatchController extends BaseController {

    private final ISaleOrderItemBatchService saleOrderItemBatchService;

    /**
     * 查询销售订单批次明细列表
     */
    @SaCheckPermission("erp:saleOrderItemBatch:list")
    @GetMapping("/list")
    public TableDataInfo<SaleOrderItemBatchVo> list(SaleOrderItemBatchBo bo, PageQuery pageQuery) {
        return saleOrderItemBatchService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出销售订单批次明细列表
     */
    @SaCheckPermission("erp:saleOrderItemBatch:export")
    @Log(title = "销售订单批次明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SaleOrderItemBatchBo bo, HttpServletResponse response) {
        List<SaleOrderItemBatchVo> list = saleOrderItemBatchService.queryList(bo);
        ExcelUtil.exportExcel(list, "销售订单批次明细", SaleOrderItemBatchVo.class, response);
    }

    /**
     * 获取销售订单批次明细详细信息
     *
     * @param batchId 主键
     */
    @SaCheckPermission("erp:saleOrderItemBatch:query")
    @GetMapping("/{batchId}")
    public R<SaleOrderItemBatchVo> getInfo(@NotNull(message = "主键不能为空")
                                           @PathVariable Long batchId) {
        return R.ok(saleOrderItemBatchService.queryById(batchId));
    }

    /**
     * 新增销售订单批次明细
     */
    @SaCheckPermission("erp:saleOrderItemBatch:add")
    @Log(title = "销售订单批次明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SaleOrderItemBatchBo bo) {
        return toAjax(saleOrderItemBatchService.insertByBo(bo));
    }

    /**
     * 修改销售订单批次明细
     */
    @SaCheckPermission("erp:saleOrderItemBatch:edit")
    @Log(title = "销售订单批次明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SaleOrderItemBatchBo bo) {
        return toAjax(saleOrderItemBatchService.updateByBo(bo));
    }

    /**
     * 删除销售订单批次明细
     *
     * @param batchIds 主键串
     */
    @SaCheckPermission("erp:saleOrderItemBatch:remove")
    @Log(title = "销售订单批次明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{batchIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] batchIds) {
        return toAjax(saleOrderItemBatchService.deleteWithValidByIds(List.of(batchIds), true));
    }
}
