package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.PurchaseReturnItemBatch;
import com.iotlaser.spms.erp.domain.bo.PurchaseReturnItemBatchBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnItemBatchVo;
import com.iotlaser.spms.erp.mapper.PurchaseReturnItemBatchMapper;
import com.iotlaser.spms.erp.service.IPurchaseReturnItemBatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 采购退货批次明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseReturnItemBatchServiceImpl implements IPurchaseReturnItemBatchService {

    private final PurchaseReturnItemBatchMapper baseMapper;

    /**
     * 查询采购退货批次明细
     *
     * @param batchId 主键
     * @return 采购退货批次明细
     */
    @Override
    public PurchaseReturnItemBatchVo queryById(Long batchId) {
        return baseMapper.selectVoById(batchId);
    }

    /**
     * 分页查询采购退货批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购退货批次明细分页列表
     */
    @Override
    public TableDataInfo<PurchaseReturnItemBatchVo> queryPageList(PurchaseReturnItemBatchBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseReturnItemBatch> lqw = buildQueryWrapper(bo);
        Page<PurchaseReturnItemBatchVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购退货批次明细列表
     *
     * @param bo 查询条件
     * @return 采购退货批次明细列表
     */
    @Override
    public List<PurchaseReturnItemBatchVo> queryList(PurchaseReturnItemBatchBo bo) {
        LambdaQueryWrapper<PurchaseReturnItemBatch> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseReturnItemBatch> buildQueryWrapper(PurchaseReturnItemBatchBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseReturnItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseReturnItemBatch::getBatchId);
        lqw.eq(bo.getItemId() != null, PurchaseReturnItemBatch::getItemId, bo.getItemId());
        lqw.eq(bo.getReturnId() != null, PurchaseReturnItemBatch::getReturnId, bo.getReturnId());
        lqw.eq(bo.getInventoryBatchId() != null, PurchaseReturnItemBatch::getInventoryBatchId, bo.getInventoryBatchId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), PurchaseReturnItemBatch::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), PurchaseReturnItemBatch::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), PurchaseReturnItemBatch::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getProductId() != null, PurchaseReturnItemBatch::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), PurchaseReturnItemBatch::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), PurchaseReturnItemBatch::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, PurchaseReturnItemBatch::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), PurchaseReturnItemBatch::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), PurchaseReturnItemBatch::getUnitName, bo.getUnitName());
        // ✅ 优化：移除数量和价格的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, PurchaseReturnItemBatch::getQuantity, bo.getQuantity());
        // 原代码：lqw.eq(bo.getPrice() != null, PurchaseReturnItemBatch::getPrice, bo.getPrice());
        // TODO: 如需要可以后续添加数量和价格的范围查询支持
        lqw.eq(bo.getLocationId() != null, PurchaseReturnItemBatch::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), PurchaseReturnItemBatch::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), PurchaseReturnItemBatch::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseReturnItemBatch::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增采购退货批次明细
     *
     * @param bo 采购退货批次明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PurchaseReturnItemBatchBo bo) {
        PurchaseReturnItemBatch add = MapstructUtils.convert(bo, PurchaseReturnItemBatch.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBatchId(add.getBatchId());
        }
        return flag;
    }

    /**
     * 修改采购退货批次明细
     *
     * @param bo 采购退货批次明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PurchaseReturnItemBatchBo bo) {
        PurchaseReturnItemBatch update = MapstructUtils.convert(bo, PurchaseReturnItemBatch.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PurchaseReturnItemBatch entity) {
        // 校验必填字段
        if (entity.getItemId() == null) {
            throw new ServiceException("退货明细不能为空");
        }
        if (entity.getInventoryBatchId() == null) {
            throw new ServiceException("库存批次不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("退货数量必须大于0");
        }
        if (StringUtils.isBlank(entity.getInternalBatchNumber())) {
            throw new ServiceException("内部批次号不能为空");
        }
        if (entity.getLocationId() == null) {
            throw new ServiceException("库位不能为空");
        }
    }

    /**
     * 校验并批量删除采购退货批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验批次明细是否可以删除
            List<PurchaseReturnItemBatch> batches = baseMapper.selectByIds(ids);
            for (PurchaseReturnItemBatch batch : batches) {
                log.info("删除采购退货批次明细，批次号：{}", batch.getInternalBatchNumber());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
