package com.iotlaser.spms.base.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.base.domain.AutoCodePart;
import com.iotlaser.spms.base.enums.CycleMethod;
import com.iotlaser.spms.base.enums.PartType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;

/**
 * 编码生成规则组成视图对象 sys_auto_code_part
 *
 * <AUTHOR> <PERSON>
 * @date 2025/03/11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AutoCodePart.class)
public class AutoCodePartVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分段ID
     */
    @ExcelProperty(value = "分段ID")
    private Long partId;

    /**
     * 规则ID
     */
    @ExcelProperty(value = "规则ID")
    private Long ruleId;

    /**
     * 分段序号
     */
    @ExcelProperty(value = "分段序号")
    private Long partIndex;

    /**
     * 分段类型
     */
    @ExcelProperty(value = "分段类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_autocode_parttype")
    private PartType partType;

    /**
     * 分段编号
     */
    @ExcelProperty(value = "分段编号")
    private String partCode;

    /**
     * 分段名称
     */
    @ExcelProperty(value = "分段名称")
    private String partName;

    /**
     * 分段长度
     */
    @ExcelProperty(value = "分段长度")
    private Long partLength;

    /**
     * 时间格式
     */
    @ExcelProperty(value = "时间格式")
    private String dateFormat;

    /**
     * 输入字符
     */
    @ExcelProperty(value = "输入字符")
    private String inputCharacter;

    /**
     * 固定字符
     */
    @ExcelProperty(value = "固定字符")
    private String fixCharacter;

    /**
     * 流水号起始值
     */
    @ExcelProperty(value = "流水号起始值")
    private Long seriaStartNo;

    /**
     * 流水号步长
     */
    @ExcelProperty(value = "流水号步长")
    private Long seriaStep;

    /**
     * 流水号当前值
     */
    @ExcelProperty(value = "流水号当前值")
    private Long seriaNowNo;

    /**
     * 流水号是否循环
     */
    @ExcelProperty(value = "流水号是否循环", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String cycleFlag;

    /**
     * 循环方式
     */
    @ExcelProperty(value = "循环方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_autocode_cyclemethod")
    private CycleMethod cycleMethod;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

}
