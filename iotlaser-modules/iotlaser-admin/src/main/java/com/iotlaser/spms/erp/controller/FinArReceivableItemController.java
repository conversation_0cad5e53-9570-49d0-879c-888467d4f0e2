package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinArReceivableItemBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableItemVo;
import com.iotlaser.spms.erp.service.IFinArReceivableItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应收单明细
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finArReceivableItem")
public class FinArReceivableItemController extends BaseController {

    private final IFinArReceivableItemService finArReceivableItemService;

    /**
     * 查询应收单明细列表
     */
    @SaCheckPermission("erp:finArReceivableItem:list")
    @GetMapping("/list")
    public TableDataInfo<FinArReceivableItemVo> list(FinArReceivableItemBo bo, PageQuery pageQuery) {
        return finArReceivableItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出应收单明细列表
     */
    @SaCheckPermission("erp:finArReceivableItem:export")
    @Log(title = "应收单明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinArReceivableItemBo bo, HttpServletResponse response) {
        List<FinArReceivableItemVo> list = finArReceivableItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "应收单明细", FinArReceivableItemVo.class, response);
    }

    /**
     * 获取应收单明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("erp:finArReceivableItem:query")
    @GetMapping("/{itemId}")
    public R<FinArReceivableItemVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long itemId) {
        return R.ok(finArReceivableItemService.queryById(itemId));
    }

    /**
     * 新增应收单明细
     */
    @SaCheckPermission("erp:finArReceivableItem:add")
    @Log(title = "应收单明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinArReceivableItemBo bo) {
        return toAjax(finArReceivableItemService.insertByBo(bo));
    }

    /**
     * 修改应收单明细
     */
    @SaCheckPermission("erp:finArReceivableItem:edit")
    @Log(title = "应收单明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinArReceivableItemBo bo) {
        return toAjax(finArReceivableItemService.updateByBo(bo));
    }

    /**
     * 删除应收单明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("erp:finArReceivableItem:remove")
    @Log(title = "应收单明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(finArReceivableItemService.deleteWithValidByIds(List.of(itemIds), true));
    }
}
