package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.SaleReturn;
import com.iotlaser.spms.erp.enums.SaleReturnStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 销售退货业务对象 erp_sale_return
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SaleReturn.class, reverseConvertGenerate = false)
public class SaleReturnBo extends BaseEntity {

    /**
     * 退货单ID
     */
    @NotNull(message = "退货单ID不能为空", groups = {EditGroup.class})
    private Long returnId;

    /**
     * 退货单编号
     */
    @NotBlank(message = "退货单编号不能为空", groups = {EditGroup.class})
    private String returnCode;

    /**
     * 退货单名称
     */
    private String returnName;

    /**
     * 销售订单ID
     */
    private Long orderId;

    /**
     * 销售订单编号
     */
    private String orderCode;

    /**
     * 销售订单名称
     */
    private String orderName;

    /**
     * 出库单ID
     */
    private Long outboundId;

    /**
     * 出库单编号
     */
    private String outboundCode;

    /**
     * 出库单名称
     */
    private String outboundName;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long customerId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 退货时间
     */
    @NotNull(message = "退货时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date returnTime;

    /**
     * 退货状态
     */
    private SaleReturnStatus returnStatus;

    /**
     * 退货处理人ID
     */
    private Long handlerId;

    /**
     * 退货处理人
     */
    private String handlerName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 明细
     */
    private List<SaleReturnItemBo> items;
}
