package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.erp.enums.PurchaseReturnStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.time.LocalDate;
import java.util.List;

/**
 * 采购退货对象 erp_purchase_return
 *
 * <AUTHOR>
 * @date 2025/05/07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_purchase_return")
public class PurchaseReturn extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退货单ID
     */
    @TableId(value = "return_id")
    private Long returnId;

    /**
     * 退货单编号
     */
    private String returnCode;

    /**
     * 退货单名称
     */
    private String returnName;

    /**
     * 采购订单ID
     */
    private Long orderId;

    /**
     * 采购订单编码
     */
    private String orderCode;

    /**
     * 采购订单名称
     */
    private String orderName;

    /**
     * 入库单ID
     */
    private Long inboundId;

    /**
     * 入库单编号
     */
    private String inboundCode;

    /**
     * 入库单名称
     */
    private String inboundName;

    /**
     * 检验单ID
     */
    private Long inspectionId;

    /**
     * 检验单编号
     */
    private String inspectionCode;

    /**
     * 检验单名称
     */
    private String inspectionName;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 退货日期
     */
    private LocalDate returnDate;

    /**
     * 退货状态
     */
    private PurchaseReturnStatus returnStatus;

    /**
     * 退货申请人ID
     */
    private Long applicantId;

    /**
     * 退货申请人
     */
    private String applicantName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 明细
     */
    @TableField(exist = false)
    private List<PurchaseReturnItem> items;
}
