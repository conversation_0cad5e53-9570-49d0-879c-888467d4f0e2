package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.SaleOrder;
import com.iotlaser.spms.erp.enums.SaleOrderStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 销售订单视图对象 erp_sale_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SaleOrder.class)
public class SaleOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 订单编号
     */
    @ExcelProperty(value = "订单编号")
    private String orderCode;

    /**
     * 订单名称
     */
    @ExcelProperty(value = "订单名称")
    private String orderName;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 客户编码
     */
    @ExcelProperty(value = "客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String customerName;

    /**
     * 下单日期
     */
    @ExcelProperty(value = "下单日期")
    private LocalDate orderDate;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_sale_order_status")
    private SaleOrderStatus orderStatus;

    /**
     * 销售员ID
     */
    @ExcelProperty(value = "销售员ID")
    private Long handlerId;

    /**
     * 销售员
     */
    @ExcelProperty(value = "销售员")
    private String handlerName;

    /**
     * 审批人ID
     */
    @ExcelProperty(value = "审批人ID")
    private Long approverId;

    /**
     * 审批人
     */
    @ExcelProperty(value = "审批人")
    private String approverName;

    /**
     * 审批通过时间
     */
    @ExcelProperty(value = "审批通过时间")
    private LocalDateTime approveTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

    /**
     * 明细
     */
    private List<SaleOrderItemVo> items;

    // ==================== 临时变量：汇总字段 ====================
    // TODO: 待数据库结构完善后，这些字段应该持久化到数据库

    /**
     * 总数量（临时变量）
     */
    @ExcelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    /**
     * 总金额-含税（临时变量）
     */
    @ExcelProperty(value = "总金额(含税)")
    private BigDecimal totalAmount;

    /**
     * 总金额-不含税（临时变量）
     */
    @ExcelProperty(value = "总金额(不含税)")
    private BigDecimal totalAmountExclusiveTax;

    /**
     * 总税额（临时变量）
     */
    @ExcelProperty(value = "总税额")
    private BigDecimal totalTaxAmount;
}
