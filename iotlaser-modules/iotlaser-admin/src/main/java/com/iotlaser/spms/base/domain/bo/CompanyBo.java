package com.iotlaser.spms.base.domain.bo;

import com.iotlaser.spms.base.domain.Company;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 公司信息业务对象 base_company
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Company.class, reverseConvertGenerate = false)
public class CompanyBo extends BaseEntity {

    /**
     * 公司ID
     */
    @NotNull(message = "公司ID不能为空", groups = {EditGroup.class})
    private Long companyId;

    /**
     * 公司类型
     */
    @NotBlank(message = "公司类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String companyType;

    /**
     * 公司编码
     */
    @NotBlank(message = "公司编码不能为空", groups = {EditGroup.class})
    private String companyCode;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String companyName;

    /**
     * 公司简称
     */
    private String shortName;

    /**
     * 公司英文名称
     */
    private String englishName;

    /**
     * 公司简介
     */
    private String intro;

    /**
     * 公司等级
     */
    private String level;

    /**
     * 公司LOGO地址
     */
    private String logo;

    /**
     * 公司地址
     */
    private String address;

    /**
     * 官网地址
     */
    private String website;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 公司电话
     */
    private String tel;

    /**
     * 联系人1
     */
    private String contact1;

    /**
     * 联系人1-电话
     */
    private String contact1Tel;

    /**
     * 联系人1-邮箱
     */
    private String contact1Email;

    /**
     * 联系人2
     */
    private String contact2;

    /**
     * 联系人2-电话
     */
    private String contact2Tel;

    /**
     * 联系人2-邮箱
     */
    private String contact2Email;

    /**
     * 是否供应商
     */
    private String supplierFlag;

    /**
     * 是否客户
     */
    private String customerFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

}
