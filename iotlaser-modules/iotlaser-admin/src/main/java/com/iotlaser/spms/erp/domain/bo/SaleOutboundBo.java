package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDate;
import java.util.List;

/**
 * 销售出库业务对象 erp_sale_outbound
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SaleOutbound.class, reverseConvertGenerate = false)
public class SaleOutboundBo extends BaseEntity {

    /**
     * 出库单ID
     */
    @NotNull(message = "出库单ID不能为空", groups = {EditGroup.class})
    private Long outboundId;

    /**
     * 出库单编号
     */
    @NotBlank(message = "出库单编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String outboundCode;

    /**
     * 出库单名称
     */
    private String outboundName;

    /**
     * 销售订单ID
     */
    private Long orderId;

    /**
     * 销售订单编号
     */
    private String orderCode;

    /**
     * 销售订单名称
     */
    private String orderName;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 出库日期
     */
    private LocalDate outboundDate;

    /**
     * 出库状态
     */
    private SaleOutboundStatus outboundStatus;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人
     */
    private String applicantName;

    /**
     * 发货负责人ID
     */
    private Long handlerId;

    /**
     * 发货负责人
     */
    private String handlerName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 明细
     */
    private List<SaleOutboundItemBo> items;
}
