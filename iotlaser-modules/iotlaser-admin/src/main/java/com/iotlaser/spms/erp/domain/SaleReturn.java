package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.erp.enums.SaleReturnStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 销售退货对象 erp_sale_return
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_sale_return")
public class SaleReturn extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退货单ID
     */
    @TableId(value = "return_id")
    private Long returnId;

    /**
     * 退货单编号
     */
    private String returnCode;

    /**
     * 退货单名称
     */
    private String returnName;

    /**
     * 销售订单ID
     */
    private Long orderId;

    /**
     * 销售订单编号
     */
    private String orderCode;

    /**
     * 销售订单名称
     */
    private String orderName;

    /**
     * 出库单ID
     */
    private Long outboundId;

    /**
     * 出库单编号
     */
    private String outboundCode;

    /**
     * 出库单名称
     */
    private String outboundName;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 退货时间
     */
    private Date returnTime;

    /**
     * 退货状态
     */
    private SaleReturnStatus returnStatus;

    /**
     * 退货处理人ID
     */
    private Long handlerId;

    /**
     * 退货处理人
     */
    private String handlerName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 明细
     */
    @TableField(exist = false)
    private List<SaleReturnItem> items;
}
