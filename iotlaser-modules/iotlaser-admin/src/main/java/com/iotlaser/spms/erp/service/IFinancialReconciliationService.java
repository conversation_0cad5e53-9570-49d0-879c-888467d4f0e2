package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.vo.FinancialReconciliationVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 财务对账服务接口
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
public interface IFinancialReconciliationService {

    /**
     * 对单个销售订单进行财务对账
     *
     * @param orderId 销售订单ID
     * @return 对账结果
     */
    FinancialReconciliationVo reconcileOrder(Long orderId);

    /**
     * 批量对账销售订单
     *
     * @param orderIds 销售订单ID列表
     * @return 对账结果列表
     */
    List<FinancialReconciliationVo> batchReconcileOrders(List<Long> orderIds);

    /**
     * 按客户进行财务对账
     *
     * @param customerId 客户ID
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return 对账结果列表
     */
    List<FinancialReconciliationVo> reconcileByCustomer(Long customerId, LocalDate startDate, LocalDate endDate);

    /**
     * 按日期范围进行财务对账
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param pageQuery 分页参数
     * @return 对账结果分页列表
     */
    TableDataInfo<FinancialReconciliationVo> reconcileByDateRange(LocalDate startDate, LocalDate endDate, PageQuery pageQuery);

    /**
     * 获取对账差异报告
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 差异报告列表
     */
    List<FinancialReconciliationVo> getDifferenceReport(LocalDate startDate, LocalDate endDate);

    /**
     * 计算订单的应收金额
     *
     * @param orderId 销售订单ID
     * @return 应收金额
     */
    BigDecimal calculateOrderReceivableAmount(Long orderId);

    /**
     * 计算订单的已收款金额
     *
     * @param orderId 销售订单ID
     * @return 已收款金额
     */
    BigDecimal calculateOrderReceivedAmount(Long orderId);

    /**
     * 计算订单的已开票金额
     *
     * @param orderId 销售订单ID
     * @return 已开票金额
     */
    BigDecimal calculateOrderInvoicedAmount(Long orderId);

    /**
     * 检查订单是否存在对账差异
     *
     * @param orderId 销售订单ID
     * @return 是否存在差异
     */
    Boolean hasReconciliationDifference(Long orderId);

    /**
     * 获取对账差异详情
     *
     * @param orderId 销售订单ID
     * @return 差异详情列表
     */
    List<FinancialReconciliationVo.ReconciliationDifference> getReconciliationDifferences(Long orderId);

    /**
     * 标记对账差异已处理
     *
     * @param orderId 销售订单ID
     * @param remark  处理备注
     * @return 是否处理成功
     */
    Boolean markDifferenceResolved(Long orderId, String remark);

    /**
     * 获取对账统计信息
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计信息
     */
    ReconciliationStatistics getReconciliationStatistics(LocalDate startDate, LocalDate endDate);

    /**
     * 对账统计信息
     */
    class ReconciliationStatistics {
        /**
         * 总订单数
         */
        private Integer totalOrders;

        /**
         * 对账一致订单数
         */
        private Integer matchedOrders;

        /**
         * 存在差异订单数
         */
        private Integer differenceOrders;

        /**
         * 未对账订单数
         */
        private Integer unreconciledOrders;

        /**
         * 总订单金额
         */
        private BigDecimal totalOrderAmount;

        /**
         * 总已收款金额
         */
        private BigDecimal totalReceivedAmount;

        /**
         * 总已开票金额
         */
        private BigDecimal totalInvoicedAmount;

        /**
         * 总差异金额
         */
        private BigDecimal totalDifferenceAmount;

        // Getters and Setters
        public Integer getTotalOrders() {
            return totalOrders;
        }

        public void setTotalOrders(Integer totalOrders) {
            this.totalOrders = totalOrders;
        }

        public Integer getMatchedOrders() {
            return matchedOrders;
        }

        public void setMatchedOrders(Integer matchedOrders) {
            this.matchedOrders = matchedOrders;
        }

        public Integer getDifferenceOrders() {
            return differenceOrders;
        }

        public void setDifferenceOrders(Integer differenceOrders) {
            this.differenceOrders = differenceOrders;
        }

        public Integer getUnreconciledOrders() {
            return unreconciledOrders;
        }

        public void setUnreconciledOrders(Integer unreconciledOrders) {
            this.unreconciledOrders = unreconciledOrders;
        }

        public BigDecimal getTotalOrderAmount() {
            return totalOrderAmount;
        }

        public void setTotalOrderAmount(BigDecimal totalOrderAmount) {
            this.totalOrderAmount = totalOrderAmount;
        }

        public BigDecimal getTotalReceivedAmount() {
            return totalReceivedAmount;
        }

        public void setTotalReceivedAmount(BigDecimal totalReceivedAmount) {
            this.totalReceivedAmount = totalReceivedAmount;
        }

        public BigDecimal getTotalInvoicedAmount() {
            return totalInvoicedAmount;
        }

        public void setTotalInvoicedAmount(BigDecimal totalInvoicedAmount) {
            this.totalInvoicedAmount = totalInvoicedAmount;
        }

        public BigDecimal getTotalDifferenceAmount() {
            return totalDifferenceAmount;
        }

        public void setTotalDifferenceAmount(BigDecimal totalDifferenceAmount) {
            this.totalDifferenceAmount = totalDifferenceAmount;
        }
    }
}
