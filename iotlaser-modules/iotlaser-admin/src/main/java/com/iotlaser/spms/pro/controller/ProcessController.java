package com.iotlaser.spms.pro.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.pro.domain.bo.ProcessBo;
import com.iotlaser.spms.pro.domain.vo.ProcessVo;
import com.iotlaser.spms.pro.service.IProcessService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工序
 *
 * <AUTHOR> Kai
 * @date 2025-06-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/pro/process")
public class ProcessController extends BaseController {

    private final IProcessService processService;

    /**
     * 查询工序列表
     */
    @SaCheckPermission("pro:process:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessVo> list(ProcessBo bo, PageQuery pageQuery) {
        return processService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出工序列表
     */
    @SaCheckPermission("pro:process:export")
    @Log(title = "工序", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProcessBo bo, HttpServletResponse response) {
        List<ProcessVo> list = processService.queryList(bo);
        ExcelUtil.exportExcel(list, "工序", ProcessVo.class, response);
    }

    /**
     * 获取工序详细信息
     *
     * @param processId 主键
     */
    @SaCheckPermission("pro:process:query")
    @GetMapping("/{processId}")
    public R<ProcessVo> getInfo(@NotNull(message = "主键不能为空")
                                @PathVariable Long processId) {
        return R.ok(processService.queryById(processId));
    }

    /**
     * 新增工序
     */
    @SaCheckPermission("pro:process:add")
    @Log(title = "工序", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProcessBo bo) {
        return toAjax(processService.insertByBo(bo));
    }

    /**
     * 修改工序
     */
    @SaCheckPermission("pro:process:edit")
    @Log(title = "工序", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProcessBo bo) {
        return toAjax(processService.updateByBo(bo));
    }

    /**
     * 删除工序
     *
     * @param processIds 主键串
     */
    @SaCheckPermission("pro:process:remove")
    @Log(title = "工序", businessType = BusinessType.DELETE)
    @DeleteMapping("/{processIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] processIds) {
        return toAjax(processService.deleteWithValidByIds(List.of(processIds), true));
    }
}
