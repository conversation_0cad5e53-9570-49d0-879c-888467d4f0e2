package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.SaleReturn;
import com.iotlaser.spms.erp.enums.SaleReturnStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 销售退货视图对象 erp_sale_return
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SaleReturn.class)
public class SaleReturnVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退货单ID
     */
    @ExcelProperty(value = "退货单ID")
    private Long returnId;

    /**
     * 退货单编号
     */
    @ExcelProperty(value = "退货单编号")
    private String returnCode;

    /**
     * 退货单名称
     */
    @ExcelProperty(value = "退货单名称")
    private String returnName;

    /**
     * 销售订单ID
     */
    @ExcelProperty(value = "销售订单ID")
    private Long orderId;

    /**
     * 销售订单编号
     */
    @ExcelProperty(value = "销售订单编号")
    private String orderCode;

    /**
     * 销售订单名称
     */
    @ExcelProperty(value = "销售订单名称")
    private String orderName;

    /**
     * 出库单ID
     */
    @ExcelProperty(value = "出库单ID")
    private Long outboundId;

    /**
     * 出库单编号
     */
    @ExcelProperty(value = "出库单编号")
    private String outboundCode;

    /**
     * 出库单名称
     */
    @ExcelProperty(value = "出库单名称")
    private String outboundName;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 客户编码
     */
    @ExcelProperty(value = "客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String customerName;

    /**
     * 退货时间
     */
    @ExcelProperty(value = "退货时间")
    private Date returnTime;

    /**
     * 退货状态
     */
    @ExcelProperty(value = "退货状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_sale_return_status")
    private SaleReturnStatus returnStatus;

    /**
     * 退货处理人ID
     */
    @ExcelProperty(value = "退货处理人ID")
    private Long handlerId;

    /**
     * 退货处理人
     */
    @ExcelProperty(value = "退货处理人")
    private String handlerName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

    /**
     * 退货明细
     */
    private List<SaleReturnItemVo> items;
}
