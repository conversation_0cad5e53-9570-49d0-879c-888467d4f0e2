package com.iotlaser.spms.pro.service;

import com.iotlaser.spms.pro.domain.bo.RoutingBo;
import com.iotlaser.spms.pro.domain.vo.RoutingVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 工艺路线Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-15
 */
public interface IRoutingService {

    /**
     * 查询工艺路线
     *
     * @param routingId 主键
     * @return 工艺路线
     */
    RoutingVo queryById(Long routingId);

    /**
     * 分页查询工艺路线列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工艺路线分页列表
     */
    TableDataInfo<RoutingVo> queryPageList(RoutingBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的工艺路线列表
     *
     * @param bo 查询条件
     * @return 工艺路线列表
     */
    List<RoutingVo> queryList(RoutingBo bo);

    /**
     * 新增工艺路线
     *
     * @param bo 工艺路线
     * @return 是否新增成功
     */
    Boolean insertByBo(RoutingBo bo);

    /**
     * 修改工艺路线
     *
     * @param bo 工艺路线
     * @return 是否修改成功
     */
    Boolean updateByBo(RoutingBo bo);

    /**
     * 校验并批量删除工艺路线信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
