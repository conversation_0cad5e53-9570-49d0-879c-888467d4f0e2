package com.iotlaser.spms.mes.service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 生产报工工作流程Service接口
 * 基于README_FLOW.md第7节：生产报工流程
 * <p>
 * 整合生产报工、产品实例、物料消耗的完整业务流程
 *
 * <AUTHOR> <PERSON>
 * @date 2025/06/16
 */
public interface IProductionWorkflowService {

    /**
     * 完整的开工流程
     * 基于README_FLOW.md第7.2节：开工 (首工序)
     *
     * @param orderId       生产订单ID
     * @param productId     产品ID
     * @param routingId     工艺路线ID
     * @param routingStepId 工艺步骤ID
     * @param operatorId    操作员ID
     * @return 工作流程结果
     */
    Map<String, Object> startProductionWorkflow(Long orderId, Long productId, Long routingId,
                                                Long routingStepId, Long operatorId);

    /**
     * 完整的物料消耗流程
     * 基于README_FLOW.md第7.2节：物料消耗 (工序中)
     *
     * @param instanceCode     产品实例编码
     * @param materialConsumes 物料消耗映射 (materialBatchId -> quantity)
     * @param operatorId       操作员ID
     * @return 工作流程结果
     */
    Map<String, Object> materialConsumeWorkflow(String instanceCode,
                                                Map<Long, BigDecimal> materialConsumes,
                                                Long operatorId);

    /**
     * 完整的完工流程
     * 基于README_FLOW.md第7.2节：完工 (当前工序)
     *
     * @param instanceCode  产品实例编码
     * @param routingStepId 工艺步骤ID
     * @param quantityGood  良品数量
     * @param quantityBad   不良品数量
     * @param operatorId    操作员ID
     * @return 工作流程结果
     */
    Map<String, Object> completeProductionWorkflow(String instanceCode, Long routingStepId,
                                                   BigDecimal quantityGood, BigDecimal quantityBad,
                                                   Long operatorId);

    /**
     * 获取产品实例的完整追溯信息
     * 基于README_FLOW.md第7.3节：生产追溯流程（报工与组件消耗）
     *
     * @param instanceCode 产品实例编码
     * @return 完整追溯信息
     */
    Map<String, Object> getCompleteTraceability(String instanceCode);
}
