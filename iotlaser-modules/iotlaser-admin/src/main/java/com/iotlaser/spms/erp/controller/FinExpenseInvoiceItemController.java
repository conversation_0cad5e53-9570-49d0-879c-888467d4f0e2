package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinExpenseInvoiceItemBo;
import com.iotlaser.spms.erp.domain.vo.FinExpenseInvoiceItemVo;
import com.iotlaser.spms.erp.service.IFinExpenseInvoiceItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理费用明细
 *
 * <AUTHOR> Kai
 * @date 2025-06-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finExpenseInvoiceItem")
public class FinExpenseInvoiceItemController extends BaseController {

    private final IFinExpenseInvoiceItemService finExpenseInvoiceItemService;

    /**
     * 查询管理费用明细列表
     */
    @SaCheckPermission("spms/erp:finExpenseInvoiceItem:list")
    @GetMapping("/list")
    public TableDataInfo<FinExpenseInvoiceItemVo> list(FinExpenseInvoiceItemBo bo, PageQuery pageQuery) {
        return finExpenseInvoiceItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出管理费用明细列表
     */
    @SaCheckPermission("spms/erp:finExpenseInvoiceItem:export")
    @Log(title = "管理费用明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinExpenseInvoiceItemBo bo, HttpServletResponse response) {
        List<FinExpenseInvoiceItemVo> list = finExpenseInvoiceItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "管理费用明细", FinExpenseInvoiceItemVo.class, response);
    }

    /**
     * 获取管理费用明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("spms/erp:finExpenseInvoiceItem:query")
    @GetMapping("/{itemId}")
    public R<FinExpenseInvoiceItemVo> getInfo(@NotNull(message = "主键不能为空")
                                              @PathVariable Long itemId) {
        return R.ok(finExpenseInvoiceItemService.queryById(itemId));
    }

    /**
     * 新增管理费用明细
     */
    @SaCheckPermission("spms/erp:finExpenseInvoiceItem:add")
    @Log(title = "管理费用明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinExpenseInvoiceItemBo bo) {
        return toAjax(finExpenseInvoiceItemService.insertByBo(bo));
    }

    /**
     * 修改管理费用明细
     */
    @SaCheckPermission("spms/erp:finExpenseInvoiceItem:edit")
    @Log(title = "管理费用明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinExpenseInvoiceItemBo bo) {
        return toAjax(finExpenseInvoiceItemService.updateByBo(bo));
    }

    /**
     * 删除管理费用明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("spms/erp:finExpenseInvoiceItem:remove")
    @Log(title = "管理费用明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(finExpenseInvoiceItemService.deleteWithValidByIds(List.of(itemIds), true));
    }
}
