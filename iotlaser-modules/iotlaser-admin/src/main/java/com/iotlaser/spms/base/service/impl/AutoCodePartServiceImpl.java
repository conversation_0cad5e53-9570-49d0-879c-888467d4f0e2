package com.iotlaser.spms.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.AutoCodePart;
import com.iotlaser.spms.base.domain.bo.AutoCodePartBo;
import com.iotlaser.spms.base.domain.vo.AutoCodePartVo;
import com.iotlaser.spms.base.mapper.AutoCodePartMapper;
import com.iotlaser.spms.base.service.IAutoCodePartService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 编码生成规则组成Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/03/11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AutoCodePartServiceImpl implements IAutoCodePartService {

    private final AutoCodePartMapper baseMapper;

    /**
     * 查询编码生成规则组成
     *
     * @param partId 主键
     * @return 编码生成规则组成
     */
    @Override
    public AutoCodePartVo queryById(Long partId) {
        return baseMapper.selectVoById(partId);
    }

    /**
     * 分页查询编码生成规则组成列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 编码生成规则组成分页列表
     */
    @Override
    public TableDataInfo<AutoCodePartVo> queryPageList(AutoCodePartBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AutoCodePart> lqw = buildQueryWrapper(bo);
        Page<AutoCodePartVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的编码生成规则组成列表
     *
     * @param bo 查询条件
     * @return 编码生成规则组成列表
     */
    @Override
    public List<AutoCodePartVo> queryList(AutoCodePartBo bo) {
        LambdaQueryWrapper<AutoCodePart> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AutoCodePart> buildQueryWrapper(AutoCodePartBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AutoCodePart> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AutoCodePart::getPartIndex);
        lqw.eq(bo.getRuleId() != null, AutoCodePart::getRuleId, bo.getRuleId());
        lqw.eq(bo.getPartIndex() != null, AutoCodePart::getPartIndex, bo.getPartIndex());
        // 修复enum类型转换：如果partType是enum类型，使用getValue()方法
        if (bo.getPartType() != null) {
            lqw.eq(AutoCodePart::getPartType, bo.getPartType().getValue());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getPartCode()), AutoCodePart::getPartCode, bo.getPartCode());
        lqw.like(StringUtils.isNotBlank(bo.getPartName()), AutoCodePart::getPartName, bo.getPartName());
        lqw.eq(bo.getPartLength() != null, AutoCodePart::getPartLength, bo.getPartLength());
        lqw.eq(StringUtils.isNotBlank(bo.getDateFormat()), AutoCodePart::getDateFormat, bo.getDateFormat());
        lqw.eq(StringUtils.isNotBlank(bo.getInputCharacter()), AutoCodePart::getInputCharacter, bo.getInputCharacter());
        lqw.eq(StringUtils.isNotBlank(bo.getFixCharacter()), AutoCodePart::getFixCharacter, bo.getFixCharacter());
        lqw.eq(bo.getSeriaStartNo() != null, AutoCodePart::getSeriaStartNo, bo.getSeriaStartNo());
        lqw.eq(bo.getSeriaStep() != null, AutoCodePart::getSeriaStep, bo.getSeriaStep());
        lqw.eq(bo.getSeriaNowNo() != null, AutoCodePart::getSeriaNowNo, bo.getSeriaNowNo());
        lqw.eq(StringUtils.isNotBlank(bo.getCycleFlag()), AutoCodePart::getCycleFlag, bo.getCycleFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getCycleMethod()), AutoCodePart::getCycleMethod, bo.getCycleMethod());
        return lqw;
    }

    /**
     * 新增编码生成规则组成
     *
     * @param bo 编码生成规则组成
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(AutoCodePartBo bo) {
        try {
            AutoCodePart add = MapstructUtils.convert(bo, AutoCodePart.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增编码生成规则组成失败");
            }

            bo.setPartId(add.getPartId());
            log.info("新增编码生成规则组成成功：规则【{}】组成【{}】", add.getRuleId(), add.getPartName());
            return true;
        } catch (Exception e) {
            log.error("新增编码生成规则组成失败：{}", e.getMessage(), e);
            throw new ServiceException("新增编码生成规则组成失败：" + e.getMessage());
        }
    }

    /**
     * 修改编码生成规则组成
     *
     * @param bo 编码生成规则组成
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(AutoCodePartBo bo) {
        try {
            AutoCodePart update = MapstructUtils.convert(bo, AutoCodePart.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改编码生成规则组成失败：组成不存在或数据未变更");
            }

            log.info("修改编码生成规则组成成功：规则【{}】组成【{}】", update.getRuleId(), update.getPartName());
            return true;
        } catch (Exception e) {
            log.error("修改编码生成规则组成失败：{}", e.getMessage(), e);
            throw new ServiceException("修改编码生成规则组成失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AutoCodePart entity) {
        // 保留关联数据存在性校验
        if (entity.getRuleId() != null) {
            // TODO: 校验编码规则是否存在
        }

        // 校验同一规则下的组成顺序唯一性
        if (entity.getPartIndex() != null) {
            LambdaQueryWrapper<AutoCodePart> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(AutoCodePart::getRuleId, entity.getRuleId());
            wrapper.eq(AutoCodePart::getPartIndex, entity.getPartIndex());
            if (entity.getPartId() != null) {
                wrapper.ne(AutoCodePart::getPartId, entity.getPartId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一规则下的组成顺序不能重复：" + entity.getPartIndex());
            }
        }
    }

    /**
     * 校验并批量删除编码生成规则组成信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验编码规则组成是否被使用
            List<AutoCodePart> parts = baseMapper.selectByIds(ids);
            for (AutoCodePart part : parts) {
                // 检查是否有关联的自动编码规则
                log.info("删除自动编码部件校验：{}", part.getPartName());
                // 例如：检查是否有编码生成记录使用了该组成
                log.info("删除编码规则组成校验：规则【{}】组成【{}】", part.getRuleId(), part.getPartName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除编码生成规则组成成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除编码生成规则组成失败：{}", e.getMessage(), e);
            throw new ServiceException("删除编码生成规则组成失败：" + e.getMessage());
        }
    }
}
