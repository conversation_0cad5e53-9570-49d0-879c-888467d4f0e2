package com.iotlaser.spms.pro.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.pro.domain.bo.RoutingBo;
import com.iotlaser.spms.pro.domain.vo.RoutingVo;
import com.iotlaser.spms.pro.service.IRoutingService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工艺路线
 *
 * <AUTHOR> Kai
 * @date 2025-06-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/pro/routing")
public class RoutingController extends BaseController {

    private final IRoutingService routingService;

    /**
     * 查询工艺路线列表
     */
    @SaCheckPermission("pro:routing:list")
    @GetMapping("/list")
    public TableDataInfo<RoutingVo> list(RoutingBo bo, PageQuery pageQuery) {
        return routingService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出工艺路线列表
     */
    @SaCheckPermission("pro:routing:export")
    @Log(title = "工艺路线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(RoutingBo bo, HttpServletResponse response) {
        List<RoutingVo> list = routingService.queryList(bo);
        ExcelUtil.exportExcel(list, "工艺路线", RoutingVo.class, response);
    }

    /**
     * 获取工艺路线详细信息
     *
     * @param routingId 主键
     */
    @SaCheckPermission("pro:routing:query")
    @GetMapping("/{routingId}")
    public R<RoutingVo> getInfo(@NotNull(message = "主键不能为空")
                                @PathVariable Long routingId) {
        return R.ok(routingService.queryById(routingId));
    }

    /**
     * 新增工艺路线
     */
    @SaCheckPermission("pro:routing:add")
    @Log(title = "工艺路线", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody RoutingBo bo) {
        return toAjax(routingService.insertByBo(bo));
    }

    /**
     * 修改工艺路线
     */
    @SaCheckPermission("pro:routing:edit")
    @Log(title = "工艺路线", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody RoutingBo bo) {
        return toAjax(routingService.updateByBo(bo));
    }

    /**
     * 删除工艺路线
     *
     * @param routingIds 主键串
     */
    @SaCheckPermission("pro:routing:remove")
    @Log(title = "工艺路线", businessType = BusinessType.DELETE)
    @DeleteMapping("/{routingIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] routingIds) {
        return toAjax(routingService.deleteWithValidByIds(List.of(routingIds), true));
    }
}
