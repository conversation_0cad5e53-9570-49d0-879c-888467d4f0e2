package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.InventoryCheckItem;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 库存盘点明细业务对象 wms_inventory_check_item
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InventoryCheckItem.class, reverseConvertGenerate = false)
public class InventoryCheckItemBo extends BaseEntity {

    /**
     * 盘点明细ID
     */
    @NotNull(message = "盘点明细ID不能为空", groups = {EditGroup.class})
    private Long itemId;

    /**
     * 盘点ID
     */
    private Long checkId;

    /**
     * 内部批次号/成品序列号
     */
    private String internalBatchNumber;

    /**
     * 供应商批次编号
     */
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    private String serialNumber;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 账面数量
     */
    private BigDecimal bookQuantity;

    /**
     * 实盘数量
     */
    private BigDecimal actualQuantity;

    /**
     * 盈亏数量
     */
    private BigDecimal differenceQuantity;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 盈亏金额
     */
    private BigDecimal differenceAmount;

    /**
     * 盘点状态
     */
    private String itemStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
