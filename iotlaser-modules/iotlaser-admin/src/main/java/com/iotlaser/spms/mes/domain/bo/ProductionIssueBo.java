package com.iotlaser.spms.mes.domain.bo;

import com.iotlaser.spms.mes.domain.ProductionIssue;
import com.iotlaser.spms.mes.enums.ProductionIssueStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 生产领料业务对象 mes_production_issue
 *
 * <AUTHOR>
 * @date 2025/05/07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductionIssue.class, reverseConvertGenerate = false)
public class ProductionIssueBo extends BaseEntity {

    /**
     * 领料单ID
     */
    @NotNull(message = "领料单ID不能为空", groups = {EditGroup.class})
    private Long issueId;

    /**
     * 领料单编号
     */
    @NotBlank(message = "领料单编号不能为空", groups = {EditGroup.class})
    private String issueCode;

    /**
     * 领料单名称
     */
    private String issueName;

    /**
     * 生产订单ID
     */
    private Long orderId;

    /**
     * 生产订单编码
     */
    private String orderCode;

    /**
     * 生产订单名称
     */
    private String orderName;

    /**
     * 检验单ID
     */
    private Long inspectionId;

    /**
     * 检验单编号
     */
    private String inspectionCode;

    /**
     * 检验单名称
     */
    private String inspectionName;

    /**
     * 领料时间
     */
    @NotNull(message = "领料时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date issueTime;

    /**
     * 领料状态
     */
    private ProductionIssueStatus issueStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 明细
     */
    private List<ProductionIssueItemBo> items;
}
