package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinApPaymentInvoiceLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentInvoiceLinkVo;
import com.iotlaser.spms.erp.service.IFinApPaymentInvoiceLinkService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 付款单与发票核销关系
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finApPaymentInvoiceLink")
public class FinApPaymentInvoiceLinkController extends BaseController {

    private final IFinApPaymentInvoiceLinkService finApPaymentInvoiceLinkService;

    /**
     * 查询付款单与发票核销关系列表
     */
    @SaCheckPermission("erp:finApPaymentInvoiceLink:list")
    @GetMapping("/list")
    public TableDataInfo<FinApPaymentInvoiceLinkVo> list(FinApPaymentInvoiceLinkBo bo, PageQuery pageQuery) {
        return finApPaymentInvoiceLinkService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出付款单与发票核销关系列表
     */
    @SaCheckPermission("erp:finApPaymentInvoiceLink:export")
    @Log(title = "付款单与发票核销关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinApPaymentInvoiceLinkBo bo, HttpServletResponse response) {
        List<FinApPaymentInvoiceLinkVo> list = finApPaymentInvoiceLinkService.queryList(bo);
        ExcelUtil.exportExcel(list, "付款单与发票核销关系", FinApPaymentInvoiceLinkVo.class, response);
    }

    /**
     * 获取付款单与发票核销关系详细信息
     *
     * @param linkId 主键
     */
    @SaCheckPermission("erp:finApPaymentInvoiceLink:query")
    @GetMapping("/{linkId}")
    public R<FinApPaymentInvoiceLinkVo> getInfo(@NotNull(message = "主键不能为空")
                                                @PathVariable Long linkId) {
        return R.ok(finApPaymentInvoiceLinkService.queryById(linkId));
    }

    /**
     * 新增付款单与发票核销关系
     */
    @SaCheckPermission("erp:finApPaymentInvoiceLink:add")
    @Log(title = "付款单与发票核销关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinApPaymentInvoiceLinkBo bo) {
        return toAjax(finApPaymentInvoiceLinkService.insertByBo(bo));
    }

    /**
     * 修改付款单与发票核销关系
     */
    @SaCheckPermission("erp:finApPaymentInvoiceLink:edit")
    @Log(title = "付款单与发票核销关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinApPaymentInvoiceLinkBo bo) {
        return toAjax(finApPaymentInvoiceLinkService.updateByBo(bo));
    }

    /**
     * 删除付款单与发票核销关系
     *
     * @param linkIds 主键串
     */
    @SaCheckPermission("erp:finApPaymentInvoiceLink:remove")
    @Log(title = "付款单与发票核销关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{linkIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] linkIds) {
        return toAjax(finApPaymentInvoiceLinkService.deleteWithValidByIds(List.of(linkIds), true));
    }
}
