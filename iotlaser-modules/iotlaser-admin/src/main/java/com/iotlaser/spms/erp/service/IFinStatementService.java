package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinStatementBo;
import com.iotlaser.spms.erp.domain.vo.FinStatementVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * 对账单Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-18
 */
public interface IFinStatementService {

    /**
     * 查询对账单
     *
     * @param statementId 主键
     * @return 对账单
     */
    FinStatementVo queryById(Long statementId);

    /**
     * 分页查询对账单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 对账单分页列表
     */
    TableDataInfo<FinStatementVo> queryPageList(FinStatementBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的对账单列表
     *
     * @param bo 查询条件
     * @return 对账单列表
     */
    List<FinStatementVo> queryList(FinStatementBo bo);

    /**
     * 新增对账单
     *
     * @param bo 对账单
     * @return 是否新增成功
     */
    Boolean insertByBo(FinStatementBo bo);

    /**
     * 修改对账单
     *
     * @param bo 对账单
     * @return 是否修改成功
     */
    Boolean updateByBo(FinStatementBo bo);

    /**
     * 校验并批量删除对账单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 生成客户对账单
     *
     * @param customerId   客户ID
     * @param customerCode 客户编码
     * @param customerName 客户名称
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 对账单ID
     */
    Long generateCustomerStatement(Long customerId, String customerCode, String customerName,
                                   LocalDate startDate, LocalDate endDate,
                                   Long operatorId, String operatorName);

    /**
     * 生成供应商对账单
     *
     * @param supplierId   供应商ID
     * @param supplierCode 供应商编码
     * @param supplierName 供应商名称
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 对账单ID
     */
    Long generateSupplierStatement(Long supplierId, String supplierCode, String supplierName,
                                   LocalDate startDate, LocalDate endDate,
                                   Long operatorId, String operatorName);

    /**
     * 对账单确认
     *
     * @param statementId   对账单ID
     * @param confirmById   确认人ID
     * @param confirmByName 确认人姓名
     * @param confirmRemark 确认备注
     * @return 是否确认成功
     */
    Boolean confirmStatement(Long statementId, Long confirmById, String confirmByName, String confirmRemark);
}
