package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 账户收支流水对象 erp_fin_account_ledger
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_account_ledger")
public class FinAccountLedger extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收支ID
     */
    @TableId(value = "ledger_id")
    private Long ledgerId;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 银行交易流水号
     */
    private String blankSerialNumber;

    /**
     * 交易发生时间
     */
    private Date transactionTime;

    /**
     * 方向
     */
    private String direction;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易前账户余额
     */
    private BigDecimal balanceBefore;

    /**
     * 交易后账户余额
     */
    private BigDecimal balanceAfter;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 来源编号
     */
    private String sourceCode;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 往来单位ID
     */
    private Long partnerId;

    /**
     * 往来单位编码
     */
    private String partnerCode;

    /**
     * 往来单位名称
     */
    private String partnerName;

    /**
     * 往来单位账号
     */
    private String partnerAccount;

    /**
     * 流水状态
     */
    private String ledgerStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;


}
