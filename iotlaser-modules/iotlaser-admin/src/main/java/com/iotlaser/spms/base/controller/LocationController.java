package com.iotlaser.spms.base.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.base.domain.bo.LocationBo;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 位置库位
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/base/location")
public class LocationController extends BaseController {

    private final ILocationService baseLocationService;

    /**
     * 查询位置库位列表
     */
    @SaCheckPermission("base:location:list")
    @GetMapping("/list")
    public R<List<LocationVo>> list(LocationBo bo) {
        List<LocationVo> list = baseLocationService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 导出位置库位列表
     */
    @SaCheckPermission("base:location:export")
    @Log(title = "位置库位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LocationBo bo, HttpServletResponse response) {
        List<LocationVo> list = baseLocationService.queryList(bo);
        ExcelUtil.exportExcel(list, "位置库位", LocationVo.class, response);
    }

    /**
     * 获取位置库位详细信息
     *
     * @param locationId 主键
     */
    @SaCheckPermission("base:location:query")
    @GetMapping("/{locationId}")
    public R<LocationVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long locationId) {
        return R.ok(baseLocationService.queryById(locationId));
    }

    /**
     * 新增位置库位
     */
    @SaCheckPermission("base:location:add")
    @Log(title = "位置库位", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LocationBo bo) {
        return toAjax(baseLocationService.insertByBo(bo));
    }

    /**
     * 修改位置库位
     */
    @SaCheckPermission("base:location:edit")
    @Log(title = "位置库位", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LocationBo bo) {
        return toAjax(baseLocationService.updateByBo(bo));
    }

    /**
     * 删除位置库位
     *
     * @param locationIds 主键串
     */
    @SaCheckPermission("base:location:remove")
    @Log(title = "位置库位", businessType = BusinessType.DELETE)
    @DeleteMapping("/{locationIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] locationIds) {
        return toAjax(baseLocationService.deleteWithValidByIds(List.of(locationIds), true));
    }
}
