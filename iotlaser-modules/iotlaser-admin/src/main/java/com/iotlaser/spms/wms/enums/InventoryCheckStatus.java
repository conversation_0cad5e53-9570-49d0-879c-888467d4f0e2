package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存盘点单状态枚举
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Getter
@AllArgsConstructor
public enum InventoryCheckStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "盘点单已创建，待开始盘点"),
    IN_PROGRESS("in_progress", "盘点中", "盘点工作正在进行中"),
    COMPLETED("completed", "已完成", "盘点工作已完成，待审核"),
    APPROVED("approved", "已审核", "盘点结果已审核通过，库存已调整"),
    CANCELLED("cancelled", "已取消", "盘点单被取消");

    public final static String DICT_CODE = "wms_inventory_check_status";
    public final static String DICT_NAME = "盘点状态";
    public final static String DICT_DESC = "管理库存盘点单的流程状态，从盘点计划到盘点完成的完整流程";

    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 库存盘点状态枚举
     */
    public static InventoryCheckStatus getByValue(String value) {
        for (InventoryCheckStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
