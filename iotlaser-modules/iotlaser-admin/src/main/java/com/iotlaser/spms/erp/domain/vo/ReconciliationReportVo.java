package com.iotlaser.spms.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 对账报表视图对象
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Data
public class ReconciliationReportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 报表标题
     */
    private String reportTitle;

    /**
     * 报表类型
     */
    private ReportType reportType;

    /**
     * 统计日期范围开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 统计日期范围结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 报表生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generateTime;

    /**
     * 汇总统计信息
     */
    private SummaryStatistics summaryStatistics;

    /**
     * 对账明细列表
     */
    private List<FinancialReconciliationVo> reconciliationDetails;

    /**
     * 差异明细列表
     */
    private List<DifferenceDetail> differenceDetails;

    /**
     * 客户对账汇总
     */
    private List<CustomerReconciliationSummary> customerSummaries;

    /**
     * 异常提醒列表
     */
    private List<ReconciliationAlert> alerts;

    /**
     * 报表类型枚举
     */
    public enum ReportType {
        /**
         * 汇总报表
         */
        SUMMARY("汇总报表"),

        /**
         * 明细报表
         */
        DETAIL("明细报表"),

        /**
         * 差异报表
         */
        DIFFERENCE("差异报表"),

        /**
         * 客户对账报表
         */
        CUSTOMER("客户对账报表");

        private final String description;

        ReportType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 汇总统计信息
     */
    @Data
    public static class SummaryStatistics {
        /**
         * 总订单数
         */
        private Integer totalOrders;

        /**
         * 对账一致订单数
         */
        private Integer matchedOrders;

        /**
         * 存在差异订单数
         */
        private Integer differenceOrders;

        /**
         * 未对账订单数
         */
        private Integer unreconciledOrders;

        /**
         * 总订单金额
         */
        private BigDecimal totalOrderAmount;

        /**
         * 总已收款金额
         */
        private BigDecimal totalReceivedAmount;

        /**
         * 总已开票金额
         */
        private BigDecimal totalInvoicedAmount;

        /**
         * 总差异金额
         */
        private BigDecimal totalDifferenceAmount;

        /**
         * 对账一致率
         */
        private BigDecimal matchedRate;

        /**
         * 收款完成率
         */
        private BigDecimal receivedRate;

        /**
         * 开票完成率
         */
        private BigDecimal invoicedRate;
    }

    /**
     * 差异明细
     */
    @Data
    public static class DifferenceDetail {
        /**
         * 订单ID
         */
        private Long orderId;

        /**
         * 订单编号
         */
        private String orderCode;

        /**
         * 客户名称
         */
        private String customerName;

        /**
         * 订单金额
         */
        private BigDecimal orderAmount;

        /**
         * 已收款金额
         */
        private BigDecimal receivedAmount;

        /**
         * 已开票金额
         */
        private BigDecimal invoicedAmount;

        /**
         * 差异金额
         */
        private BigDecimal differenceAmount;

        /**
         * 差异类型
         */
        private String differenceType;

        /**
         * 差异说明
         */
        private String differenceRemark;

        /**
         * 订单日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate orderDate;
    }

    /**
     * 客户对账汇总
     */
    @Data
    public static class CustomerReconciliationSummary {
        /**
         * 客户ID
         */
        private Long customerId;

        /**
         * 客户编号
         */
        private String customerCode;

        /**
         * 客户名称
         */
        private String customerName;

        /**
         * 订单数量
         */
        private Integer orderCount;

        /**
         * 订单总金额
         */
        private BigDecimal totalOrderAmount;

        /**
         * 已收款金额
         */
        private BigDecimal totalReceivedAmount;

        /**
         * 已开票金额
         */
        private BigDecimal totalInvoicedAmount;

        /**
         * 未收款金额
         */
        private BigDecimal unreceivedAmount;

        /**
         * 未开票金额
         */
        private BigDecimal uninvoicedAmount;

        /**
         * 差异金额
         */
        private BigDecimal differenceAmount;

        /**
         * 收款完成率
         */
        private BigDecimal receivedRate;

        /**
         * 开票完成率
         */
        private BigDecimal invoicedRate;
    }

    /**
     * 对账异常提醒
     */
    @Data
    public static class ReconciliationAlert {
        /**
         * 提醒类型
         */
        private AlertType alertType;

        /**
         * 提醒级别
         */
        private AlertLevel alertLevel;

        /**
         * 提醒标题
         */
        private String alertTitle;

        /**
         * 提醒内容
         */
        private String alertMessage;

        /**
         * 相关订单ID
         */
        private Long orderId;

        /**
         * 相关订单编号
         */
        private String orderCode;

        /**
         * 涉及金额
         */
        private BigDecimal amount;

        /**
         * 提醒时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime alertTime;

        /**
         * 提醒类型枚举
         */
        public enum AlertType {
            LARGE_DIFFERENCE("大额差异"),
            LONG_OVERDUE("长期逾期"),
            NEGATIVE_BALANCE("负余额"),
            DUPLICATE_PAYMENT("重复收款"),
            MISSING_INVOICE("缺失发票");

            private final String description;

            AlertType(String description) {
                this.description = description;
            }

            public String getDescription() {
                return description;
            }
        }

        /**
         * 提醒级别枚举
         */
        public enum AlertLevel {
            LOW("低"),
            MEDIUM("中"),
            HIGH("高"),
            CRITICAL("紧急");

            private final String description;

            AlertLevel(String description) {
                this.description = description;
            }

            public String getDescription() {
                return description;
            }
        }
    }
}
