package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * BOM库存分析结果VO
 *
 * <AUTHOR> Assistant
 * @date 2025-06-24
 */
@Data
@ExcelIgnoreUnannotated
public class BomInventoryAnalysisVo {

    /**
     * BOM清单ID
     */
    @ExcelProperty(value = "BOM清单ID")
    private Long bomId;

    /**
     * BOM清单编码
     */
    @ExcelProperty(value = "BOM清单编码")
    private String bomCode;

    /**
     * BOM清单名称
     */
    @ExcelProperty(value = "BOM清单名称")
    private String bomName;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 需求数量
     */
    @ExcelProperty(value = "需求数量")
    private BigDecimal requiredQuantity;

    /**
     * 分析时间
     */
    @ExcelProperty(value = "分析时间")
    private LocalDateTime analysisTime;

    /**
     * 库存状态：SUFFICIENT(充足)、INSUFFICIENT(不足)、SHORTAGE(缺料)
     */
    @ExcelProperty(value = "库存状态")
    private String inventoryStatus;

    /**
     * 是否可以生产
     */
    @ExcelProperty(value = "是否可以生产")
    private Boolean canProduce;

    /**
     * 缺料数量
     */
    private Integer shortageCount;

    /**
     * 预警数量
     */
    private Integer warningCount;

    /**
     * 原材料分析明细
     */
    private List<MaterialInventoryDetailVo> materialDetails;

    /**
     * 缺料清单
     */
    private List<MaterialShortageVo> shortageList;

    /**
     * 预警清单
     */
    private List<MaterialWarningVo> warningList;

    /**
     * 总采购金额估算
     */
    @ExcelProperty(value = "总采购金额估算")
    private BigDecimal totalPurchaseAmount;

    /**
     * 分析备注
     */
    private String analysisRemark;

    /**
     * 原材料库存明细VO
     */
    @Data
    public static class MaterialInventoryDetailVo {
        /**
         * 原材料ID
         */
        private Long materialId;

        /**
         * 原材料编码
         */
        private String materialCode;

        /**
         * 原材料名称
         */
        private String materialName;

        /**
         * 单位ID
         */
        private Long unitId;

        /**
         * 单位编码
         */
        private String unitCode;

        /**
         * 单位名称
         */
        private String unitName;

        /**
         * BOM用量（单位产品）
         */
        private BigDecimal bomUsage;

        /**
         * 总需求量
         */
        private BigDecimal totalRequirement;

        /**
         * 当前库存量
         */
        private BigDecimal currentStock;

        /**
         * 可用库存量
         */
        private BigDecimal availableStock;

        /**
         * 在途数量
         */
        private BigDecimal inTransitQuantity;

        /**
         * 安全库存量
         */
        private BigDecimal safetyStock;

        /**
         * 缺料数量
         */
        private BigDecimal shortageQuantity;

        /**
         * 库存状态
         */
        private String stockStatus;

        /**
         * 是否预警
         */
        private Boolean isWarning;

        /**
         * 最后更新时间
         */
        private LocalDateTime lastUpdateTime;
    }
}
