package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.PurchaseReturnBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnVo;
import com.iotlaser.spms.erp.service.IPurchaseReturnService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购退货
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/purchaseReturn")
public class PurchaseReturnController extends BaseController {

    private final IPurchaseReturnService purchaseReturnService;

    /**
     * 查询采购退货列表
     */
    @SaCheckPermission("erp:purchaseReturn:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseReturnVo> list(PurchaseReturnBo bo, PageQuery pageQuery) {
        return purchaseReturnService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出采购退货列表
     */
    @SaCheckPermission("erp:purchaseReturn:export")
    @Log(title = "采购退货", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseReturnBo bo, HttpServletResponse response) {
        List<PurchaseReturnVo> list = purchaseReturnService.queryList(bo);
        ExcelUtil.exportExcel(list, "采购退货", PurchaseReturnVo.class, response);
    }

    /**
     * 获取采购退货详细信息
     *
     * @param returnId 主键
     */
    @SaCheckPermission("erp:purchaseReturn:query")
    @GetMapping("/{returnId}")
    public R<PurchaseReturnVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long returnId) {
        return R.ok(purchaseReturnService.queryById(returnId));
    }

    /**
     * 新增采购退货
     */
    @SaCheckPermission("erp:purchaseReturn:add")
    @Log(title = "采购退货", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PurchaseReturnBo bo) {
        return toAjax(purchaseReturnService.insertByBo(bo));
    }

    /**
     * 修改采购退货
     */
    @SaCheckPermission("erp:purchaseReturn:edit")
    @Log(title = "采购退货", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PurchaseReturnBo bo) {
        return toAjax(purchaseReturnService.updateByBo(bo));
    }

    /**
     * 删除采购退货
     *
     * @param returnIds 主键串
     */
    @SaCheckPermission("erp:purchaseReturn:remove")
    @Log(title = "采购退货", businessType = BusinessType.DELETE)
    @DeleteMapping("/{returnIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] returnIds) {
        return toAjax(purchaseReturnService.deleteWithValidByIds(List.of(returnIds), true));
    }

    /**
     * 确认采购退货单
     *
     * @param returnId 退货单ID
     */
    @SaCheckPermission("erp:purchaseReturn:edit")
    @Log(title = "确认采购退货单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{returnId}")
    public R<Void> confirm(@NotNull(message = "退货单ID不能为空") @PathVariable Long returnId) {
        return toAjax(purchaseReturnService.confirmReturn(returnId));
    }

    /**
     * 批量确认采购退货单
     *
     * @param returnIds 退货单ID集合
     */
    @SaCheckPermission("erp:purchaseReturn:edit")
    @Log(title = "批量确认采购退货单", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "退货单ID不能为空") @RequestBody Long[] returnIds) {
        return toAjax(purchaseReturnService.batchConfirmReturns(List.of(returnIds)));
    }

    /**
     * 完成采购退货出库
     *
     * @param returnId 退货单ID
     */
    @SaCheckPermission("erp:purchaseReturn:edit")
    @Log(title = "完成采购退货出库", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{returnId}")
    public R<Void> complete(@NotNull(message = "退货单ID不能为空") @PathVariable Long returnId) {
        return toAjax(purchaseReturnService.completeReturn(returnId));
    }

    /**
     * 取消采购退货单
     *
     * @param returnId 退货单ID
     * @param reason   取消原因
     */
    @SaCheckPermission("erp:purchaseReturn:edit")
    @Log(title = "取消采购退货单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{returnId}")
    public R<Void> cancel(@NotNull(message = "退货单ID不能为空") @PathVariable Long returnId,
                          @RequestParam(required = false) String reason) {
        return toAjax(purchaseReturnService.cancelReturn(returnId, reason));
    }

    /**
     * 根据采购入库单创建退货单
     *
     * @param purchaseInboundId 采购入库单ID
     */
    @SaCheckPermission("erp:purchaseReturn:add")
    @Log(title = "从采购入库单创建退货单", businessType = BusinessType.INSERT)
    @PostMapping("/createFromPurchaseInbound/{purchaseInboundId}")
    public R<PurchaseReturnVo> createFromPurchaseInbound(@NotNull(message = "采购入库单ID不能为空") @PathVariable Long purchaseInboundId) {
        return R.ok(purchaseReturnService.createFromPurchaseInbound(purchaseInboundId));
    }
}
