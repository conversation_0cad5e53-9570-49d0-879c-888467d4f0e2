package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 出库类型枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Getter
@AllArgsConstructor
public enum OutboundStatus implements IDictEnum<String> {

    PENDING_PICKING("pending_picking", "待拣货", "已收到出库指令，等待分配拣货员"),
    PICKING_IN_PROGRESS("picking_in_progress", "拣货中", "拣货任务已开始"),
    PICKED("picked", "已拣货/待发运", "所有货物已从库位拣出，集中到发货区"),
    SHIPPED("shipped", "已发运", "货物已装车离开发货区"),
    COMPLETED("completed", "已完成", "与 SHIPPED 同义，代表流程结束"),
    CANCELLED("cancelled", "已取消", "上游单据取消，该出库任务作废");

    public final static String DICT_CODE = "wms_outbound_status";
    public final static String DICT_NAME = "出库状态";
    public final static String DICT_DESC = "管理仓库出库单的流程状态，从拣货指令到发运完成的完整流程";

    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 出库状态枚举
     */
    public static OutboundStatus getByValue(String value) {
        for (OutboundStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
