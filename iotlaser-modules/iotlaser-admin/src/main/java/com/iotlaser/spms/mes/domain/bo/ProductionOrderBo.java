package com.iotlaser.spms.mes.domain.bo;

import com.iotlaser.spms.mes.domain.ProductionOrder;
import com.iotlaser.spms.mes.enums.ProductionOrderStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 生产订单业务对象 mes_production_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductionOrder.class, reverseConvertGenerate = false)
public class ProductionOrderBo extends BaseEntity {

    /**
     * 生产订单ID
     */
    @NotNull(message = "生产订单ID不能为空", groups = {EditGroup.class})
    private Long orderId;

    /**
     * 生产订单编码
     */
    @NotBlank(message = "生产订单编码不能为空", groups = {EditGroup.class})
    private String orderCode;

    /**
     * 生产订单名称
     */
    private String orderName;

    /**
     * 生产订单类型
     */
    @NotBlank(message = "生产订单类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderType;

    /**
     * 销售订单ID
     */
    private Long saleOrderId;

    /**
     * 销售订单编码
     */
    private String saleOrderCode;

    /**
     * 销售订单名称
     */
    private String saleOrderName;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * BOMID
     */
    @NotBlank(message = "BOMID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String bomId;

    /**
     * BOM编码
     */
    private String bomCode;

    /**
     * BOM名称
     */
    private String bomName;

    /**
     * 生产数量
     */
    @NotNull(message = "生产数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal quantity;

    /**
     * 已完成数量
     */
    private BigDecimal finishQuantity;

    /**
     * 计划开始日期
     */
    @NotNull(message = "计划开始日期不能为空", groups = {AddGroup.class, EditGroup.class})
    private LocalDate plannedStartDate;

    /**
     * 计划结束日期
     */
    @NotNull(message = "计划结束日期不能为空", groups = {AddGroup.class, EditGroup.class})
    private LocalDate plannedEndDate;

    /**
     * 计划员ID
     */
    @NotNull(message = "计划员不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long plannerId;

    /**
     * 计划员姓名
     */
    private String plannerName;

    /**
     * 车间主管ID
     */
    private Long supervisorId;

    /**
     * 车间主管姓名
     */
    private String supervisorName;

    /**
     * 工单下达时间
     */
    private LocalDateTime releaseTime;

    /**
     * 实际开始时间
     */
    private Date actualStartTime;

    /**
     * 实际完成时间
     */
    private Date actualEndTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 订单状态
     */
    private ProductionOrderStatus orderStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

}
