package com.iotlaser.spms.pro.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * BOM物料替代料管理Service接口
 * <p>
 * 功能：
 * 1. 物料替代料查询
 * 2. 替代料可用性检查
 * 3. 替代料成本分析
 * 4. 智能替代料推荐
 *
 * <AUTHOR> <PERSON>
 * @date 2025/06/16
 */
public interface IBomMaterialSubstituteService {

    /**
     * 获取物料的替代料列表
     *
     * @param materialId 原物料ID
     * @return 替代料列表
     */
    List<Map<String, Object>> getMaterialSubstitutes(Long materialId);

    /**
     * 检查替代料的可用性
     *
     * @param originalMaterialId 原物料ID
     * @param requiredQuantity   需求数量
     * @return 替代料可用性分析
     */
    Map<String, Object> checkSubstituteAvailability(Long originalMaterialId, BigDecimal requiredQuantity);

    /**
     * 获取替代料成本分析
     *
     * @param originalMaterialId 原物料ID
     * @param substituteId       替代料ID
     * @param requiredQuantity   需求数量
     * @return 成本分析结果
     */
    Map<String, Object> analyzeSubstituteCost(Long originalMaterialId, Long substituteId, BigDecimal requiredQuantity);

    /**
     * 智能推荐最佳替代料
     *
     * @param originalMaterialId 原物料ID
     * @param requiredQuantity   需求数量
     * @param criteria           推荐标准（COST_FIRST, QUALITY_FIRST, AVAILABILITY_FIRST）
     * @return 推荐的替代料
     */
    Map<String, Object> recommendBestSubstitute(Long originalMaterialId, BigDecimal requiredQuantity, String criteria);

    /**
     * 批量检查BOM物料的替代料方案
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 生产数量
     * @return 替代料方案
     */
    Map<String, Object> generateSubstitutePlan(Long bomId, BigDecimal requiredQuantity);

    /**
     * 应用替代料方案
     *
     * @param bomId          BOM清单ID
     * @param substitutePlan 替代料方案
     * @return 应用结果
     */
    Map<String, Object> applySubstitutePlan(Long bomId, Map<String, Object> substitutePlan);
}
