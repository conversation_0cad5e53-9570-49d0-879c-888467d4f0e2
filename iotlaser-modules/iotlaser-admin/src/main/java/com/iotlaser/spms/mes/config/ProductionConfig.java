package com.iotlaser.spms.mes.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 生产管理配置类
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@Component
@ConfigurationProperties(prefix = "production")
public class ProductionConfig {

    /**
     * 生产订单配置
     */
    private Order order = new Order();

    /**
     * 生产报工配置
     */
    private Report report = new Report();

    /**
     * 生产领料配置
     */
    private Issue issue = new Issue();

    /**
     * 生产入库配置
     */
    private Inbound inbound = new Inbound();

    /**
     * 验证配置参数的有效性
     */
    public void validate() {
        // 验证订单配置
        if (order.getAllowedOverPercentage().compareTo(BigDecimal.ZERO) < 0 ||
            order.getAllowedOverPercentage().compareTo(BigDecimal.valueOf(100)) > 0) {
            throw new IllegalArgumentException("允许超产比例必须在0-100之间");
        }

        if (order.getMaxOverPercentage().compareTo(order.getAllowedOverPercentage()) < 0) {
            throw new IllegalArgumentException("最大超产比例不能小于允许超产比例");
        }

        // 验证报工配置
        if (report.getMaxContinuousWorkHours() <= 0 || report.getMaxContinuousWorkHours() > 24) {
            throw new IllegalArgumentException("最大连续工作时间必须在1-24小时之间");
        }

        // 验证领料配置
        if (issue.getReservationTimeoutHours() <= 0) {
            throw new IllegalArgumentException("库存预留超时时间必须大于0");
        }

        // 验证入库配置
        if (inbound.getQualityCheckPercentage().compareTo(BigDecimal.ZERO) < 0 ||
            inbound.getQualityCheckPercentage().compareTo(BigDecimal.valueOf(100)) > 0) {
            throw new IllegalArgumentException("质检比例必须在0-100之间");
        }
    }

    @Data
    public static class Order {
        /**
         * 允许超产比例（百分比）
         * 默认值：5.0，表示允许5%的超产
         */
        private BigDecimal allowedOverPercentage = BigDecimal.valueOf(5.0);

        /**
         * 最大超产比例（百分比）
         * 默认值：10.0，表示最大允许10%的超产
         */
        private BigDecimal maxOverPercentage = BigDecimal.valueOf(10.0);

        /**
         * 最小生产数量比例（百分比）
         * 默认值：1.0，表示最小生产数量为计划数量的1%
         */
        private BigDecimal minQuantityPercentage = BigDecimal.valueOf(1.0);

        /**
         * 自动下达订单阈值（小时）
         * 默认值：24，表示24小时后自动下达
         */
        private Integer autoReleaseHours = 24;

        /**
         * 订单超时预警时间（小时）
         * 默认值：72，表示72小时后预警
         */
        private Integer timeoutWarningHours = 72;
    }

    @Data
    public static class Report {
        /**
         * 自动计算工时
         * 默认值：true，表示自动计算工时
         */
        private Boolean autoCalculateWorkTime = true;

        /**
         * 最大连续工作时间（小时）
         * 默认值：12，表示最大连续工作12小时
         */
        private Integer maxContinuousWorkHours = 12;

        /**
         * 工序间隔时间（分钟）
         * 默认值：30，表示工序间隔30分钟
         */
        private Integer processIntervalMinutes = 30;

        /**
         * 进度更新频率（分钟）
         * 默认值：15，表示每15分钟更新一次进度
         */
        private Integer progressUpdateMinutes = 15;
    }

    @Data
    public static class Issue {
        /**
         * 自动创建领料单
         * 默认值：true，表示自动创建领料单
         */
        private Boolean autoCreateIssue = true;

        /**
         * 库存预留超时时间（小时）
         * 默认值：24，表示预留24小时后自动释放
         */
        private Integer reservationTimeoutHours = 24;

        /**
         * 最小库存预警比例（百分比）
         * 默认值：10.0，表示库存低于10%时预警
         */
        private BigDecimal minInventoryWarningPercentage = BigDecimal.valueOf(10.0);

        /**
         * 批次优先级策略
         * 默认值：FIFO，表示先进先出
         * 可选值：FIFO（先进先出）、LIFO（后进先出）、EXPIRY（按有效期）
         */
        private String batchPriorityStrategy = "FIFO";
    }

    @Data
    public static class Inbound {
        /**
         * 自动创建入库单
         * 默认值：true，表示自动创建入库单
         */
        private Boolean autoCreateInbound = true;

        /**
         * 质检比例（百分比）
         * 默认值：100.0，表示100%质检
         */
        private BigDecimal qualityCheckPercentage = BigDecimal.valueOf(100.0);

        /**
         * 默认有效期（天）
         * 默认值：365，表示默认有效期1年
         */
        private Integer defaultShelfLifeDays = 365;

        /**
         * 批次号生成策略
         * 默认值：AUTO，表示自动生成
         * 可选值：AUTO（自动生成）、MANUAL（手工输入）、DATE（按日期生成）
         */
        private String batchNumberStrategy = "AUTO";
    }
}
