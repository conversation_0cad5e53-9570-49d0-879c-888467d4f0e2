package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinArReceiptReceivableLink;
import com.iotlaser.spms.erp.domain.bo.FinArReceiptReceivableLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptReceivableLinkVo;
import com.iotlaser.spms.erp.mapper.FinArReceiptReceivableLinkMapper;
import com.iotlaser.spms.erp.service.IFinArReceiptReceivableLinkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 收款单与应收单核销关系Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinArReceiptReceivableLinkServiceImpl implements IFinArReceiptReceivableLinkService {

    private final FinArReceiptReceivableLinkMapper baseMapper;

    /**
     * 查询收款单与应收单核销关系
     *
     * @param linkId 主键
     * @return 收款单与应收单核销关系
     */
    @Override
    public FinArReceiptReceivableLinkVo queryById(Long linkId) {
        return baseMapper.selectVoById(linkId);
    }

    /**
     * 分页查询收款单与应收单核销关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 收款单与应收单核销关系分页列表
     */
    @Override
    public TableDataInfo<FinArReceiptReceivableLinkVo> queryPageList(FinArReceiptReceivableLinkBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinArReceiptReceivableLink> lqw = buildQueryWrapper(bo);
        Page<FinArReceiptReceivableLinkVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的收款单与应收单核销关系列表
     *
     * @param bo 查询条件
     * @return 收款单与应收单核销关系列表
     */
    @Override
    public List<FinArReceiptReceivableLinkVo> queryList(FinArReceiptReceivableLinkBo bo) {
        LambdaQueryWrapper<FinArReceiptReceivableLink> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinArReceiptReceivableLink> buildQueryWrapper(FinArReceiptReceivableLinkBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinArReceiptReceivableLink> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinArReceiptReceivableLink::getLinkId);
        lqw.eq(bo.getReceiptId() != null, FinArReceiptReceivableLink::getReceiptId, bo.getReceiptId());
        lqw.eq(bo.getReceivableId() != null, FinArReceiptReceivableLink::getReceivableId, bo.getReceivableId());
        lqw.eq(bo.getAppliedAmount() != null, FinArReceiptReceivableLink::getAppliedAmount, bo.getAppliedAmount());
        lqw.eq(bo.getCancellationDate() != null, FinArReceiptReceivableLink::getCancellationDate, bo.getCancellationDate());
        // TODO: 需要在实体中新增handlerId和handlerName字段
        // lqw.eq(bo.getHandlerId() != null, FinArReceiptReceivableLink::getHandlerId, bo.getHandlerId());
        // lqw.like(StringUtils.isNotBlank(bo.getHandlerName()), FinArReceiptReceivableLink::getHandlerName, bo.getHandlerName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinArReceiptReceivableLink::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增收款单与应收单核销关系
     *
     * @param bo 收款单与应收单核销关系
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinArReceiptReceivableLinkBo bo) {
        FinArReceiptReceivableLink add = MapstructUtils.convert(bo, FinArReceiptReceivableLink.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setLinkId(add.getLinkId());
        }
        return flag;
    }

    /**
     * 修改收款单与应收单核销关系
     *
     * @param bo 收款单与应收单核销关系
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinArReceiptReceivableLinkBo bo) {
        FinArReceiptReceivableLink update = MapstructUtils.convert(bo, FinArReceiptReceivableLink.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinArReceiptReceivableLink entity) {
        // 数据校验：检查唯一约束和必填字段
        validateUniqueConstraint(entity);
        validateRequiredFields(entity);
    }

    /**
     * 校验并批量删除收款单与应收单核销关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 业务校验：检查是否可以删除
            validateBeforeDelete(ids);
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取应收单已核销金额
     *
     * @param receivableId 应收单ID
     * @return 已核销金额
     */
    @Override
    public BigDecimal getAppliedAmountByReceivableId(Long receivableId) {
        LambdaQueryWrapper<FinArReceiptReceivableLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinArReceiptReceivableLink::getReceivableId, receivableId);
        wrapper.eq(FinArReceiptReceivableLink::getStatus, "1"); // 有效状态

        List<FinArReceiptReceivableLink> links = baseMapper.selectList(wrapper);
        return links.stream()
            .map(FinArReceiptReceivableLink::getAppliedAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取收款单已核销金额
     *
     * @param receiptId 收款单ID
     * @return 已核销金额
     */
    @Override
    public BigDecimal getAppliedAmountByReceiptId(Long receiptId) {
        LambdaQueryWrapper<FinArReceiptReceivableLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinArReceiptReceivableLink::getReceiptId, receiptId);
        wrapper.eq(FinArReceiptReceivableLink::getStatus, "1"); // 有效状态

        List<FinArReceiptReceivableLink> links = baseMapper.selectList(wrapper);
        return links.stream()
            .map(FinArReceiptReceivableLink::getAppliedAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 校验唯一约束
     *
     * @param entity 实体对象
     */
    private void validateUniqueConstraint(FinArReceiptReceivableLink entity) {
        // 检查同一收款单和应收单的核销记录是否已存在
        LambdaQueryWrapper<FinArReceiptReceivableLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinArReceiptReceivableLink::getReceiptId, entity.getReceiptId());
        wrapper.eq(FinArReceiptReceivableLink::getReceivableId, entity.getReceivableId());

        // 如果是更新操作，排除当前记录
        if (entity.getLinkId() != null) {
            wrapper.ne(FinArReceiptReceivableLink::getLinkId, entity.getLinkId());
        }

        if (baseMapper.exists(wrapper)) {
            throw new ServiceException("该收款单与应收单的核销记录已存在，不能重复创建");
        }
    }

    /**
     * 校验必填字段
     *
     * @param entity 实体对象
     */
    private void validateRequiredFields(FinArReceiptReceivableLink entity) {
        if (entity.getReceiptId() == null) {
            throw new ServiceException("收款单ID不能为空");
        }
        if (entity.getReceivableId() == null) {
            throw new ServiceException("应收单ID不能为空");
        }
        if (entity.getAppliedAmount() == null || entity.getAppliedAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("核销金额必须大于0");
        }
    }

    /**
     * 删除前校验
     *
     * @param ids 待删除的ID集合
     */
    private void validateBeforeDelete(Collection<Long> ids) {
        // 检查核销记录是否已确认，如果已确认则不能删除
        for (Long id : ids) {
            FinArReceiptReceivableLink link = baseMapper.selectById(id);
            if (link != null) {
                // TODO: 检查核销记录状态，如果已确认则不能删除
                // 暂时允许删除，后续根据业务需求完善
            }
        }
    }

    /**
     * 收款单与应收单主单据级核销
     *
     * @param receiptId     收款单ID
     * @param receivableId  应收单ID
     * @param appliedAmount 核销金额
     * @param remark        备注
     * @return 是否核销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyReceiptToReceivable(Long receiptId, Long receivableId,
                                            BigDecimal appliedAmount, String remark) {
        try {
            // 1. 参数校验
            if (receiptId == null) {
                throw new ServiceException("收款单ID不能为空");
            }
            if (receivableId == null) {
                throw new ServiceException("应收单ID不能为空");
            }
            if (appliedAmount == null || appliedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("核销金额必须大于0");
            }

            // 2. 校验收款单和应收单是否存在
            // 简化校验：检查ID是否有效
            if (receiptId == null || receiptId <= 0) {
                throw new ServiceException("收款单ID无效");
            }
            if (receivableId == null || receivableId <= 0) {
                throw new ServiceException("应收单ID无效");
            }

            // 3. 校验核销金额不能超过可核销金额
            BigDecimal receivableApplied = getAppliedAmountByReceivableId(receivableId);
            BigDecimal receiptApplied = getAppliedAmountByReceiptId(receiptId);

            // 校验核销金额的合理性
            if (appliedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("核销金额必须大于0");
            }

            // TODO: 完善金额校验逻辑
            // 当前简化处理，后续需要获取应收单和收款单的总金额进行校验
            log.info("核销金额校验 - 应收单已核销: {}, 收款单已核销: {}, 本次核销: {}",
                receivableApplied, receiptApplied, appliedAmount);
            // BigDecimal receivableAvailable = receivableAmount.subtract(receivableApplied);
            // BigDecimal receiptAvailable = receiptAmount.subtract(receiptApplied);

            // if (appliedAmount.compareTo(receivableAvailable) > 0) {
            //     throw new ServiceException("核销金额不能超过应收单可核销金额");
            // }
            // if (appliedAmount.compareTo(receiptAvailable) > 0) {
            //     throw new ServiceException("核销金额不能超过收款单可核销金额");
            // }

            // 4. 检查是否已存在核销记录
            LambdaQueryWrapper<FinArReceiptReceivableLink> checkWrapper = Wrappers.lambdaQuery();
            checkWrapper.eq(FinArReceiptReceivableLink::getReceiptId, receiptId);
            checkWrapper.eq(FinArReceiptReceivableLink::getReceivableId, receivableId);
            if (baseMapper.exists(checkWrapper)) {
                throw new ServiceException("该收款单与应收单的核销记录已存在");
            }

            // 5. 创建核销记录
            FinArReceiptReceivableLink link = new FinArReceiptReceivableLink();
            link.setReceiptId(receiptId);
            link.setReceivableId(receivableId);
            link.setAppliedAmount(appliedAmount);
            link.setCancellationDate(LocalDate.now());
            link.setRemark(StringUtils.isNotBlank(remark) ? remark : "主单据级核销");
            link.setStatus("1"); // 有效状态

            // TODO: 设置经办人信息（当前为临时变量，不会持久化）
            // 待数据库字段添加后，移除@TableField(exist = false)注解并启用以下代码：
            link.setHandlerId(LoginHelper.getUserId());
            link.setHandlerName(LoginHelper.getUsername());
            link.setHandleTime(LocalDateTime.now());

            // TODO: 设置经办部门信息（当前为临时变量，不会持久化）
            // 待数据库字段添加后，移除@TableField(exist = false)注解并启用以下代码：
            // link.setHandlerDeptId(LoginHelper.getDeptId());
            // link.setHandlerDeptName(LoginHelper.getDeptName());

            // TODO: 大额核销审批逻辑
            // 可以根据核销金额判断是否需要审批
            // if (appliedAmount.compareTo(new BigDecimal("10000")) > 0) {
            //     // 大额核销需要审批
            //     link.setApproverId(null); // 待审批
            //     link.setApproverName(null);
            //     link.setApproveTime(null);
            //     link.setApproveRemark("待审批");
            // } else {
            //     // 小额核销直接通过
            //     link.setApproverId(LoginHelper.getUserId());
            //     link.setApproverName(LoginHelper.getUsername());
            //     link.setApproveTime(LocalDateTime.now());
            //     link.setApproveRemark("小额核销自动通过");
            // }

            boolean result = baseMapper.insert(link) > 0;

            if (result) {
                log.info("主单据级核销成功 - 收款单ID: {}, 应收单ID: {}, 核销金额: {}",
                    receiptId, receivableId, appliedAmount);
            }

            return result;
        } catch (Exception e) {
            log.error("主单据级核销失败 - 收款单ID: {}, 应收单ID: {}, 错误: {}",
                receiptId, receivableId, e.getMessage(), e);
            throw new ServiceException("主单据级核销失败：" + e.getMessage());
        }
    }

    /**
     * 撤销收款单与应收单核销
     *
     * @param linkId 核销关系ID
     * @param reason 撤销原因
     * @return 是否撤销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelReceiptReceivableLink(Long linkId, String reason) {
        try {
            if (linkId == null) {
                throw new ServiceException("核销关系ID不能为空");
            }

            // 1. 获取核销记录
            FinArReceiptReceivableLink link = baseMapper.selectById(linkId);
            if (link == null) {
                throw new ServiceException("核销记录不存在");
            }

            // 2. 校验是否可以撤销
            // TODO: 根据业务规则校验是否可以撤销
            // 例如：检查相关单据状态、时间限制等

            // 3. 删除核销记录（逻辑删除）
            boolean result = baseMapper.deleteById(linkId) > 0;

            if (result) {
                log.info("核销撤销成功 - 核销ID: {}, 收款单ID: {}, 应收单ID: {}, 撤销原因: {}",
                    linkId, link.getReceiptId(), link.getReceivableId(), reason);
            }

            return result;
        } catch (Exception e) {
            log.error("核销撤销失败 - 核销ID: {}, 错误: {}", linkId, e.getMessage(), e);
            throw new ServiceException("核销撤销失败：" + e.getMessage());
        }
    }

    /**
     * 查询应收单的核销状态
     *
     * @param receivableId 应收单ID
     * @return 核销记录列表
     */
    @Override
    public List<FinArReceiptReceivableLinkVo> queryReceivableApplyStatus(Long receivableId) {
        LambdaQueryWrapper<FinArReceiptReceivableLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinArReceiptReceivableLink::getReceivableId, receivableId);
        wrapper.eq(FinArReceiptReceivableLink::getStatus, "1"); // 有效状态
        wrapper.orderByDesc(FinArReceiptReceivableLink::getCancellationDate);

        List<FinArReceiptReceivableLink> links = baseMapper.selectList(wrapper);
        return MapstructUtils.convert(links, FinArReceiptReceivableLinkVo.class);
    }

    /**
     * 查询收款单的核销记录
     *
     * @param receiptId 收款单ID
     * @return 核销记录列表
     */
    @Override
    public List<FinArReceiptReceivableLinkVo> queryReceiptApplyRecords(Long receiptId) {
        LambdaQueryWrapper<FinArReceiptReceivableLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinArReceiptReceivableLink::getReceiptId, receiptId);
        wrapper.eq(FinArReceiptReceivableLink::getStatus, "1"); // 有效状态
        wrapper.orderByDesc(FinArReceiptReceivableLink::getCancellationDate);

        List<FinArReceiptReceivableLink> links = baseMapper.selectList(wrapper);
        return MapstructUtils.convert(links, FinArReceiptReceivableLinkVo.class);
    }

    /**
     * 检查收款单是否存在核销记录
     *
     * @param receiptId 收款单ID
     * @return 是否存在核销记录
     */
    @Override
    public Boolean existsByReceiptId(Long receiptId) {
        LambdaQueryWrapper<FinArReceiptReceivableLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinArReceiptReceivableLink::getReceiptId, receiptId);
        wrapper.eq(FinArReceiptReceivableLink::getStatus, "1"); // 有效状态
        return baseMapper.exists(wrapper);
    }
}
