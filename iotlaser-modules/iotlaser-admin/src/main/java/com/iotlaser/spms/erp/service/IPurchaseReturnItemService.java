package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.PurchaseReturnItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 采购退货明细Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/07
 */
public interface IPurchaseReturnItemService {

    /**
     * 查询采购退货明细
     *
     * @param itemId 主键
     * @return 采购退货明细
     */
    PurchaseReturnItemVo queryById(Long itemId);

    /**
     * 分页查询采购退货明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购退货明细分页列表
     */
    TableDataInfo<PurchaseReturnItemVo> queryPageList(PurchaseReturnItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的采购退货明细列表
     *
     * @param bo 查询条件
     * @return 采购退货明细列表
     */
    List<PurchaseReturnItemVo> queryList(PurchaseReturnItemBo bo);

    /**
     * 新增采购退货明细
     *
     * @param bo 采购退货明细
     * @return 是否新增成功
     */
    Boolean insertByBo(PurchaseReturnItemBo bo);

    /**
     * 修改采购退货明细
     *
     * @param bo 采购退货明细
     * @return 是否修改成功
     */
    Boolean updateByBo(PurchaseReturnItemBo bo);

    /**
     * 校验并批量删除采购退货明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量新增或更新采购退货明细
     * ✅ 统一使用insertOrUpdateBatch方法，避免重复的批量插入方法
     *
     * @param items 采购退货明细列表
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<PurchaseReturnItemBo> items);

    /**
     * 根据退货单ID查询明细列表
     *
     * @param returnId 退货单ID
     * @return 明细列表
     */
    List<PurchaseReturnItemVo> queryByReturnId(Long returnId);

    /**
     * 查询采购退货明细表及其关联信息
     *
     * @param itemId 主键
     * @return 采购退货明细表
     */
    PurchaseReturnItemVo queryByIdWith(Long itemId);

    /**
     * 分页查询采购退货明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购退货明细表分页列表
     */
    TableDataInfo<PurchaseReturnItemVo> queryPageListWith(PurchaseReturnItemBo bo, PageQuery pageQuery);

}
