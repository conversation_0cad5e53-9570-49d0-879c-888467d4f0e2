package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.enums.PurchaseOrderType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 采购订单对象 erp_purchase_order
 *
 * <AUTHOR> Kai
 * @date 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_purchase_order")
public class PurchaseOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @TableId(value = "order_id")
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 来源编号
     */
    private String sourceCode;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 来源类型
     */
    private PurchaseOrderType sourceType;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 下单日期
     */
    private Date orderDate;

    /**
     * 订单状态
     */
    private PurchaseOrderStatus orderStatus;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人
     */
    private String applicantName;

    /**
     * 采购员ID
     */
    private Long handlerId;

    /**
     * 采购员
     */
    private String handlerName;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审批人
     */
    private String approverName;

    /**
     * 审批通过时间
     */
    private LocalDateTime approveTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 明细
     */
    @TableField(exist = false)
    private List<PurchaseOrderItem> items;

    // ==================== 临时变量：汇总字段 ====================
    // TODO: 待数据库结构完善后，这些字段应该持久化到数据库

    /**
     * 总数量（临时变量）
     * TODO: 需要在数据库中添加 total_quantity DECIMAL(15,4) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalQuantity;

    /**
     * 总金额-含税（临时变量）
     * TODO: 需要在数据库中添加 total_amount DECIMAL(15,2) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalAmount;

    /**
     * 总金额-不含税（临时变量）
     * TODO: 需要在数据库中添加 total_amount_exclusive_tax DECIMAL(15,2) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalAmountExclusiveTax;

    /**
     * 总税额（临时变量）
     * TODO: 需要在数据库中添加 total_tax_amount DECIMAL(15,2) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalTaxAmount;

}
