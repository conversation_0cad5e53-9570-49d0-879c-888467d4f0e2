package com.iotlaser.spms.pro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.pro.domain.RoutingStep;
import com.iotlaser.spms.pro.domain.bo.RoutingStepBo;
import com.iotlaser.spms.pro.domain.vo.RoutingStepVo;
import com.iotlaser.spms.pro.mapper.RoutingStepMapper;
import com.iotlaser.spms.pro.service.IRoutingStepService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 工艺路线工序Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RoutingStepServiceImpl implements IRoutingStepService {

    private final RoutingStepMapper baseMapper;

    /**
     * 查询工艺路线工序
     *
     * @param stepId 主键
     * @return 工艺路线工序
     */
    @Override
    public RoutingStepVo queryById(Long stepId) {
        return baseMapper.selectVoById(stepId);
    }

    /**
     * 分页查询工艺路线工序列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工艺路线工序分页列表
     */
    @Override
    public TableDataInfo<RoutingStepVo> queryPageList(RoutingStepBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<RoutingStep> lqw = buildQueryWrapper(bo);
        Page<RoutingStepVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的工艺路线工序列表
     *
     * @param bo 查询条件
     * @return 工艺路线工序列表
     */
    @Override
    public List<RoutingStepVo> queryList(RoutingStepBo bo) {
        LambdaQueryWrapper<RoutingStep> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<RoutingStep> buildQueryWrapper(RoutingStepBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<RoutingStep> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(RoutingStep::getStepId);
        lqw.eq(bo.getRoutingId() != null, RoutingStep::getRoutingId, bo.getRoutingId());
        lqw.eq(bo.getProcessId() != null, RoutingStep::getProcessId, bo.getProcessId());
        lqw.eq(StringUtils.isNotBlank(bo.getProcessCode()), RoutingStep::getProcessCode, bo.getProcessCode());
        lqw.like(StringUtils.isNotBlank(bo.getProcessName()), RoutingStep::getProcessName, bo.getProcessName());
        lqw.eq(bo.getNextStepId() != null, RoutingStep::getNextStepId, bo.getNextStepId());
        lqw.eq(bo.getReworkStepId() != null, RoutingStep::getReworkStepId, bo.getReworkStepId());
        lqw.eq(bo.getSetupTime() != null, RoutingStep::getSetupTime, bo.getSetupTime());
        lqw.eq(bo.getProcessingTime() != null, RoutingStep::getProcessingTime, bo.getProcessingTime());
        lqw.eq(StringUtils.isNotBlank(bo.getQualityCheckSpecs()), RoutingStep::getQualityCheckSpecs, bo.getQualityCheckSpecs());
        lqw.eq(StringUtils.isNotBlank(bo.getReportType()), RoutingStep::getReportType, bo.getReportType());
        lqw.eq(bo.getOrderNum() != null, RoutingStep::getOrderNum, bo.getOrderNum());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), RoutingStep::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增工艺路线工序
     *
     * @param bo 工艺路线工序
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(RoutingStepBo bo) {
        RoutingStep add = MapstructUtils.convert(bo, RoutingStep.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStepId(add.getStepId());
        }
        return flag;
    }

    /**
     * 修改工艺路线工序
     *
     * @param bo 工艺路线工序
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(RoutingStepBo bo) {
        RoutingStep update = MapstructUtils.convert(bo, RoutingStep.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(RoutingStep entity) {
        // 校验必填字段
        if (entity.getRoutingId() == null) {
            throw new ServiceException("工艺路线不能为空");
        }
        if (entity.getProcessId() == null) {
            throw new ServiceException("工艺不能为空");
        }
        if (entity.getOrderNum() == null || entity.getOrderNum() <= 0) {
            throw new ServiceException("工序号必须大于0");
        }

        // 校验同一工艺路线中工序号不能重复
        if (entity.getRoutingId() != null && entity.getOrderNum() != null) {
            LambdaQueryWrapper<RoutingStep> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(RoutingStep::getRoutingId, entity.getRoutingId());
            wrapper.eq(RoutingStep::getOrderNum, entity.getOrderNum());
            if (entity.getStepId() != null) {
                wrapper.ne(RoutingStep::getStepId, entity.getStepId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一工艺路线中工序号不能重复：" + entity.getOrderNum());
            }
        }
    }

    /**
     * 校验并批量删除工艺路线工序信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验工艺路线工序是否可以删除
            List<RoutingStep> steps = baseMapper.selectByIds(ids);
            for (RoutingStep step : steps) {
                log.info("删除工艺路线工序，工序号：{}", step.getOrderNum());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
