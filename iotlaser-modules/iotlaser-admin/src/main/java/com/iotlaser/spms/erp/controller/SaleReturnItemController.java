package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.SaleReturnItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnItemVo;
import com.iotlaser.spms.erp.service.ISaleReturnItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售退货明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/saleReturnItem")
public class SaleReturnItemController extends BaseController {

    private final ISaleReturnItemService saleReturnItemService;

    /**
     * 查询销售退货明细列表
     */
    @SaCheckPermission("erp:saleReturnItem:list")
    @GetMapping("/list")
    public TableDataInfo<SaleReturnItemVo> list(SaleReturnItemBo bo, PageQuery pageQuery) {
        return saleReturnItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出销售退货明细列表
     */
    @SaCheckPermission("erp:saleReturnItem:export")
    @Log(title = "销售退货明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SaleReturnItemBo bo, HttpServletResponse response) {
        List<SaleReturnItemVo> list = saleReturnItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "销售退货明细", SaleReturnItemVo.class, response);
    }

    /**
     * 获取销售退货明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("erp:saleReturnItem:query")
    @GetMapping("/{itemId}")
    public R<SaleReturnItemVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long itemId) {
        return R.ok(saleReturnItemService.queryById(itemId));
    }

    /**
     * 新增销售退货明细
     */
    @SaCheckPermission("erp:saleReturnItem:add")
    @Log(title = "销售退货明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SaleReturnItemBo bo) {
        return toAjax(saleReturnItemService.insertByBo(bo));
    }

    /**
     * 修改销售退货明细
     */
    @SaCheckPermission("erp:saleReturnItem:edit")
    @Log(title = "销售退货明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SaleReturnItemBo bo) {
        return toAjax(saleReturnItemService.updateByBo(bo));
    }

    /**
     * 删除销售退货明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("erp:saleReturnItem:remove")
    @Log(title = "销售退货明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(saleReturnItemService.deleteWithValidByIds(List.of(itemIds), true));
    }

    /**
     * 查询销售退货明细及其关联信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("erp:saleReturnItem:query")
    @GetMapping("/queryByIdWith/{itemId}")
    public R<SaleReturnItemVo> queryByIdWith(@NotNull(message = "主键不能为空")
                                             @PathVariable Long itemId) {
        return R.ok(saleReturnItemService.queryByIdWith(itemId));
    }

    /**
     * 分页查询销售退货明细列表及其关联信息
     */
    @SaCheckPermission("erp:saleReturnItem:list")
    @GetMapping("/queryPageListWith")
    public TableDataInfo<SaleReturnItemVo> queryPageListWith(SaleReturnItemBo bo, PageQuery pageQuery) {
        return saleReturnItemService.queryPageListWith(bo, pageQuery);
    }
}
