package com.iotlaser.spms.erp.mapper;

import com.iotlaser.spms.erp.domain.PurchaseInboundItemBatch;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemBatchVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;

/**
 * 采购入库批次明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
public interface PurchaseInboundItemBatchMapper extends BaseMapperPlus<PurchaseInboundItemBatch, PurchaseInboundItemBatchVo> {

    BigDecimal selectQuantitySumByItemId(@Param("itemId") Long itemId, @Param("batchId") Long batchId);
}
