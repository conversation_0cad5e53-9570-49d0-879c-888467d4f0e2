package com.iotlaser.spms.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.wms.domain.bo.InventoryCheckBo;
import com.iotlaser.spms.wms.domain.vo.InventoryCheckVo;
import com.iotlaser.spms.wms.service.IInventoryCheckService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 库存盘点
 *
 * <AUTHOR> Kai
 * @date 2025-06-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/wms/inventoryCheck")
public class InventoryCheckController extends BaseController {

    private final IInventoryCheckService inventoryCheckService;

    /**
     * 查询库存盘点列表
     */
    @SaCheckPermission("wms:inventoryCheck:list")
    @GetMapping("/list")
    public TableDataInfo<InventoryCheckVo> list(InventoryCheckBo bo, PageQuery pageQuery) {
        return inventoryCheckService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出库存盘点列表
     */
    @SaCheckPermission("wms:inventoryCheck:export")
    @Log(title = "库存盘点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InventoryCheckBo bo, HttpServletResponse response) {
        List<InventoryCheckVo> list = inventoryCheckService.queryList(bo);
        ExcelUtil.exportExcel(list, "库存盘点", InventoryCheckVo.class, response);
    }

    /**
     * 获取库存盘点详细信息
     *
     * @param checkId 主键
     */
    @SaCheckPermission("wms:inventoryCheck:query")
    @GetMapping("/{checkId}")
    public R<InventoryCheckVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long checkId) {
        return R.ok(inventoryCheckService.queryById(checkId));
    }

    /**
     * 新增库存盘点
     */
    @SaCheckPermission("wms:inventoryCheck:add")
    @Log(title = "库存盘点", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InventoryCheckBo bo) {
        Boolean result = inventoryCheckService.insertByBo(bo);
        return result ? R.ok() : R.fail("新增库存盘点失败");
    }

    /**
     * 修改库存盘点
     */
    @SaCheckPermission("wms:inventoryCheck:edit")
    @Log(title = "库存盘点", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InventoryCheckBo bo) {
        Boolean result = inventoryCheckService.updateByBo(bo);
        return result ? R.ok() : R.fail("修改库存盘点失败");
    }

    /**
     * 删除库存盘点
     *
     * @param checkIds 主键串
     */
    @SaCheckPermission("wms:inventoryCheck:remove")
    @Log(title = "库存盘点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{checkIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] checkIds) {
        return toAjax(inventoryCheckService.deleteWithValidByIds(List.of(checkIds), true));
    }

    /**
     * 开始盘点
     *
     * @param checkId 盘点单ID
     */
    @SaCheckPermission("wms:inventoryCheck:edit")
    @Log(title = "开始库存盘点", businessType = BusinessType.UPDATE)
    @PostMapping("/start/{checkId}")
    public R<Void> start(@NotNull(message = "盘点单ID不能为空") @PathVariable Long checkId) {
        return toAjax(inventoryCheckService.startCheck(checkId));
    }

    /**
     * 完成盘点
     *
     * @param checkId 盘点单ID
     */
    @SaCheckPermission("wms:inventoryCheck:edit")
    @Log(title = "完成库存盘点", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{checkId}")
    public R<Void> complete(@NotNull(message = "盘点单ID不能为空") @PathVariable Long checkId) {
        return toAjax(inventoryCheckService.completeCheck(checkId));
    }

    /**
     * 审核盘点
     *
     * @param checkId 盘点单ID
     */
    @SaCheckPermission("wms:inventoryCheck:approve")
    @Log(title = "审核库存盘点", businessType = BusinessType.UPDATE)
    @PostMapping("/approve/{checkId}")
    public R<Void> approve(@NotNull(message = "盘点单ID不能为空") @PathVariable Long checkId) {
        return toAjax(inventoryCheckService.approveCheck(checkId));
    }

    /**
     * 取消盘点
     *
     * @param checkId 盘点单ID
     * @param reason  取消原因
     */
    @SaCheckPermission("wms:inventoryCheck:edit")
    @Log(title = "取消库存盘点", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{checkId}")
    public R<Void> cancel(@NotNull(message = "盘点单ID不能为空") @PathVariable Long checkId,
                          @RequestParam(required = false) String reason) {
        return toAjax(inventoryCheckService.cancelCheck(checkId, reason));
    }

    /**
     * 生成盘点单
     *
     * @param locationId 仓库ID
     */
    @SaCheckPermission("wms:inventoryCheck:add")
    @Log(title = "生成库存盘点单", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public R<InventoryCheckVo> generate(@NotNull(message = "仓库ID不能为空") @RequestParam Long locationId) {
        return R.ok(inventoryCheckService.generateCheck(locationId));
    }
}
