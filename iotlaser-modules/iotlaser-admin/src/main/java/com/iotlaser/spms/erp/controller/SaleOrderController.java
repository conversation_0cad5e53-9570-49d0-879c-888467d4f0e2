package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.SaleOrderBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderVo;
import com.iotlaser.spms.erp.service.ISaleOrderService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售订单
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/saleOrder")
public class SaleOrderController extends BaseController {

    private final ISaleOrderService saleOrderService;

    /**
     * 查询销售订单列表
     */
    @SaCheckPermission("erp:saleOrder:list")
    @GetMapping("/list")
    public TableDataInfo<SaleOrderVo> list(SaleOrderBo bo, PageQuery pageQuery) {
        return saleOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出销售订单列表
     */
    @SaCheckPermission("erp:saleOrder:export")
    @Log(title = "销售订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SaleOrderBo bo, HttpServletResponse response) {
        List<SaleOrderVo> list = saleOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "销售订单", SaleOrderVo.class, response);
    }

    /**
     * 获取销售订单详细信息
     *
     * @param orderId 主键
     */
    @SaCheckPermission("erp:saleOrder:query")
    @GetMapping("/{orderId}")
    public R<SaleOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long orderId) {
        return R.ok(saleOrderService.queryById(orderId));
    }

    /**
     * 新增销售订单
     */
    @SaCheckPermission("erp:saleOrder:add")
    @Log(title = "销售订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SaleOrderBo bo) {
        return toAjax(saleOrderService.insertByBo(bo));
    }

    /**
     * 修改销售订单
     */
    @SaCheckPermission("erp:saleOrder:edit")
    @Log(title = "销售订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SaleOrderBo bo) {
        return toAjax(saleOrderService.updateByBo(bo));
    }

    /**
     * 删除销售订单
     *
     * @param orderIds 主键串
     */
    @SaCheckPermission("erp:saleOrder:remove")
    @Log(title = "销售订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] orderIds) {
        return toAjax(saleOrderService.deleteWithValidByIds(List.of(orderIds), true));
    }

    /**
     * 确认销售订单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:saleOrder:confirm")
    @Log(title = "确认销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{orderId}")
    public R<Void> confirmOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.confirmOrder(orderId));
    }

    /**
     * 挂起销售订单
     *
     * @param orderId    订单ID
     * @param holdReason 挂起原因
     */
    @SaCheckPermission("erp:saleOrder:hold")
    @Log(title = "挂起销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/hold/{orderId}")
    public R<Void> holdOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId,
                             @RequestParam(required = false) String holdReason) {
        return toAjax(saleOrderService.holdOrder(orderId, holdReason));
    }

    /**
     * 恢复挂起的销售订单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:saleOrder:resume")
    @Log(title = "恢复销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/resume/{orderId}")
    public R<Void> resumeOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.resumeOrder(orderId));
    }

    /**
     * 取消销售订单
     *
     * @param orderId      订单ID
     * @param cancelReason 取消原因
     */
    @SaCheckPermission("erp:saleOrder:cancel")
    @Log(title = "取消销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{orderId}")
    public R<Void> cancelOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId,
                               @RequestParam(required = false) String cancelReason) {
        return toAjax(saleOrderService.cancelOrder(orderId, cancelReason));
    }

    /**
     * 关闭销售订单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:saleOrder:close")
    @Log(title = "关闭销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/close/{orderId}")
    public R<Void> closeOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.closeOrder(orderId));
    }

    /**
     * 从销售订单生成应收单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:saleOrder:generateReceivable")
    @Log(title = "生成应收单", businessType = BusinessType.INSERT)
    @PostMapping("/generateReceivable/{orderId}")
    public R<Void> generateReceivable(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.generateReceivableFromOrder(orderId));
    }

    /**
     * 自动创建出库单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:saleOrder:createOutbound")
    @Log(title = "创建出库单", businessType = BusinessType.INSERT)
    @PostMapping("/createOutbound/{orderId}")
    public R<Void> createOutbound(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.autoCreateOutbound(orderId));
    }
}
