package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.SaleReturnItem;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * 销售退货明细业务对象 erp_sale_return_item
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SaleReturnItem.class, reverseConvertGenerate = false)
public class SaleReturnItemBo extends BaseEntity {

    /**
     * 明细ID
     */
    @NotNull(message = "明细ID不能为空", groups = {EditGroup.class})
    private Long itemId;

    /**
     * 退货单ID
     */
    @NotNull(message = "退货单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long returnId;

    /**
     * 库存ID
     */
    private Long inventoryBatchId;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 待完成数量
     */
    @NotNull(message = "待完成数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal quantity;

    /**
     * 已完成数量
     */
    private BigDecimal finishQuantity;

    /**
     * 单价（含税）
     */
    private BigDecimal price;

    /**
     * 单价（不含税）
     */
    @DecimalMin(value = "0.00", message = "单价（不含税）不能小于0")
    private BigDecimal priceExclusiveTax;

    /**
     * 金额（不含税）
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    @DecimalMin(value = "0.00", message = "税率不能小于0")
    @DecimalMax(value = "100.00", message = "税率不能大于100")
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 批次
     */
    private List<SaleReturnItemBatchBo> batches;
}
