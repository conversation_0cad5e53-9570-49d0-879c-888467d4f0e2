package com.iotlaser.spms.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.mes.domain.ProductionInbound;
import com.iotlaser.spms.mes.enums.ProductionInboundStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 生产入库视图对象 mes_production_inbound
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductionInbound.class)
public class ProductionInboundVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 入库单ID
     */
    @ExcelProperty(value = "入库单ID")
    private Long inboundId;

    /**
     * 入库单编号
     */
    @ExcelProperty(value = "入库单编号")
    private String inboundCode;

    /**
     * 入库单名称
     */
    @ExcelProperty(value = "入库单名称")
    private String inboundName;

    /**
     * 生产订单ID
     */
    @ExcelProperty(value = "生产订单ID")
    private Long orderId;

    /**
     * 生产订单编码
     */
    @ExcelProperty(value = "生产订单编码")
    private String orderCode;

    /**
     * 生产订单名称
     */
    @ExcelProperty(value = "生产订单名称")
    private String orderName;

    /**
     * 检验单ID
     */
    @ExcelProperty(value = "检验单ID")
    private Long inspectionId;

    /**
     * 检验单编号
     */
    @ExcelProperty(value = "检验单编号")
    private String inspectionCode;

    /**
     * 检验单名称
     */
    @ExcelProperty(value = "检验单名称")
    private String inspectionName;

    /**
     * 入库时间
     */
    @ExcelProperty(value = "入库时间")
    private Date inboundTime;

    /**
     * 入库状态
     */
    @ExcelProperty(value = "入库状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_production_inbound_status")
    private ProductionInboundStatus inboundStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
