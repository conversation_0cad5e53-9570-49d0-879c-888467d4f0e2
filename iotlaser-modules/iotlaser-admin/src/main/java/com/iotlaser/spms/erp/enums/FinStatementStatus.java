package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 对账单状态枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinStatementStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "对账单已创建，但未生成明细"),
    GENERATING("generating", "生成中", "正在生成对账明细"),
    GENERATED("generated", "已生成", "对账明细已生成，等待确认"),
    CUSTOMER_CONFIRMED("customer_confirmed", "客户已确认", "客户已确认对账单"),
    COMPANY_CONFIRMED("company_confirmed", "公司已确认", "公司已确认对账单"),
    CONFIRMED("confirmed", "双方确认", "客户和公司都已确认对账单"),
    PARTIALLY_CONFIRMED("partially_confirmed", "部分确认", "存在差异但部分确认"),
    DISPUTED("disputed", "有争议", "对账单存在争议，需要处理"),
    CANCELLED("cancelled", "已取消", "对账单被取消");

    public final static String DICT_CODE = "erp_fin_statement_status";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 对账单状态枚举
     */
    public static FinStatementStatus getByValue(String value) {
        for (FinStatementStatus statementStatus : values()) {
            if (statementStatus.getValue().equals(value)) {
                return statementStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    /**
     * 判断是否为可编辑状态
     *
     * @return 是否可编辑
     */
    public boolean isEditable() {
        return this == DRAFT;
    }

    /**
     * 判断是否为可删除状态
     *
     * @return 是否可删除
     */
    public boolean isDeletable() {
        return this == DRAFT;
    }

    /**
     * 判断是否为已完成状态
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return this == CONFIRMED || this == CANCELLED;
    }

    /**
     * 判断是否可以确认
     *
     * @return 是否可以确认
     */
    public boolean isConfirmable() {
        return this == GENERATED || this == CUSTOMER_CONFIRMED || this == COMPANY_CONFIRMED || this == PARTIALLY_CONFIRMED;
    }

    /**
     * 判断是否有争议
     *
     * @return 是否有争议
     */
    public boolean isDisputed() {
        return this == DISPUTED;
    }

    /**
     * 获取下一个可能的状态
     *
     * @return 下一个可能的状态列表
     */
    public FinStatementStatus[] getNextPossibleStates() {
        switch (this) {
            case DRAFT:
                return new FinStatementStatus[]{GENERATING, CANCELLED};
            case GENERATING:
                return new FinStatementStatus[]{GENERATED, CANCELLED};
            case GENERATED:
                return new FinStatementStatus[]{CUSTOMER_CONFIRMED, COMPANY_CONFIRMED, DISPUTED, CANCELLED};
            case CUSTOMER_CONFIRMED:
                return new FinStatementStatus[]{CONFIRMED, DISPUTED};
            case COMPANY_CONFIRMED:
                return new FinStatementStatus[]{CONFIRMED, DISPUTED};
            case PARTIALLY_CONFIRMED:
                return new FinStatementStatus[]{CONFIRMED, DISPUTED};
            case DISPUTED:
                return new FinStatementStatus[]{GENERATED, PARTIALLY_CONFIRMED, CANCELLED};
            case CONFIRMED:
            case CANCELLED:
            default:
                return new FinStatementStatus[]{};
        }
    }
}
