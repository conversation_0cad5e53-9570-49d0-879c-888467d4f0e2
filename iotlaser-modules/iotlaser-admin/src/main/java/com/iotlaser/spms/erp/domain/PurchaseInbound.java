package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 采购入库对象 erp_purchase_inbound
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_purchase_inbound")
public class PurchaseInbound extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 入库单ID
     */
    @TableId(value = "inbound_id")
    private Long inboundId;

    /**
     * 入库单编号
     */
    private String inboundCode;

    /**
     * 入库单名称
     */
    private String inboundName;

    /**
     * 采购订单ID
     */
    private Long orderId;

    /**
     * 采购订单编码
     */
    private String orderCode;

    /**
     * 采购订单名称
     */
    private String orderName;

    /**
     * 检验单ID
     */
    private Long inspectionId;

    /**
     * 检验单编号
     */
    private String inspectionCode;

    /**
     * 检验单名称
     */
    private String inspectionName;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 入库日期
     */
    private LocalDate inboundDate;

    /**
     * 入库状态
     */
    private PurchaseInboundStatus inboundStatus;

    /**
     * 收货负责人ID
     */
    private Long handlerId;

    /**
     * 收货负责人
     */
    private String handlerName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 明细
     */
    @TableField(exist = false)
    private List<PurchaseInboundItem> items;

    // ==================== 临时变量：汇总字段 ====================
    // TODO: 待数据库结构完善后，这些字段应该持久化到数据库

    /**
     * 总数量（临时变量）
     * TODO: 需要在数据库中添加 total_quantity DECIMAL(15,4) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalQuantity;

    /**
     * 总金额（临时变量）
     * TODO: 需要在数据库中添加 total_amount DECIMAL(15,2) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalAmount;
}
