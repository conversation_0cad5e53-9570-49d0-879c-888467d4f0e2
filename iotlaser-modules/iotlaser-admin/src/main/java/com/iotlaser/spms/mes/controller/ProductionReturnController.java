package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.ProductionReturnBo;
import com.iotlaser.spms.mes.domain.vo.ProductionReturnVo;
import com.iotlaser.spms.mes.service.IProductionReturnService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产退料
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/productionReturn")
public class ProductionReturnController extends BaseController {

    private final IProductionReturnService productionReturnService;

    /**
     * 查询生产退料列表
     */
    @SaCheckPermission("mes:productionReturn:list")
    @GetMapping("/list")
    public TableDataInfo<ProductionReturnVo> list(ProductionReturnBo bo, PageQuery pageQuery) {
        return productionReturnService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产退料列表
     */
    @SaCheckPermission("mes:productionReturn:export")
    @Log(title = "生产退料", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductionReturnBo bo, HttpServletResponse response) {
        List<ProductionReturnVo> list = productionReturnService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产退料", ProductionReturnVo.class, response);
    }

    /**
     * 获取生产退料详细信息
     *
     * @param returnId 主键
     */
    @SaCheckPermission("mes:productionReturn:query")
    @GetMapping("/{returnId}")
    public R<ProductionReturnVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long returnId) {
        return R.ok(productionReturnService.queryById(returnId));
    }

    /**
     * 新增生产退料
     */
    @SaCheckPermission("mes:productionReturn:add")
    @Log(title = "生产退料", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductionReturnBo bo) {
        Boolean result = productionReturnService.insertByBo(bo);
        return result ? R.ok() : R.fail("新增生产退料失败");
    }

    /**
     * 修改生产退料
     */
    @SaCheckPermission("mes:productionReturn:edit")
    @Log(title = "生产退料", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductionReturnBo bo) {
        Boolean result = productionReturnService.updateByBo(bo);
        return result ? R.ok() : R.fail("修改生产退料失败");
    }

    /**
     * 删除生产退料
     *
     * @param returnIds 主键串
     */
    @SaCheckPermission("mes:productionReturn:remove")
    @Log(title = "生产退料", businessType = BusinessType.DELETE)
    @DeleteMapping("/{returnIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] returnIds) {
        return toAjax(productionReturnService.deleteWithValidByIds(List.of(returnIds), true));
    }

    /**
     * 确认生产退料单
     *
     * @param returnId 退料单ID
     */
    @SaCheckPermission("mes:productionReturn:edit")
    @Log(title = "确认生产退料单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{returnId}")
    public R<Void> confirm(@NotNull(message = "退料单ID不能为空") @PathVariable Long returnId) {
        return toAjax(productionReturnService.confirmReturn(returnId));
    }

    /**
     * 批量确认生产退料单
     *
     * @param returnIds 退料单ID集合
     */
    @SaCheckPermission("mes:productionReturn:edit")
    @Log(title = "批量确认生产退料单", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "退料单ID不能为空") @RequestBody Long[] returnIds) {
        return toAjax(productionReturnService.batchConfirmReturns(List.of(returnIds)));
    }

    /**
     * 完成生产退料入库
     *
     * @param returnId 退料单ID
     */
    @SaCheckPermission("mes:productionReturn:edit")
    @Log(title = "完成生产退料入库", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{returnId}")
    public R<Void> complete(@NotNull(message = "退料单ID不能为空") @PathVariable Long returnId) {
        return toAjax(productionReturnService.completeReturn(returnId));
    }

    /**
     * 取消生产退料单
     *
     * @param returnId 退料单ID
     * @param reason   取消原因
     */
    @SaCheckPermission("mes:productionReturn:edit")
    @Log(title = "取消生产退料单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{returnId}")
    public R<Void> cancel(@NotNull(message = "退料单ID不能为空") @PathVariable Long returnId,
                          @RequestParam(required = false) String reason) {
        return toAjax(productionReturnService.cancelReturn(returnId, reason));
    }

    /**
     * 根据生产领料单创建退料单
     *
     * @param productionIssueId 生产领料单ID
     */
    @SaCheckPermission("mes:productionReturn:add")
    @Log(title = "从生产领料单创建退料单", businessType = BusinessType.INSERT)
    @PostMapping("/createFromProductionIssue/{productionIssueId}")
    public R<ProductionReturnVo> createFromProductionIssue(@NotNull(message = "生产领料单ID不能为空") @PathVariable Long productionIssueId) {
        return R.ok(productionReturnService.createFromProductionIssue(productionIssueId));
    }
}
