package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundItemVo;
import com.iotlaser.spms.erp.service.ISaleOutboundItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售出库明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/saleOutboundItem")
public class SaleOutboundItemController extends BaseController {

    private final ISaleOutboundItemService saleOutboundItemService;

    /**
     * 查询销售出库明细列表
     */
    @SaCheckPermission("erp:saleOutboundItem:list")
    @GetMapping("/list")
    public TableDataInfo<SaleOutboundItemVo> list(SaleOutboundItemBo bo, PageQuery pageQuery) {
        return saleOutboundItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出销售出库明细列表
     */
    @SaCheckPermission("erp:saleOutboundItem:export")
    @Log(title = "销售出库明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SaleOutboundItemBo bo, HttpServletResponse response) {
        List<SaleOutboundItemVo> list = saleOutboundItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "销售出库明细", SaleOutboundItemVo.class, response);
    }

    /**
     * 获取销售出库明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("erp:saleOutboundItem:query")
    @GetMapping("/{itemId}")
    public R<SaleOutboundItemVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long itemId) {
        return R.ok(saleOutboundItemService.queryById(itemId));
    }

    /**
     * 新增销售出库明细
     */
    @SaCheckPermission("erp:saleOutboundItem:add")
    @Log(title = "销售出库明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SaleOutboundItemBo bo) {
        return toAjax(saleOutboundItemService.insertByBo(bo));
    }

    /**
     * 修改销售出库明细
     */
    @SaCheckPermission("erp:saleOutboundItem:edit")
    @Log(title = "销售出库明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SaleOutboundItemBo bo) {
        return toAjax(saleOutboundItemService.updateByBo(bo));
    }

    /**
     * 删除销售出库明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("erp:saleOutboundItem:remove")
    @Log(title = "销售出库明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(saleOutboundItemService.deleteWithValidByIds(List.of(itemIds), true));
    }
}
