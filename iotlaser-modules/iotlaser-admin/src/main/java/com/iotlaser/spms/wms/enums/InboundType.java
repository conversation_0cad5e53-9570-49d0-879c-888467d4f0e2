package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 入库类型枚举
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@Getter
@AllArgsConstructor
public enum InboundType implements IDictEnum<String> {
    INIT_INBOUND("init_inbound", "初始化入库", "系统初始化库存入库"),
    SALE_RETURN_INBOUND("sale_return_inbound", "销售退货入库", "销售退货单入库"),
    PURCHASE_ORDER_INBOUND("purchase_order_inbound", "采购订单入库", "采购订单直接入库"),
    PURCHASE_INBOUND("purchase_inbound", "采购入库", "采购入库单入库"),
    PRODUCTION_INBOUND("production_inbound", "生产入库", "生产完工入库"),
    PRODUCTION_RETURN_INBOUND("production_return_inbound", "生产退料入库", "生产退料入库"),
    TRANSFER_INBOUND("transfer_inbound", "移库入库", "库位移库入库"),
    OTHER_INBOUND("other_inbound", "其他入库", "其他原因入库");

    public final static String DICT_CODE = "wms_inbound_type";
    public final static String DICT_NAME = "入库类型";
    public final static String DICT_DESC = "定义不同业务场景的入库类型，包括采购入库、生产入库、退货入库等";

    @EnumValue
    private final String value;
    private final String name;
    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static InboundType getByValue(String value) {
        for (InboundType type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
