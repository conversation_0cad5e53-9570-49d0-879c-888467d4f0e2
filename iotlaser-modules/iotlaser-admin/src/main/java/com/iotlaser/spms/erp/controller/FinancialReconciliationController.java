package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.vo.FinancialReconciliationVo;
import com.iotlaser.spms.erp.service.IFinancialReconciliationService;
import com.iotlaser.spms.erp.service.IPurchaseOrderService;
import com.iotlaser.spms.erp.service.ISaleOrderService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 财务对账控制器
 * 提供销售和采购业务的财务对账功能
 *
 * <AUTHOR>
 * @date 2024-12-22
 * @updated Augment Agent 2025-06-24
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/erp/reconciliation")
public class FinancialReconciliationController extends BaseController {

    private final ISaleOrderService saleOrderService;
    private final IPurchaseOrderService purchaseOrderService;
    private final IFinancialReconciliationService financialReconciliationService;

    /**
     * 销售订单财务对账
     *
     * @param saleOrderId 销售订单ID
     * @return 对账结果
     */
    @SaCheckPermission("erp:reconciliation:sale:query")
    @Log(title = "销售订单财务对账", businessType = BusinessType.OTHER)
    @GetMapping("/sale/{saleOrderId}")
    public R<FinancialReconciliationVo> reconcileSaleOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long saleOrderId) {
        try {
            log.info("开始销售订单财务对账 - 订单ID: {}", saleOrderId);

            // 使用新的财务对账服务
            FinancialReconciliationVo result = financialReconciliationService.reconcileOrder(saleOrderId);

            log.info("销售订单财务对账完成 - 订单ID: {}, 状态: {}", saleOrderId, result.getReconciliationStatus());
            return R.ok(result);
        } catch (Exception e) {
            log.error("销售订单财务对账失败 - 订单ID: {}, 错误: {}", saleOrderId, e.getMessage(), e);
            return R.fail("销售订单财务对账失败：" + e.getMessage());
        }
    }

    /**
     * 批量销售订单财务对账
     *
     * @param orderIds 销售订单ID列表
     * @return 对账结果列表
     */
    @SaCheckPermission("erp:reconciliation:sale:batchQuery")
    @Log(title = "批量销售订单财务对账", businessType = BusinessType.OTHER)
    @PostMapping("/sale/batch")
    public R<List<FinancialReconciliationVo>> batchReconcileSaleOrders(@NotEmpty(message = "订单ID列表不能为空") @RequestBody List<Long> orderIds) {
        try {
            log.info("开始批量销售订单财务对账 - 订单数量: {}", orderIds.size());

            List<FinancialReconciliationVo> results = financialReconciliationService.batchReconcileOrders(orderIds);

            log.info("批量销售订单财务对账完成 - 处理数量: {}", results.size());
            return R.ok(results);
        } catch (Exception e) {
            log.error("批量销售订单财务对账失败 - 错误: {}", e.getMessage(), e);
            return R.fail("批量销售订单财务对账失败：" + e.getMessage());
        }
    }

    /**
     * 采购订单财务对账
     *
     * @param purchaseOrderId 采购订单ID
     * @return 对账结果
     */
    @SaCheckPermission("erp:reconciliation:purchase:query")
    @GetMapping("/purchase/{purchaseOrderId}")
    public R<Map<String, Object>> reconcilePurchaseOrder(@PathVariable Long purchaseOrderId) {
        try {
            log.info("开始采购订单财务对账 - 订单ID: {}", purchaseOrderId);

            // TODO: 实现采购订单财务对账逻辑
            Map<String, Object> result = purchaseOrderService.reconcilePurchaseOrder(purchaseOrderId);

            log.info("采购订单财务对账完成 - 订单ID: {}, 结果: {}", purchaseOrderId, result);
            return R.ok(result);
        } catch (Exception e) {
            log.error("采购订单财务对账失败 - 订单ID: {}, 错误: {}", purchaseOrderId, e.getMessage(), e);
            return R.fail("采购订单财务对账失败：" + e.getMessage());
        }
    }

    /**
     * 批量财务对账
     *
     * @param request 批量对账请求
     * @return 批量对账结果
     */
    @SaCheckPermission("erp:reconciliation:batch:execute")
    @PostMapping("/batch")
    public R<Map<String, Object>> batchReconciliation(@RequestBody @Validated BatchReconciliationRequest request) {
        try {
            log.info("开始批量财务对账 - 请求: {}", request);

            // TODO: 实现批量财务对账逻辑
            Map<String, Object> result = Map.of(
                "success", true,
                "message", "批量财务对账功能待实现",
                "request", request
            );

            log.info("批量财务对账完成 - 结果: {}", result);
            return R.ok(result);
        } catch (Exception e) {
            log.error("批量财务对账失败 - 请求: {}, 错误: {}", request, e.getMessage(), e);
            return R.fail("批量财务对账失败：" + e.getMessage());
        }
    }

    /**
     * 财务对账报告
     *
     * @param request 报告请求
     * @return 对账报告
     */
    @SaCheckPermission("erp:reconciliation:report:query")
    @PostMapping("/report")
    public R<Map<String, Object>> reconciliationReport(@RequestBody @Validated ReconciliationReportRequest request) {
        try {
            log.info("开始生成财务对账报告 - 请求: {}", request);

            // TODO: 实现财务对账报告逻辑
            Map<String, Object> result = Map.of(
                "success", true,
                "message", "财务对账报告功能待实现",
                "request", request
            );

            log.info("财务对账报告生成完成 - 结果: {}", result);
            return R.ok(result);
        } catch (Exception e) {
            log.error("财务对账报告生成失败 - 请求: {}, 错误: {}", request, e.getMessage(), e);
            return R.fail("财务对账报告生成失败：" + e.getMessage());
        }
    }

    /**
     * 按客户进行财务对账
     *
     * @param customerId 客户ID
     * @param startDate  开始日期
     * @param endDate    结束日期
     */
    @SaCheckPermission("erp:reconciliation:customer:query")
    @Log(title = "按客户财务对账", businessType = BusinessType.OTHER)
    @GetMapping("/customer/{customerId}")
    public R<List<FinancialReconciliationVo>> reconcileByCustomer(
        @NotNull(message = "客户ID不能为空") @PathVariable Long customerId,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        List<FinancialReconciliationVo> results = financialReconciliationService.reconcileByCustomer(customerId, startDate, endDate);
        return R.ok(results);
    }

    /**
     * 按日期范围进行财务对账
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param pageQuery 分页参数
     */
    @SaCheckPermission("erp:reconciliation:dateRange:query")
    @Log(title = "按日期范围财务对账", businessType = BusinessType.OTHER)
    @GetMapping("/dateRange")
    public TableDataInfo<FinancialReconciliationVo> reconcileByDateRange(
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
        PageQuery pageQuery) {
        return financialReconciliationService.reconcileByDateRange(startDate, endDate, pageQuery);
    }

    // ==================== 新增的财务对账API ====================

    /**
     * 获取对账差异报告
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @SaCheckPermission("erp:reconciliation:difference:report")
    @Log(title = "对账差异报告", businessType = BusinessType.EXPORT)
    @GetMapping("/difference/report")
    public R<List<FinancialReconciliationVo>> getDifferenceReport(
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        List<FinancialReconciliationVo> results = financialReconciliationService.getDifferenceReport(startDate, endDate);
        return R.ok(results);
    }

    /**
     * 获取对账统计信息
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @SaCheckPermission("erp:reconciliation:statistics:query")
    @GetMapping("/statistics")
    public R<IFinancialReconciliationService.ReconciliationStatistics> getReconciliationStatistics(
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        IFinancialReconciliationService.ReconciliationStatistics statistics =
            financialReconciliationService.getReconciliationStatistics(startDate, endDate);
        return R.ok(statistics);
    }

    /**
     * 检查订单是否存在对账差异
     *
     * @param orderId 销售订单ID
     */
    @SaCheckPermission("erp:reconciliation:difference:check")
    @GetMapping("/difference/check/{orderId}")
    public R<Boolean> hasReconciliationDifference(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        Boolean hasDifference = financialReconciliationService.hasReconciliationDifference(orderId);
        return R.ok(hasDifference);
    }

    /**
     * 获取对账差异详情
     *
     * @param orderId 销售订单ID
     */
    @SaCheckPermission("erp:reconciliation:difference:details")
    @GetMapping("/difference/details/{orderId}")
    public R<List<FinancialReconciliationVo.ReconciliationDifference>> getReconciliationDifferences(
        @NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        List<FinancialReconciliationVo.ReconciliationDifference> differences =
            financialReconciliationService.getReconciliationDifferences(orderId);
        return R.ok(differences);
    }

    /**
     * 标记对账差异已处理
     *
     * @param orderId 销售订单ID
     * @param remark  处理备注
     */
    @SaCheckPermission("erp:reconciliation:difference:resolve")
    @Log(title = "标记差异已处理", businessType = BusinessType.UPDATE)
    @PostMapping("/difference/resolve/{orderId}")
    public R<Void> markDifferenceResolved(
        @NotNull(message = "订单ID不能为空") @PathVariable Long orderId,
        @RequestParam(required = false) String remark) {
        Boolean result = financialReconciliationService.markDifferenceResolved(orderId, remark);
        return toAjax(result);
    }

    /**
     * 计算订单的应收金额
     *
     * @param orderId 销售订单ID
     */
    @SaCheckPermission("erp:reconciliation:amount:calculate")
    @GetMapping("/amount/receivable/{orderId}")
    public R<BigDecimal> calculateOrderReceivableAmount(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        BigDecimal amount = financialReconciliationService.calculateOrderReceivableAmount(orderId);
        return R.ok(amount);
    }

    /**
     * 计算订单的已收款金额
     *
     * @param orderId 销售订单ID
     */
    @SaCheckPermission("erp:reconciliation:amount:calculate")
    @GetMapping("/amount/received/{orderId}")
    public R<BigDecimal> calculateOrderReceivedAmount(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        BigDecimal amount = financialReconciliationService.calculateOrderReceivedAmount(orderId);
        return R.ok(amount);
    }

    /**
     * 计算订单的已开票金额
     *
     * @param orderId 销售订单ID
     */
    @SaCheckPermission("erp:reconciliation:amount:calculate")
    @GetMapping("/amount/invoiced/{orderId}")
    public R<BigDecimal> calculateOrderInvoicedAmount(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        BigDecimal amount = financialReconciliationService.calculateOrderInvoicedAmount(orderId);
        return R.ok(amount);
    }

    /**
     * 批量对账请求DTO
     */
    @lombok.Data
    public static class BatchReconciliationRequest {
        /**
         * 业务类型（SALE/PURCHASE）
         */
        private String businessType;

        /**
         * 订单ID列表
         */
        private java.util.List<Long> orderIds;

        /**
         * 对账日期范围开始
         */
        private java.time.LocalDate startDate;

        /**
         * 对账日期范围结束
         */
        private java.time.LocalDate endDate;
    }

    /**
     * 对账报告请求DTO
     */
    @lombok.Data
    public static class ReconciliationReportRequest {
        /**
         * 报告类型（SUMMARY/DETAIL）
         */
        private String reportType;

        /**
         * 业务类型（SALE/PURCHASE/ALL）
         */
        private String businessType;

        /**
         * 报告日期范围开始
         */
        private java.time.LocalDate startDate;

        /**
         * 报告日期范围结束
         */
        private java.time.LocalDate endDate;

        /**
         * 客户/供应商ID（可选）
         */
        private Long partnerId;
    }
}
