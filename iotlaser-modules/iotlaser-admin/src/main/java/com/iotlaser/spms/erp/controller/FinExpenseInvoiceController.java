package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinExpenseInvoiceBo;
import com.iotlaser.spms.erp.domain.vo.FinExpenseInvoiceVo;
import com.iotlaser.spms.erp.service.IFinExpenseInvoiceService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理费用
 *
 * <AUTHOR> Kai
 * @date 2025-06-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finExpenseInvoice")
public class FinExpenseInvoiceController extends BaseController {

    private final IFinExpenseInvoiceService finExpenseInvoiceService;

    /**
     * 查询管理费用列表
     */
    @SaCheckPermission("spms/erp:finExpenseInvoice:list")
    @GetMapping("/list")
    public TableDataInfo<FinExpenseInvoiceVo> list(FinExpenseInvoiceBo bo, PageQuery pageQuery) {
        return finExpenseInvoiceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出管理费用列表
     */
    @SaCheckPermission("spms/erp:finExpenseInvoice:export")
    @Log(title = "管理费用", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinExpenseInvoiceBo bo, HttpServletResponse response) {
        List<FinExpenseInvoiceVo> list = finExpenseInvoiceService.queryList(bo);
        ExcelUtil.exportExcel(list, "管理费用", FinExpenseInvoiceVo.class, response);
    }

    /**
     * 获取管理费用详细信息
     *
     * @param invoiceId 主键
     */
    @SaCheckPermission("spms/erp:finExpenseInvoice:query")
    @GetMapping("/{invoiceId}")
    public R<FinExpenseInvoiceVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long invoiceId) {
        return R.ok(finExpenseInvoiceService.queryById(invoiceId));
    }

    /**
     * 新增管理费用
     */
    @SaCheckPermission("spms/erp:finExpenseInvoice:add")
    @Log(title = "管理费用", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinExpenseInvoiceBo bo) {
        return toAjax(finExpenseInvoiceService.insertByBo(bo));
    }

    /**
     * 修改管理费用
     */
    @SaCheckPermission("spms/erp:finExpenseInvoice:edit")
    @Log(title = "管理费用", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinExpenseInvoiceBo bo) {
        return toAjax(finExpenseInvoiceService.updateByBo(bo));
    }

    /**
     * 删除管理费用
     *
     * @param invoiceIds 主键串
     */
    @SaCheckPermission("spms/erp:finExpenseInvoice:remove")
    @Log(title = "管理费用", businessType = BusinessType.DELETE)
    @DeleteMapping("/{invoiceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] invoiceIds) {
        return toAjax(finExpenseInvoiceService.deleteWithValidByIds(List.of(invoiceIds), true));
    }
}
