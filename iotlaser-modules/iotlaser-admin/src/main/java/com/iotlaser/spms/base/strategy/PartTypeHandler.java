package com.iotlaser.spms.base.strategy;

import com.iotlaser.spms.base.domain.vo.AutoCodePartVo;
import com.iotlaser.spms.base.enums.PartType;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 部分类型处理器
 *
 * <AUTHOR>
 * @date 2025/5/14
 */
@RequiredArgsConstructor
@Component
public class PartTypeHandler {

    private final List<PartTypeTemplate> partTypeTemplatesList;

    @PostConstruct
    public void init() {
    }

    /**
     * 根据提供的自动代码部分信息执行相应的处理逻辑
     * 此方法主要用于根据不同的部分类型代码，选择并执行相应的处理模板
     *
     * @param autoCodePartVo 包含部分类型代码等信息的自动代码部分视图对象
     * @return 处理结果字符串
     * @throws ServiceException 当部分类型代码未知或处理器索引超出范围时抛出
     */
    public String choiceExecute(AutoCodePartVo autoCodePartVo) {
        // 获取部分类型代码
        PartType partTypeEnum = autoCodePartVo.getPartType();

        // 获取枚举类型对应的bean索引（使用ordinal()方法）
        int beanIndex = partTypeEnum.ordinal();
        // 检查索引是否在有效范围内
        if (beanIndex < 0 || beanIndex >= partTypeTemplatesList.size()) {
            throw new ServiceException("类型 " + partTypeEnum.getName() + " 的处理器索引 " + beanIndex + " 超出范围");
        }
        // 根据索引获取对应的处理模板
        PartTypeTemplate handler = partTypeTemplatesList.get(beanIndex);
        // 执行处理并返回结果
        return handler.partHandle(autoCodePartVo);
    }
}
