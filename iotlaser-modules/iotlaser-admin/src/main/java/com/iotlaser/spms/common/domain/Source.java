package com.iotlaser.spms.common.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class Source extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long code;
    private Long name;
    private Date time;
    private String status;
    @TableLogic
    private String delFlag;
    @TableField(exist = false)
    private List<SourceItem> items;
}
