package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.PurchaseReturnItemBatchBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnItemBatchVo;
import com.iotlaser.spms.erp.service.IPurchaseReturnItemBatchService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购退货批次明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/purchaseReturnItemBatch")
public class PurchaseReturnItemBatchController extends BaseController {

    private final IPurchaseReturnItemBatchService purchaseReturnItemBatchService;

    /**
     * 查询采购退货批次明细列表
     */
    @SaCheckPermission("erp:purchaseReturnItemBatch:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseReturnItemBatchVo> list(PurchaseReturnItemBatchBo bo, PageQuery pageQuery) {
        return purchaseReturnItemBatchService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出采购退货批次明细列表
     */
    @SaCheckPermission("erp:purchaseReturnItemBatch:export")
    @Log(title = "采购退货批次明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseReturnItemBatchBo bo, HttpServletResponse response) {
        List<PurchaseReturnItemBatchVo> list = purchaseReturnItemBatchService.queryList(bo);
        ExcelUtil.exportExcel(list, "采购退货批次明细", PurchaseReturnItemBatchVo.class, response);
    }

    /**
     * 获取采购退货批次明细详细信息
     *
     * @param batchId 主键
     */
    @SaCheckPermission("erp:purchaseReturnItemBatch:query")
    @GetMapping("/{batchId}")
    public R<PurchaseReturnItemBatchVo> getInfo(@NotNull(message = "主键不能为空")
                                                @PathVariable Long batchId) {
        return R.ok(purchaseReturnItemBatchService.queryById(batchId));
    }

    /**
     * 新增采购退货批次明细
     */
    @SaCheckPermission("erp:purchaseReturnItemBatch:add")
    @Log(title = "采购退货批次明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PurchaseReturnItemBatchBo bo) {
        return toAjax(purchaseReturnItemBatchService.insertByBo(bo));
    }

    /**
     * 修改采购退货批次明细
     */
    @SaCheckPermission("erp:purchaseReturnItemBatch:edit")
    @Log(title = "采购退货批次明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PurchaseReturnItemBatchBo bo) {
        return toAjax(purchaseReturnItemBatchService.updateByBo(bo));
    }

    /**
     * 删除采购退货批次明细
     *
     * @param batchIds 主键串
     */
    @SaCheckPermission("erp:purchaseReturnItemBatch:remove")
    @Log(title = "采购退货批次明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{batchIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] batchIds) {
        return toAjax(purchaseReturnItemBatchService.deleteWithValidByIds(List.of(batchIds), true));
    }
}
