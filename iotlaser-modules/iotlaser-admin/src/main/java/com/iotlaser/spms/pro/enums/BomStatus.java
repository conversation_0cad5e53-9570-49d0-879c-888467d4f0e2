package com.iotlaser.spms.pro.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * BOM状态枚举
 * 用于管理物料清单的生命周期状态，从编制到生效的完整流程
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Getter
@AllArgsConstructor
public enum BomStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "编制中"),
    PENDING_REVIEW("pending_review", "待审核", "等待审核"),
    APPROVED("approved", "已审核", "审核通过"),
    ACTIVE("active", "生效", "可用于生产"),
    INACTIVE("inactive", "失效", "不可用于生产"),
    ARCHIVED("archived", "归档", "已归档");

    public final static String DICT_CODE = "pro_bom_status";
    public final static String DICT_NAME = "BOM状态";
    public final static String DICT_DESC = "管理物料清单的生命周期状态，从编制、审核到生效的完整流程状态";
    @EnumValue
    private final String value;
    private final String name;
    private final String desc;

    /**
     * 根据状态代码获取枚举
     *
     * @param value 状态代码
     * @return BOM状态枚举
     */
    public static BomStatus getByValue(String value) {
        for (BomStatus bomStatus : values()) {
            if (bomStatus.getValue().equals(value)) {
                return bomStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
