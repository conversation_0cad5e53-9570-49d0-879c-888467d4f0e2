package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinAccountLedger;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 账户收支流水业务对象 erp_fin_account_ledger
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinAccountLedger.class, reverseConvertGenerate = false)
public class FinAccountLedgerBo extends BaseEntity {

    /**
     * 收支ID
     */
    @NotNull(message = "收支ID不能为空", groups = {EditGroup.class})
    private Long ledgerId;

    /**
     * 账号ID
     */
    @NotNull(message = "账号ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long accountId;

    /**
     * 银行交易流水号
     */
    private String blankSerialNumber;

    /**
     * 交易发生时间
     */
    private Date transactionTime;

    /**
     * 方向
     */
    private String direction;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易前账户余额
     */
    private Long balanceBefore;

    /**
     * 交易后账户余额
     */
    private Long balanceAfter;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 来源编号
     */
    private String sourceCode;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 往来单位ID
     */
    private Long partnerId;

    /**
     * 往来单位编码
     */
    private String partnerCode;

    /**
     * 往来单位名称
     */
    private String partnerName;

    /**
     * 往来单位账号
     */
    private String partnerAccount;

    /**
     * 流水状态
     */
    private String ledgerStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;


}
