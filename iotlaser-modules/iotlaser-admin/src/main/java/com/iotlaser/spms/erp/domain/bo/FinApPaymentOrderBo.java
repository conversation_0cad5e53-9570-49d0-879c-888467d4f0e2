package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinApPaymentOrder;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 付款单业务对象 erp_fin_ap_payment_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinApPaymentOrder.class, reverseConvertGenerate = false)
public class FinApPaymentOrderBo extends BaseEntity {

    /**
     * 付款ID
     */
    @NotNull(message = "付款ID不能为空", groups = {EditGroup.class})
    private Long paymentId;

    /**
     * 付款编码
     */
    private String paymentCode;

    /**
     * 付款名称
     */
    private String paymentName;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 付款金额
     */
    private BigDecimal paymentAmount;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 银行交易流水号
     */
    private String blankSerialNumber;

    /**
     * 已核销金额
     */
    private BigDecimal appliedAmount;

    /**
     * 未核销金额
     */
    private BigDecimal unappliedAmount;

    /**
     * 付款时间
     */
    private LocalDate paymentDate;

    /**
     * 付款状态
     */
    private String paymentStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 账号ID
     */
    @NotNull(message = "账号ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long accountId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;


}
