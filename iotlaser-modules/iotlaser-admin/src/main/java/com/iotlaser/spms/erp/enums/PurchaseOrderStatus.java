package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PurchaseOrderStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "订单已创建但未确认，可任意修改"),
    PENDING_APPROVAL("pending_approval", "待审批", "订单已提交审批，等待审批人处理"),
    CONFIRMED("confirmed", "已确认", "订单已审核，发往供应商，通常锁定关键信息（如供应商、物料）"),
    PARTIALLY_RECEIVED("partially_received", "部分到货", "已收到部分订购的货物"),
    FULLY_RECEIVED("fully_received", "全部到货", "订单所有物料已按数量收货完毕"),
    CLOSED("closed", "已关闭", "订单已完成且相关财务（如付款）流程已结束，订单归档"),
    CANCELLED("cancelled", "已取消", "在货物接收前，订单被作废");

    public final static String DICT_CODE = "erp_purchase_order_status";
    public final static String DICT_NAME = "采购订单状态";
    public final static String DICT_DESC = "管理采购订单的流程状态，从草稿创建到审批确认、收货完成的完整流程";
    @EnumValue
    private final String value;
    private final String name;
    private final String desc;

    /**
     * 根据状态代码获取枚举
     *
     * @param status 状态代码
     * @return 采购订单状态枚举
     */
    public static PurchaseOrderStatus getByStatus(String status) {
        for (PurchaseOrderStatus orderStatus : values()) {
            if (orderStatus.getValue().equals(status)) {
                return orderStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否为可编辑状态
     *
     * @return 是否可编辑
     */
    public boolean isEditable() {
        return this == DRAFT;
    }

    /**
     * 判断是否为可删除状态
     *
     * @return 是否可删除
     */
    public boolean isDeletable() {
        return this == DRAFT;
    }

    /**
     * 判断是否为待审批状态
     *
     * @return 是否待审批
     */
    public boolean isPendingApproval() {
        return this == PENDING_APPROVAL;
    }

    /**
     * 判断是否为已完成状态
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return this == FULLY_RECEIVED || this == CLOSED || this == CANCELLED;
    }

    /**
     * 判断是否可以收货
     *
     * @return 是否可以收货
     */
    public boolean canReceive() {
        return this == CONFIRMED || this == PARTIALLY_RECEIVED;
    }

    /**
     * 判断是否可以审批
     *
     * @return 是否可以审批
     */
    public boolean canApprove() {
        return this == PENDING_APPROVAL;
    }

    /**
     * 获取下一个可能的状态
     *
     * @return 下一个可能的状态列表
     */
    public PurchaseOrderStatus[] getNextPossibleStates() {
        switch (this) {
            case DRAFT:
                return new PurchaseOrderStatus[]{PENDING_APPROVAL, CONFIRMED, CANCELLED};
            case PENDING_APPROVAL:
                return new PurchaseOrderStatus[]{CONFIRMED, DRAFT, CANCELLED};
            case CONFIRMED:
                return new PurchaseOrderStatus[]{PARTIALLY_RECEIVED, FULLY_RECEIVED, CANCELLED};
            case PARTIALLY_RECEIVED:
                return new PurchaseOrderStatus[]{FULLY_RECEIVED, CANCELLED};
            case FULLY_RECEIVED:
                return new PurchaseOrderStatus[]{CLOSED};
            case CLOSED:
            case CANCELLED:
            default:
                return new PurchaseOrderStatus[]{};
        }
    }
}
