package com.iotlaser.spms.base.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PartType implements IDictEnum<String> {

    PART_TYPE_INPUTCHAR("INPUTCHAR", "传入字符", "用户输入的字符"),
    PART_TYPE_NOWDATE("NOWDATE", "当前日期", "系统当前日期"),
    PART_TYPE_FIXCHAR("FIXCHAR", "固定字符", "固定不变的字符"),
    PART_TYPE_SERIALNO("SERIALNO", "流水号", "自增的流水号"),
    PART_TYPE_OTHER("OTHER", "其他", "其他类型");

    public final static String DICT_CODE = "base_part_type";
    public final static String DICT_NAME = "编码部件类型";
    public final static String DICT_DESC = "定义编码生成规则中各个部件的类型，用于构建完整的编码规则";
    /**
     * 部件类型值
     */
    @EnumValue
    private final String value;
    /**
     * 部件类型名称
     */
    private final String name;
    /**
     * 部件类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 部件类型值
     * @return 部件类型枚举
     */
    public static PartType getByValue(String value) {
        for (PartType partType : values()) {
            if (partType.getValue().equals(value)) {
                return partType;
            }
        }
        return PART_TYPE_OTHER;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
