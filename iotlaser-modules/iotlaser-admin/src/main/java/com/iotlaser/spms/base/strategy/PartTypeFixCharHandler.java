package com.iotlaser.spms.base.strategy;

import com.iotlaser.spms.base.domain.vo.AutoCodePartVo;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 固定字符处理
 */
@Component
@Order(2)
public class PartTypeFixCharHandler implements PartTypeTemplate {

    /**
     * 处理部分自动代码生成的逻辑
     * 此方法专注于处理基础自动代码部分的固定字符生成
     *
     * @param vo 包含固定字符信息的自动代码部分对象
     * @return 返回基础自动代码部分的固定字符
     */
    @Override
    public String partHandle(AutoCodePartVo vo) {
        return vo.getFixCharacter();
    }
}
