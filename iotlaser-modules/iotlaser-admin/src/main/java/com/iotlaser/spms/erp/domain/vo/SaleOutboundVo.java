package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售出库视图对象 erp_sale_outbound
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SaleOutbound.class)
public class SaleOutboundVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 出库单ID
     */
    @ExcelProperty(value = "出库单ID")
    private Long outboundId;

    /**
     * 出库单编号
     */
    @ExcelProperty(value = "出库单编号")
    private String outboundCode;

    /**
     * 出库单名称
     */
    @ExcelProperty(value = "出库单名称")
    private String outboundName;

    /**
     * 销售订单ID
     */
    @ExcelProperty(value = "销售订单ID")
    private Long orderId;

    /**
     * 销售订单编号
     */
    @ExcelProperty(value = "销售订单编号")
    private String orderCode;

    /**
     * 销售订单名称
     */
    @ExcelProperty(value = "销售订单名称")
    private String orderName;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 客户编码
     */
    @ExcelProperty(value = "客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String customerName;

    /**
     * 出库日期
     */
    @ExcelProperty(value = "出库日期")
    private LocalDate outboundDate;

    /**
     * 出库状态
     */
    @ExcelProperty(value = "出库状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_sale_outbound_status")
    private SaleOutboundStatus outboundStatus;

    /**
     * 发货负责人ID
     */
    @ExcelProperty(value = "发货负责人ID")
    private Long handlerId;

    /**
     * 发货负责人
     */
    @ExcelProperty(value = "发货负责人")
    private String handlerName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

    /**
     * 明细
     */
    private List<SaleOutboundItemVo> items;
}
