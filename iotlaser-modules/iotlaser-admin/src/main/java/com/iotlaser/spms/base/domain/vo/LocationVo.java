package com.iotlaser.spms.base.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.base.domain.Location;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;

/**
 * 位置库位视图对象 base_location
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Location.class)
public class LocationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 位置库位ID
     */
    @ExcelProperty(value = "位置库位ID")
    private Long locationId;

    /**
     * 位置库位编码
     */
    @ExcelProperty(value = "位置库位编码")
    private String locationCode;

    /**
     * 位置库位名称
     */
    @ExcelProperty(value = "位置库位名称")
    private String locationName;

    /**
     * 库位类型
     */
    @ExcelProperty(value = "库位类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "base_location_type")
    private String locationType;

    /**
     * 上级节点
     */
    @ExcelProperty(value = "上级节点")
    private Long parentId;

    /**
     * 排列顺序
     */
    @ExcelProperty(value = "排列顺序")
    private Long orderNum;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

}
