package com.iotlaser.spms.base.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 自动编码类型枚举
 * <p>
 * 定义系统中各个模块的自动编码类型，用于自动编码生成服务
 * 包含BASE、PRO、ERP、WMS、MES、APS、QMS等模块的编码类型
 *
 * <AUTHOR> <PERSON>
 * @date 2025/5/1
 */
@Getter
@AllArgsConstructor
public enum GenCodeType implements IDictEnum<String> {
    BASE_COMPANY_CODE("BASE_COMPANY_CODE", "公司编码", "基础数据公司编码生成"),
    BASE_LOCATION_CODE("BASE_LOCATION_CODE", "位置库位编码", "基础数据库位编码生成"),
    PRO_PRODUCT_CATEGORY_CODE("PRO_PRODUCT_CATEGORY_CODE", "产品类别编码", "产品类别编码生成"),
    PRO_PRODUCT_CODE("PRO_PRODUCT_CODE", "产品编码", "产品编码生成"),
    PRO_BOM_CODE("PRO_BOM_CODE", "BOM编码", "BOM清单编码生成"),
    PRO_BATCH_CODE("PRO_BATCH_CODE", "批次序列号", "产品批次序列号生成"),
    PRO_INSTANCE_CODE("PRO_INSTANCE_CODE", "产品实例编码", "产品实例编码生成"),
    PRO_ROUTING_CODE("PRO_ROUTING_CODE", "工艺路线编码", "工艺路线编码生成"),
    PRO_PROCESS_CODE("PRO_PROCESS_CODE", "工艺流程编码", "工艺流程编码生成"),
    ERP_SALE_ORDER_CODE("ERP_SALE_ORDER_CODE", "销售订单编码", "ERP销售订单编码生成"),
    ERP_SALE_OUTBOUND_CODE("ERP_SALE_OUTBOUND_CODE", "销售出货编码", "ERP销售出库编码生成"),
    ERP_SALE_RETURN_CODE("ERP_SALE_RETURN_CODE", "销售退货编码", "ERP销售退货编码生成"),
    ERP_PURCHASE_ORDER_CODE("ERP_PURCHASE_ORDER_CODE", "采购订单编码", "ERP采购订单编码生成"),
    ERP_PURCHASE_INBOUND_CODE("ERP_PURCHASE_INBOUND_CODE", "采购入库编码", "ERP采购入库编码生成"),
    ERP_PURCHASE_RETURN_CODE("ERP_PURCHASE_RETURN_CODE", "采购退货编码", "ERP采购退货编码生成"),
    ERP_AR_RECEIVABLE_CODE("ERP_AR_RECEIVABLE_CODE", "应收账款编码", "ERP应收账款编码生成"),
    ERP_AR_PAYMENT_CODE("ERP_AR_PAYMENT_CODE", "应收付款编码", "ERP应收付款编码生成"),
    ERP_AP_INVOICE_CODE("ERP_AP_INVOICE_CODE", "应付发票编码", "ERP应付发票编码生成"),
    ERP_AP_STATEMENT_CODE("ERP_AP_STATEMENT_CODE", "应付对账编码", "ERP应付对账编码生成"),
    WMS_INBOUND_CODE("WMS_INBOUND_CODE", "产品入库编码", "WMS产品入库编码生成"),
    WMS_OUTBOUND_CODE("WMS_OUTBOUND_CODE", "生产出库编码", "WMS生产出库编码生成"),
    WMS_TRANSFER_CODE("WMS_TRANSFER_CODE", "生产移库编码", "WMS生产移库编码生成"),
    WMS_INVENTORY_CHECK_CODE("WMS_INVENTORY_CHECK_CODE", "库存盘点编码", "WMS库存盘点编码生成"),
    MES_PRODUCTION_ORDER_CODE("MES_PRODUCTION_ORDER_CODE", "生产订单编码", "MES生产订单编码生成"),
    MES_PRODUCTION_ISSUE_CODE("MES_PRODUCTION_ISSUE_CODE", "生产领料编码", "MES生产领料编码生成"),
    MES_PRODUCTION_RETURN_CODE("MES_PRODUCTION_RETURN_CODE", "生产退料编码", "MES生产退料编码生成"),
    MES_PRODUCTION_INBOUND_CODE("MES_PRODUCTION_INBOUND_CODE", "生产入库编码", "MES生产入库编码生成"),
    APS_DEMAND_CODE("APS_DEMAND_CODE", "计划需求编码", "APS计划需求编码生成"),
    APS_RUN_CODE("APS_RUN_CODE", "计划运行编码", "APS计划运行编码生成"),
    APS_TASK_CODE("APS_TASK_CODE", "计划任务编码", "APS计划任务编码生成"),
    QMS_INSPECTION_CODE("QMS_INSPECTION_CODE", "质量检验编码", "QMS质量检验编码生成"),
    QMS_INSPECTION_PLAN_CODE("QMS_INSPECTION_PLAN_CODE", "检验计划编码", "QMS检验计划编码生成");;

    public final static String DICT_CODE = "base_gen_code_type";
    public final static String DICT_NAME = "自动编码类型枚举";
    public final static String DICT_DESC = "定义系统中各个模块的自动编码类型，用于自动编码生成服务";

    /**
     * 编码类型值
     */
    @EnumValue
    private final String value;
    /**
     * 编码类型名称
     */
    private final String name;
    /**
     * 编码类型描述
     */
    private final String desc;

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
