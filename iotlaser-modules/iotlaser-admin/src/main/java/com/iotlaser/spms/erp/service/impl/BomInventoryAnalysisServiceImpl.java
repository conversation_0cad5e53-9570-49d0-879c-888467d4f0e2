package com.iotlaser.spms.erp.service.impl;

import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.erp.domain.vo.BomInventoryAnalysisVo;
import com.iotlaser.spms.erp.domain.vo.MaterialShortageVo;
import com.iotlaser.spms.erp.domain.vo.MaterialWarningVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseRequirementVo;
import com.iotlaser.spms.erp.service.IBomInventoryAnalysisService;
import com.iotlaser.spms.erp.service.IPurchaseOrderItemService;
import com.iotlaser.spms.erp.service.IPurchaseOrderService;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.service.IInventoryBatchService;
import com.iotlaser.spms.wms.service.IInventoryLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * BOM清单库存分析服务实现
 *
 * <AUTHOR> Assistant
 * @date 2025-06-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BomInventoryAnalysisServiceImpl implements IBomInventoryAnalysisService {

    // 默认配置常量
    private static final BigDecimal DEFAULT_SAFETY_STOCK_RATE = new BigDecimal("0.1"); // 默认安全库存率10%
    private static final BigDecimal DEFAULT_LOSS_RATE = new BigDecimal("0.05"); // 默认损耗率5%
    private static final Integer DEFAULT_MAX_BOM_LEVEL = 10; // 默认最大BOM展开层级
    private static final Integer CACHE_EXPIRE_MINUTES = 30; // 缓存过期时间（分钟）
    private final IInventoryBatchService inventoryBatchService;
    private final IInventoryLogService inventoryLogService;
    private final IProductService productService;
    private final ICompanyService companyService;
    @Lazy
    @Autowired
    private IPurchaseOrderService purchaseOrderService;
    private final IPurchaseOrderItemService purchaseOrderItemService;
    // 缓存，提高查询性能
    private final Map<String, Object> analysisCache = new ConcurrentHashMap<>();

    @Override
    public BomInventoryAnalysisVo analyzeBomInventory(Long bomId, BigDecimal requiredQuantity) {
        log.info("开始分析BOM库存 - BOM ID: {}, 需求数量: {}", bomId, requiredQuantity);

        try {
            BomInventoryAnalysisVo analysis = new BomInventoryAnalysisVo();
            analysis.setBomId(bomId);
            analysis.setRequiredQuantity(requiredQuantity);
            analysis.setAnalysisTime(LocalDateTime.now());

            // 1. 获取BOM基本信息（基于现有字段模拟）
            setBomBasicInfo(analysis, bomId);

            // 2. 多层级BOM展开，获取所有原材料需求
            Map<Long, BigDecimal> materialRequirements = expandBomMultiLevel(bomId, requiredQuantity, DEFAULT_MAX_BOM_LEVEL);

            // 3. 批量查询原材料库存
            List<Long> materialIds = new ArrayList<>(materialRequirements.keySet());
            Map<Long, BigDecimal> inventoryBalances = batchQueryMaterialInventoryBalance(materialIds, null);

            // 4. 分析每个原材料的库存状况
            List<BomInventoryAnalysisVo.MaterialInventoryDetailVo> materialDetails = new ArrayList<>();
            int shortageCount = 0;
            int warningCount = 0;
            BigDecimal totalPurchaseAmount = BigDecimal.ZERO;

            for (Map.Entry<Long, BigDecimal> entry : materialRequirements.entrySet()) {
                Long materialId = entry.getKey();
                BigDecimal requirement = entry.getValue();

                BomInventoryAnalysisVo.MaterialInventoryDetailVo detail = analyzeMaterialInventory(
                    materialId, requirement, inventoryBalances.get(materialId));
                materialDetails.add(detail);

                // 统计缺料和预警
                if (detail.getShortageQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    shortageCount++;
                    // 估算采购金额（基于历史价格）
                    BigDecimal estimatedPrice = getEstimatedMaterialPrice(materialId);
                    totalPurchaseAmount = totalPurchaseAmount.add(
                        detail.getShortageQuantity().multiply(estimatedPrice));
                }

                if (detail.getIsWarning()) {
                    warningCount++;
                }
            }

            // 5. 设置分析结果
            analysis.setMaterialDetails(materialDetails);
            analysis.setShortageCount(shortageCount);
            analysis.setWarningCount(warningCount);
            analysis.setTotalPurchaseAmount(totalPurchaseAmount);

            // 6. 判断整体库存状态
            if (shortageCount == 0) {
                analysis.setInventoryStatus("SUFFICIENT");
                analysis.setCanProduce(true);
            } else if (shortageCount <= materialRequirements.size() * 0.2) {
                analysis.setInventoryStatus("INSUFFICIENT");
                analysis.setCanProduce(false);
            } else {
                analysis.setInventoryStatus("SHORTAGE");
                analysis.setCanProduce(false);
            }

            // 7. 生成缺料和预警清单
            analysis.setShortageList(generateMaterialShortageReport(bomId, requiredQuantity));
            analysis.setWarningList(generateWarningList(materialIds));

            log.info("BOM库存分析完成 - BOM ID: {}, 状态: {}, 缺料数: {}, 预警数: {}",
                bomId, analysis.getInventoryStatus(), shortageCount, warningCount);

            return analysis;
        } catch (Exception e) {
            log.error("BOM库存分析失败 - BOM ID: {}, 错误: {}", bomId, e.getMessage(), e);
            throw new RuntimeException("BOM库存分析失败：" + e.getMessage());
        }
    }

    @Override
    public List<BomInventoryAnalysisVo> batchAnalyzeBomInventory(Map<Long, BigDecimal> bomRequirements) {
        log.info("开始批量分析BOM库存 - 数量: {}", bomRequirements.size());

        List<BomInventoryAnalysisVo> results = new ArrayList<>();

        for (Map.Entry<Long, BigDecimal> entry : bomRequirements.entrySet()) {
            try {
                BomInventoryAnalysisVo analysis = analyzeBomInventory(entry.getKey(), entry.getValue());
                results.add(analysis);
            } catch (Exception e) {
                log.error("批量分析中单个BOM失败 - BOM ID: {}, 错误: {}", entry.getKey(), e.getMessage());
                // 创建错误记录
                BomInventoryAnalysisVo errorAnalysis = new BomInventoryAnalysisVo();
                errorAnalysis.setBomId(entry.getKey());
                errorAnalysis.setRequiredQuantity(entry.getValue());
                errorAnalysis.setInventoryStatus("ERROR");
                errorAnalysis.setAnalysisRemark("分析失败：" + e.getMessage());
                results.add(errorAnalysis);
            }
        }

        log.info("批量BOM库存分析完成 - 总数: {}, 成功: {}",
            bomRequirements.size(), results.stream().filter(r -> !"ERROR".equals(r.getInventoryStatus())).count());

        return results;
    }

    @Override
    public BigDecimal queryMaterialInventoryBalance(Long materialId, Long locationId) {
        try {
            String cacheKey = "inventory_balance_" + materialId + "_" + (locationId != null ? locationId : "all");

            // 检查缓存
            BigDecimal cachedBalance = (BigDecimal) analysisCache.get(cacheKey);
            if (cachedBalance != null) {
                return cachedBalance;
            }

            // 基于现有字段的库存查询逻辑
            // 这里模拟通过InventoryBatchService查询库存余量
            BigDecimal balance = BigDecimal.ZERO;

            // TODO: 实际实现需要根据现有的库存表结构进行查询
            // 这里提供基于现有字段的查询逻辑示例
            /*
            LambdaQueryWrapper<InventoryBatch> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(InventoryBatch::getProductId, materialId);
            if (locationId != null) {
                wrapper.eq(InventoryBatch::getLocationId, locationId);
            }
            wrapper.eq(InventoryBatch::getInventoryStatus, "AVAILABLE");

            List<InventoryBatchVo> batches = inventoryBatchService.queryList(wrapper);
            balance = batches.stream()
                .map(InventoryBatchVo::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            */

            // 暂时使用模拟数据
            balance = generateMockInventoryBalance(materialId);

            // 缓存结果
            analysisCache.put(cacheKey, balance);

            return balance;
        } catch (Exception e) {
            log.error("查询材料库存余量失败 - 材料ID: {}, 库位ID: {}, 错误: {}", materialId, locationId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    @Override
    public Map<Long, BigDecimal> batchQueryMaterialInventoryBalance(List<Long> materialIds, Long locationId) {
        Map<Long, BigDecimal> balances = new HashMap<>();

        for (Long materialId : materialIds) {
            BigDecimal balance = queryMaterialInventoryBalance(materialId, locationId);
            balances.put(materialId, balance);
        }

        return balances;
    }

    @Override
    public PurchaseRequirementVo calculatePurchaseRequirement(Long bomId, BigDecimal requiredQuantity, Boolean considerInTransit) {
        log.info("计算采购需求 - BOM ID: {}, 需求数量: {}, 考虑在途: {}", bomId, requiredQuantity, considerInTransit);

        try {
            PurchaseRequirementVo requirement = new PurchaseRequirementVo();
            requirement.setBomId(bomId);
            requirement.setProductionQuantity(requiredQuantity);
            requirement.setAnalysisTime(LocalDateTime.now());
            requirement.setRequirementDate(LocalDate.now().plusDays(7)); // 默认一周后需要
            requirement.setUrgencyLevel("MEDIUM");
            requirement.setAnalysisStatus("DRAFT");

            // 1. 获取BOM基本信息
            setBomBasicInfoForRequirement(requirement, bomId);

            // 2. 展开BOM获取原材料需求
            Map<Long, BigDecimal> materialRequirements = expandBomMultiLevel(bomId, requiredQuantity, DEFAULT_MAX_BOM_LEVEL);

            // 3. 生成采购建议
            List<PurchaseRequirementVo.PurchaseSuggestionVo> suggestions = generatePurchaseSuggestionsInternal(
                materialRequirements, considerInTransit);

            requirement.setPurchaseSuggestions(suggestions);
            requirement.setTotalPurchaseItems(suggestions.size());

            // 4. 计算总采购金额
            BigDecimal totalAmount = suggestions.stream()
                .map(PurchaseRequirementVo.PurchaseSuggestionVo::getEstimatedAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            requirement.setTotalPurchaseAmount(totalAmount);

            // 5. 计算预计交期（取最长交期）
            LocalDate maxDeliveryDate = suggestions.stream()
                .map(PurchaseRequirementVo.PurchaseSuggestionVo::getSuggestedDeliveryDate)
                .filter(Objects::nonNull)
                .max(LocalDate::compareTo)
                .orElse(LocalDate.now().plusDays(30));
            requirement.setEstimatedDeliveryDate(maxDeliveryDate);

            log.info("采购需求计算完成 - BOM ID: {}, 采购项目数: {}, 总金额: {}",
                bomId, suggestions.size(), totalAmount);

            return requirement;
        } catch (Exception e) {
            log.error("计算采购需求失败 - BOM ID: {}, 错误: {}", bomId, e.getMessage(), e);
            throw new RuntimeException("计算采购需求失败：" + e.getMessage());
        }
    }

    // 私有辅助方法

    /**
     * 设置BOM基本信息
     */
    private void setBomBasicInfo(BomInventoryAnalysisVo analysis, Long bomId) {
        // 基于现有字段模拟BOM信息
        analysis.setBomCode("BOM-" + bomId);
        analysis.setBomName("BOM清单-" + bomId);
        analysis.setProductId(bomId * 100); // 模拟产品ID
        analysis.setProductCode("PROD-" + bomId);
        analysis.setProductName("产品-" + bomId);
    }

    /**
     * 设置采购需求的BOM基本信息
     */
    private void setBomBasicInfoForRequirement(PurchaseRequirementVo requirement, Long bomId) {
        requirement.setBomCode("BOM-" + bomId);
        requirement.setProductCode("PROD-" + bomId);
        requirement.setProductName("产品-" + bomId);
        requirement.setAnalysisById(1L); // 当前用户ID
        requirement.setAnalysisByName("系统分析");
    }

    /**
     * 分析单个原材料的库存状况
     */
    private BomInventoryAnalysisVo.MaterialInventoryDetailVo analyzeMaterialInventory(
        Long materialId, BigDecimal requirement, BigDecimal currentStock) {

        BomInventoryAnalysisVo.MaterialInventoryDetailVo detail = new BomInventoryAnalysisVo.MaterialInventoryDetailVo();
        detail.setMaterialId(materialId);
        detail.setMaterialCode("MAT-" + materialId);
        detail.setMaterialName("原材料-" + materialId);
        detail.setUnitCode("PCS");
        detail.setUnitName("个");
        detail.setTotalRequirement(requirement);
        detail.setCurrentStock(currentStock != null ? currentStock : BigDecimal.ZERO);

        // 计算可用库存（假设90%可用）
        BigDecimal availableStock = detail.getCurrentStock().multiply(new BigDecimal("0.9"));
        detail.setAvailableStock(availableStock);

        // 查询在途数量
        BigDecimal inTransit = queryInTransitQuantity(materialId);
        detail.setInTransitQuantity(inTransit);

        // 获取安全库存
        BigDecimal safetyStock = getMaterialSafetyStock(materialId);
        detail.setSafetyStock(safetyStock);

        // 计算缺料数量
        BigDecimal totalAvailable = availableStock.add(inTransit);
        BigDecimal shortage = requirement.subtract(totalAvailable);
        detail.setShortageQuantity(shortage.compareTo(BigDecimal.ZERO) > 0 ? shortage : BigDecimal.ZERO);

        // 判断库存状态
        if (shortage.compareTo(BigDecimal.ZERO) > 0) {
            detail.setStockStatus("SHORTAGE");
        } else if (availableStock.compareTo(safetyStock) <= 0) {
            detail.setStockStatus("WARNING");
            detail.setIsWarning(true);
        } else {
            detail.setStockStatus("SUFFICIENT");
        }

        detail.setLastUpdateTime(LocalDateTime.now());

        return detail;
    }

    /**
     * 生成模拟库存余量
     */
    private BigDecimal generateMockInventoryBalance(Long materialId) {
        // 基于材料ID生成模拟库存数据
        Random random = new Random(materialId);
        return new BigDecimal(random.nextInt(1000) + 100);
    }

    /**
     * 获取材料的预估价格
     */
    private BigDecimal getEstimatedMaterialPrice(Long materialId) {
        // 基于材料ID生成模拟价格
        Random random = new Random(materialId);
        return new BigDecimal(random.nextDouble() * 100 + 10).setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public List<MaterialShortageVo> generateMaterialShortageReport(Long bomId, BigDecimal requiredQuantity) {
        log.info("生成缺料分析报告 - BOM ID: {}, 需求数量: {}", bomId, requiredQuantity);

        try {
            List<MaterialShortageVo> shortageList = new ArrayList<>();

            // 1. 展开BOM获取原材料需求
            Map<Long, BigDecimal> materialRequirements = expandBomMultiLevel(bomId, requiredQuantity, DEFAULT_MAX_BOM_LEVEL);

            // 2. 批量查询库存
            List<Long> materialIds = new ArrayList<>(materialRequirements.keySet());
            Map<Long, BigDecimal> inventoryBalances = batchQueryMaterialInventoryBalance(materialIds, null);

            // 3. 分析每个原材料的缺料情况
            for (Map.Entry<Long, BigDecimal> entry : materialRequirements.entrySet()) {
                Long materialId = entry.getKey();
                BigDecimal requirement = entry.getValue();
                BigDecimal currentStock = inventoryBalances.getOrDefault(materialId, BigDecimal.ZERO);
                BigDecimal inTransit = queryInTransitQuantity(materialId);
                BigDecimal safetyStock = getMaterialSafetyStock(materialId);

                // 计算可用库存
                BigDecimal availableStock = currentStock.multiply(new BigDecimal("0.9")); // 90%可用
                BigDecimal totalAvailable = availableStock.add(inTransit);
                BigDecimal shortage = requirement.subtract(totalAvailable);

                // 只记录有缺料的材料
                if (shortage.compareTo(BigDecimal.ZERO) > 0 || availableStock.compareTo(safetyStock) <= 0) {
                    MaterialShortageVo shortageVo = createMaterialShortageVo(
                        materialId, requirement, currentStock, availableStock, inTransit, shortage, safetyStock);
                    shortageList.add(shortageVo);
                }
            }

            // 4. 按紧急程度排序
            shortageList.sort((a, b) -> {
                // 先按紧急程度排序，再按缺料率排序
                int urgencyCompare = getUrgencyPriority(a.getUrgencyLevel()) - getUrgencyPriority(b.getUrgencyLevel());
                if (urgencyCompare != 0) return urgencyCompare;
                return b.getShortageRate().compareTo(a.getShortageRate());
            });

            log.info("缺料分析报告生成完成 - BOM ID: {}, 缺料项目数: {}", bomId, shortageList.size());
            return shortageList;

        } catch (Exception e) {
            log.error("生成缺料分析报告失败 - BOM ID: {}, 错误: {}", bomId, e.getMessage(), e);
            throw new RuntimeException("生成缺料分析报告失败：" + e.getMessage());
        }
    }

    @Override
    public Boolean checkInventoryWarning(Long materialId) {
        try {
            BigDecimal currentStock = queryMaterialInventoryBalance(materialId, null);
            BigDecimal safetyStock = getMaterialSafetyStock(materialId);
            BigDecimal warningThreshold = safetyStock.multiply(new BigDecimal("1.2")); // 安全库存的120%作为预警阈值

            return currentStock.compareTo(warningThreshold) <= 0;
        } catch (Exception e) {
            log.error("检查库存预警失败 - 材料ID: {}, 错误: {}", materialId, e.getMessage());
            return false;
        }
    }

    @Override
    public List<Long> batchCheckInventoryWarning(List<Long> materialIds) {
        return materialIds.stream()
            .filter(this::checkInventoryWarning)
            .collect(Collectors.toList());
    }

    @Override
    public BigDecimal getMaterialSafetyStock(Long materialId) {
        // 基于现有字段的安全库存计算
        // 这里使用平均消耗量的10%作为安全库存
        try {
            BigDecimal averageConsumption = calculateAverageConsumption(materialId);
            return averageConsumption.multiply(DEFAULT_SAFETY_STOCK_RATE);
        } catch (Exception e) {
            log.warn("获取安全库存失败，使用默认值 - 材料ID: {}", materialId);
            return new BigDecimal("50"); // 默认安全库存
        }
    }

    @Override
    public BigDecimal getMaterialMinPurchaseQuantity(Long materialId) {
        // 基于现有字段的最小采购量计算
        // 这里使用安全库存的2倍作为最小采购量
        BigDecimal safetyStock = getMaterialSafetyStock(materialId);
        return safetyStock.multiply(new BigDecimal("2"));
    }

    @Override
    public BigDecimal calculateActualRequirement(Long materialId, BigDecimal theoreticalQuantity) {
        // 考虑损耗率的实际需求量计算
        BigDecimal lossRate = getMaterialLossRate(materialId);
        BigDecimal lossMultiplier = BigDecimal.ONE.add(lossRate);
        return theoreticalQuantity.multiply(lossMultiplier).setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public Map<Long, BigDecimal> expandBomMultiLevel(Long bomId, BigDecimal requiredQuantity, Integer maxLevel) {
        log.debug("多层级BOM展开 - BOM ID: {}, 需求数量: {}, 最大层级: {}", bomId, requiredQuantity, maxLevel);

        Map<Long, BigDecimal> materialRequirements = new HashMap<>();
        Set<Long> processedBoms = new HashSet<>();

        expandBomRecursive(bomId, requiredQuantity, materialRequirements, processedBoms, 0, maxLevel);

        log.debug("BOM展开完成 - 原材料数量: {}", materialRequirements.size());
        return materialRequirements;
    }

    @Override
    public BigDecimal queryInTransitQuantity(Long materialId) {
        try {
            // 基于现有采购订单表查询在途数量
            // TODO: 实际实现需要查询采购订单明细表中状态为"已下单"但"未入库"的数量
            /*
            LambdaQueryWrapper<PurchaseOrderItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PurchaseOrderItem::getProductId, materialId);
            wrapper.in(PurchaseOrderItem::getOrderStatus, Arrays.asList("CONFIRMED", "SHIPPED"));

            List<PurchaseOrderItemVo> items = purchaseOrderItemService.queryList(wrapper);
            return items.stream()
                .map(item -> item.getQuantity().subtract(item.getReceivedQuantity()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            */

            // 暂时使用模拟数据
            Random random = new Random(materialId);
            return new BigDecimal(random.nextInt(200));
        } catch (Exception e) {
            log.error("查询在途数量失败 - 材料ID: {}, 错误: {}", materialId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    @Override
    public List<PurchaseRequirementVo> generatePurchaseSuggestions(Long bomId, BigDecimal requiredQuantity, String priorityLevel) {
        log.info("生成采购建议清单 - BOM ID: {}, 需求数量: {}, 优先级: {}", bomId, requiredQuantity, priorityLevel);

        try {
            List<PurchaseRequirementVo> suggestions = new ArrayList<>();

            // 1. 计算采购需求
            PurchaseRequirementVo requirement = calculatePurchaseRequirement(bomId, requiredQuantity, true);
            requirement.setUrgencyLevel(priorityLevel);

            // 2. 根据优先级调整建议
            adjustSuggestionsByPriority(requirement.getPurchaseSuggestions(), priorityLevel);

            suggestions.add(requirement);

            log.info("采购建议清单生成完成 - 建议数量: {}", suggestions.size());
            return suggestions;

        } catch (Exception e) {
            log.error("生成采购建议清单失败 - BOM ID: {}, 错误: {}", bomId, e.getMessage(), e);
            throw new RuntimeException("生成采购建议清单失败：" + e.getMessage());
        }
    }

    // 私有辅助方法

    /**
     * 递归展开BOM
     */
    private void expandBomRecursive(Long bomId, BigDecimal quantity, Map<Long, BigDecimal> materialRequirements,
                                    Set<Long> processedBoms, int currentLevel, int maxLevel) {

        if (currentLevel >= maxLevel || processedBoms.contains(bomId)) {
            return; // 防止无限递归
        }

        processedBoms.add(bomId);

        // 基于现有字段模拟BOM明细查询
        List<MockBomItem> bomItems = getMockBomItems(bomId);

        for (MockBomItem item : bomItems) {
            BigDecimal itemRequirement = item.getUsage().multiply(quantity);

            if (item.getIsSubAssembly()) {
                // 如果是子件，继续递归展开
                expandBomRecursive(item.getMaterialId(), itemRequirement, materialRequirements,
                    processedBoms, currentLevel + 1, maxLevel);
            } else {
                // 如果是原材料，累加需求量
                BigDecimal actualRequirement = calculateActualRequirement(item.getMaterialId(), itemRequirement);
                materialRequirements.merge(item.getMaterialId(), actualRequirement, BigDecimal::add);
            }
        }

        processedBoms.remove(bomId);
    }

    /**
     * 获取模拟BOM明细
     */
    private List<MockBomItem> getMockBomItems(Long bomId) {
        List<MockBomItem> items = new ArrayList<>();

        // 模拟BOM明细数据
        Random random = new Random(bomId);
        int itemCount = random.nextInt(5) + 3; // 3-7个明细

        for (int i = 0; i < itemCount; i++) {
            MockBomItem item = new MockBomItem();
            item.setMaterialId(bomId * 1000 + i + 1);
            item.setUsage(new BigDecimal(random.nextDouble() * 10 + 1).setScale(2, RoundingMode.HALF_UP));
            item.setIsSubAssembly(random.nextBoolean() && i < 2); // 前两个可能是子件
            items.add(item);
        }

        return items;
    }

    /**
     * 创建缺料VO对象
     */
    private MaterialShortageVo createMaterialShortageVo(Long materialId, BigDecimal requirement,
                                                        BigDecimal currentStock, BigDecimal availableStock,
                                                        BigDecimal inTransit, BigDecimal shortage,
                                                        BigDecimal safetyStock) {
        MaterialShortageVo shortageVo = new MaterialShortageVo();
        shortageVo.setMaterialId(materialId);
        shortageVo.setMaterialCode("MAT-" + materialId);
        shortageVo.setMaterialName("原材料-" + materialId);
        shortageVo.setSpecification("规格-" + materialId);
        shortageVo.setUnitCode("PCS");
        shortageVo.setUnitName("个");
        shortageVo.setRequiredQuantity(requirement);
        shortageVo.setCurrentStock(currentStock);
        shortageVo.setAvailableStock(availableStock);
        shortageVo.setInTransitQuantity(inTransit);
        shortageVo.setShortageQuantity(shortage.compareTo(BigDecimal.ZERO) > 0 ? shortage : BigDecimal.ZERO);
        shortageVo.setSafetyStock(safetyStock);

        // 计算缺料率
        if (requirement.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal shortageRate = shortage.divide(requirement, 4, RoundingMode.HALF_UP);
            shortageVo.setShortageRate(shortageRate);
        } else {
            shortageVo.setShortageRate(BigDecimal.ZERO);
        }

        // 判断缺料类型和紧急程度
        if (shortage.compareTo(BigDecimal.ZERO) > 0) {
            shortageVo.setShortageType("ABSOLUTE");
            shortageVo.setUrgencyLevel("HIGH");
        } else if (availableStock.compareTo(safetyStock) <= 0) {
            shortageVo.setShortageType("SAFETY");
            shortageVo.setUrgencyLevel("MEDIUM");
        } else {
            shortageVo.setShortageType("WARNING");
            shortageVo.setUrgencyLevel("LOW");
        }

        // 设置其他信息
        shortageVo.setAbcCategory("A"); // 默认A类
        shortageVo.setSupplierName("主要供应商-" + materialId);
        shortageVo.setSupplierLeadTime(7); // 默认7天交期
        shortageVo.setLastPurchasePrice(getEstimatedMaterialPrice(materialId));
        shortageVo.setMinPurchaseQuantity(getMaterialMinPurchaseQuantity(materialId));
        shortageVo.setSuggestedPurchaseQuantity(shortage.add(safetyStock));
        shortageVo.setSuggestedDeliveryDate(LocalDate.now().plusDays(7));
        shortageVo.setAnalysisTime(LocalDateTime.now());
        shortageVo.setAnalysisByName("系统分析");

        return shortageVo;
    }

    /**
     * 生成预警清单
     */
    private List<MaterialWarningVo> generateWarningList(List<Long> materialIds) {
        List<MaterialWarningVo> warningList = new ArrayList<>();

        for (Long materialId : materialIds) {
            if (checkInventoryWarning(materialId)) {
                MaterialWarningVo warning = new MaterialWarningVo();
                warning.setMaterialId(materialId);
                warning.setMaterialCode("MAT-" + materialId);
                warning.setMaterialName("原材料-" + materialId);
                warning.setCurrentStock(queryMaterialInventoryBalance(materialId, null));
                warning.setSafetyStock(getMaterialSafetyStock(materialId));
                warning.setWarningThreshold(warning.getSafetyStock().multiply(new BigDecimal("1.2")));
                warning.setWarningType("LOW_STOCK");
                warning.setWarningLevel("MEDIUM");
                warning.setWarningTime(LocalDateTime.now());
                warning.setWarningReason("库存低于预警阈值");
                warning.setSuggestedAction("建议及时补充库存");
                warningList.add(warning);
            }
        }

        return warningList;
    }

    /**
     * 生成采购建议内部方法
     */
    private List<PurchaseRequirementVo.PurchaseSuggestionVo> generatePurchaseSuggestionsInternal(
        Map<Long, BigDecimal> materialRequirements, Boolean considerInTransit) {

        List<PurchaseRequirementVo.PurchaseSuggestionVo> suggestions = new ArrayList<>();

        for (Map.Entry<Long, BigDecimal> entry : materialRequirements.entrySet()) {
            Long materialId = entry.getKey();
            BigDecimal requirement = entry.getValue();

            BigDecimal currentStock = queryMaterialInventoryBalance(materialId, null);
            BigDecimal inTransit = considerInTransit ? queryInTransitQuantity(materialId) : BigDecimal.ZERO;
            BigDecimal availableStock = currentStock.multiply(new BigDecimal("0.9"));
            BigDecimal totalAvailable = availableStock.add(inTransit);
            BigDecimal shortage = requirement.subtract(totalAvailable);

            if (shortage.compareTo(BigDecimal.ZERO) > 0) {
                PurchaseRequirementVo.PurchaseSuggestionVo suggestion = createPurchaseSuggestion(
                    materialId, requirement, currentStock, inTransit, shortage);
                suggestions.add(suggestion);
            }
        }

        // 按优先级排序
        suggestions.sort(Comparator.comparing(PurchaseRequirementVo.PurchaseSuggestionVo::getPriority));

        return suggestions;
    }

    /**
     * 创建采购建议
     */
    private PurchaseRequirementVo.PurchaseSuggestionVo createPurchaseSuggestion(
        Long materialId, BigDecimal requirement, BigDecimal currentStock,
        BigDecimal inTransit, BigDecimal shortage) {

        PurchaseRequirementVo.PurchaseSuggestionVo suggestion = new PurchaseRequirementVo.PurchaseSuggestionVo();
        suggestion.setMaterialId(materialId);
        suggestion.setMaterialCode("MAT-" + materialId);
        suggestion.setMaterialName("原材料-" + materialId);
        suggestion.setSpecification("规格-" + materialId);
        suggestion.setUnitCode("PCS");
        suggestion.setUnitName("个");
        suggestion.setRequiredQuantity(requirement);
        suggestion.setCurrentStock(currentStock);
        suggestion.setInTransitQuantity(inTransit);

        // 计算建议采购量
        BigDecimal safetyStock = getMaterialSafetyStock(materialId);
        BigDecimal minPurchaseQty = getMaterialMinPurchaseQuantity(materialId);
        BigDecimal suggestedQty = shortage.add(safetyStock);

        // 确保不低于最小采购量
        if (suggestedQty.compareTo(minPurchaseQty) < 0) {
            suggestedQty = minPurchaseQty;
        }

        suggestion.setSuggestedPurchaseQuantity(suggestedQty);
        suggestion.setMinPurchaseQuantity(minPurchaseQty);
        suggestion.setPackageSize(new BigDecimal("10")); // 默认包装规格

        // 考虑包装规格调整实际采购量
        BigDecimal actualQty = adjustQuantityByPackageSize(suggestedQty, suggestion.getPackageSize());
        suggestion.setActualSuggestedQuantity(actualQty);

        // 设置价格和金额
        BigDecimal estimatedPrice = getEstimatedMaterialPrice(materialId);
        suggestion.setEstimatedPrice(estimatedPrice);
        suggestion.setEstimatedAmount(actualQty.multiply(estimatedPrice));

        // 设置优先级（基于缺料率）
        BigDecimal shortageRate = shortage.divide(requirement, 4, RoundingMode.HALF_UP);
        if (shortageRate.compareTo(new BigDecimal("0.8")) >= 0) {
            suggestion.setPriority(1); // 最高优先级
            suggestion.setIsUrgent(true);
        } else if (shortageRate.compareTo(new BigDecimal("0.5")) >= 0) {
            suggestion.setPriority(2);
            suggestion.setIsUrgent(false);
        } else {
            suggestion.setPriority(3);
            suggestion.setIsUrgent(false);
        }

        // 设置供应商信息
        suggestion.setSuggestedSupplierCode("SUP-" + materialId);
        suggestion.setSuggestedSupplierName("供应商-" + materialId);
        suggestion.setSupplierLeadTime(7);
        suggestion.setSuggestedDeliveryDate(LocalDate.now().plusDays(7));

        // 设置其他信息
        suggestion.setAbcCategory("A");
        suggestion.setInventoryTurnover(new BigDecimal("12")); // 默认年周转12次
        suggestion.setSafetyStock(safetyStock);
        suggestion.setSuggestionReason("库存不足，需要补充");

        return suggestion;
    }

    /**
     * 根据优先级调整建议
     */
    private void adjustSuggestionsByPriority(List<PurchaseRequirementVo.PurchaseSuggestionVo> suggestions, String priorityLevel) {
        if ("URGENT".equals(priorityLevel)) {
            // 紧急情况下，所有建议都设为最高优先级
            suggestions.forEach(s -> {
                s.setPriority(1);
                s.setIsUrgent(true);
                s.setSuggestedDeliveryDate(LocalDate.now().plusDays(3)); // 3天交期
            });
        } else if ("HIGH".equals(priorityLevel)) {
            // 高优先级情况下，缩短交期
            suggestions.forEach(s -> {
                if (s.getPriority() <= 2) {
                    s.setSuggestedDeliveryDate(LocalDate.now().plusDays(5)); // 5天交期
                }
            });
        }
    }

    /**
     * 根据包装规格调整数量
     */
    private BigDecimal adjustQuantityByPackageSize(BigDecimal quantity, BigDecimal packageSize) {
        if (packageSize.compareTo(BigDecimal.ZERO) <= 0) {
            return quantity;
        }

        BigDecimal packages = quantity.divide(packageSize, 0, RoundingMode.UP);
        return packages.multiply(packageSize);
    }

    /**
     * 计算平均消耗量
     */
    private BigDecimal calculateAverageConsumption(Long materialId) {
        // 基于历史出库记录计算平均消耗量
        // 这里使用模拟数据
        Random random = new Random(materialId);
        return new BigDecimal(random.nextInt(500) + 100);
    }

    /**
     * 获取材料损耗率
     */
    private BigDecimal getMaterialLossRate(Long materialId) {
        // 基于材料类型返回不同的损耗率
        // 这里使用默认损耗率
        return DEFAULT_LOSS_RATE;
    }

    /**
     * 获取紧急程度优先级数值
     */
    private int getUrgencyPriority(String urgencyLevel) {
        switch (urgencyLevel) {
            case "CRITICAL":
                return 1;
            case "HIGH":
                return 2;
            case "MEDIUM":
                return 3;
            case "LOW":
                return 4;
            default:
                return 5;
        }
    }

    /**
     * 模拟BOM明细项
     */
    private static class MockBomItem {
        private Long materialId;
        private BigDecimal usage;
        private Boolean isSubAssembly;

        public Long getMaterialId() {
            return materialId;
        }

        public void setMaterialId(Long materialId) {
            this.materialId = materialId;
        }

        public BigDecimal getUsage() {
            return usage;
        }

        public void setUsage(BigDecimal usage) {
            this.usage = usage;
        }

        public Boolean getIsSubAssembly() {
            return isSubAssembly;
        }

        public void setIsSubAssembly(Boolean isSubAssembly) {
            this.isSubAssembly = isSubAssembly;
        }
    }
}
