package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.SaleReturnItem;
import com.iotlaser.spms.erp.domain.SaleReturnItemBatch;
import com.iotlaser.spms.erp.domain.bo.SaleReturnItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnItemVo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import com.iotlaser.spms.erp.enums.SaleReturnStatus;
import com.iotlaser.spms.erp.mapper.SaleReturnItemMapper;
import com.iotlaser.spms.erp.service.ISaleReturnItemBatchService;
import com.iotlaser.spms.erp.service.ISaleReturnItemService;
import com.iotlaser.spms.erp.service.ISaleReturnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 销售退货明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleReturnItemServiceImpl implements ISaleReturnItemService {

    private final SaleReturnItemMapper baseMapper;
    @Lazy
    @Autowired
    private ISaleReturnService saleReturnService;
    private final ISaleReturnItemBatchService saleReturnItemBatchService;

    /**
     * 查询销售退货明细
     *
     * @param itemId 主键
     * @return 销售退货明细
     */
    @Override
    public SaleReturnItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询销售退货明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售退货明细分页列表
     */
    @Override
    public TableDataInfo<SaleReturnItemVo> queryPageList(SaleReturnItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleReturnItem> lqw = buildQueryWrapper(bo);
        Page<SaleReturnItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售退货明细列表
     *
     * @param bo 查询条件
     * @return 销售退货明细列表
     */
    @Override
    public List<SaleReturnItemVo> queryList(SaleReturnItemBo bo) {
        LambdaQueryWrapper<SaleReturnItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleReturnItem> buildQueryWrapper(SaleReturnItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleReturnItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleReturnItem::getItemId);
        lqw.eq(bo.getReturnId() != null, SaleReturnItem::getReturnId, bo.getReturnId());
        lqw.eq(bo.getProductId() != null, SaleReturnItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), SaleReturnItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), SaleReturnItem::getProductName, bo.getProductName());
        lqw.eq(bo.getQuantity() != null, SaleReturnItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getPrice() != null, SaleReturnItem::getPrice, bo.getPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleReturnItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增销售退货明细
     *
     * @param bo 销售退货明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SaleReturnItemBo bo) {
        SaleReturnItem add = MapstructUtils.convert(bo, SaleReturnItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改销售退货明细
     *
     * @param bo 销售退货明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SaleReturnItemBo bo) {
        SaleReturnItem update = MapstructUtils.convert(bo, SaleReturnItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleReturnItem entity) {
        // 校验必填字段
        if (entity.getReturnId() == null) {
            throw new ServiceException("退货单不能为空");
        }
        if (entity.getProductId() == null) {
            throw new ServiceException("产品不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("退货数量必须大于0");
        }
        if (entity.getPrice() == null || entity.getPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("退货价格不能为负数");
        }

        // 校验同一退货单中产品不能重复
        if (entity.getReturnId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<SaleReturnItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleReturnItem::getReturnId, entity.getReturnId());
            wrapper.eq(SaleReturnItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(SaleReturnItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一销售退货单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除销售退货明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验销售退货明细是否可以删除
            List<SaleReturnItem> items = baseMapper.selectByIds(ids);
            for (SaleReturnItem item : items) {
                // 1. 检查主表状态，只有草稿状态的退货明细才能删除
                SaleReturnVo saleReturn = saleReturnService.queryById(item.getReturnId());
                if (saleReturn != null && SaleReturnStatus.DRAFT != saleReturn.getReturnStatus()) {
                    throw new ServiceException("退货明细所属销售退货单【" + saleReturn.getReturnName() +
                        "】状态为【" + saleReturn.getReturnStatus() + "】，不允许删除明细");
                }

                // 2. 级联删除销售退货批次
                List<SaleReturnItemBatch> batches = queryBatchesByItemId(item.getItemId());
                if (!batches.isEmpty()) {
                    List<Long> batchIds = batches.stream()
                        .map(SaleReturnItemBatch::getBatchId)
                        .collect(Collectors.toList());
                    saleReturnItemBatchService.deleteWithValidByIds(batchIds, false);
                    log.info("级联删除销售退货批次，明细：{}，批次数量：{}", item.getProductName(), batchIds.size());
                }

                log.info("删除销售退货明细校验通过：产品【{}】", item.getProductName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除销售退货明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除销售退货明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除销售退货明细失败：" + e.getMessage());
        }
    }

    /**
     * 根据退货单ID查询明细ID列表
     *
     * @param returnId 退货单ID
     * @return 明细ID列表
     */
    @Override
    public List<Long> selectItemIdsByReturnId(Long returnId) {
        LambdaQueryWrapper<SaleReturnItem> wrapper = Wrappers.lambdaQuery();
        wrapper.select(SaleReturnItem::getItemId);
        wrapper.eq(SaleReturnItem::getReturnId, returnId);
        return baseMapper.selectList(wrapper).stream()
            .map(SaleReturnItem::getItemId)
            .collect(Collectors.toList());
    }

    /**
     * 批量插入或更新销售退货明细
     *
     * @param items 明细BO集合
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<SaleReturnItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }

        try {
            List<SaleReturnItem> entities = items.stream()
                .map(bo -> MapstructUtils.convert(bo, SaleReturnItem.class))
                .collect(Collectors.toList());

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新销售退货明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新销售退货明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID集合删除销售退货明细
     *
     * @param ids ID集合
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除销售退货明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除销售退货明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除失败：" + e.getMessage());
        }
    }

    /**
     * 查询销售退货明细表及其关联信息
     *
     * @param itemId 主键
     * @return 销售退货明细表
     */
    @Override
    public SaleReturnItemVo queryByIdWith(Long itemId) {
        return baseMapper.queryByIdWith(itemId);
    }

    /**
     * 分页查询销售退货明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售退货明细表分页列表
     */
    @Override
    public TableDataInfo<SaleReturnItemVo> queryPageListWith(SaleReturnItemBo bo, PageQuery pageQuery) {
        QueryWrapper<SaleReturnItem> queryWrapper = buildQueryWrapperWith(bo);
        List<SaleReturnItemVo> result = baseMapper.queryPageListWith(pageQuery.build(), queryWrapper);
        return TableDataInfo.build(result);
    }

    /**
     * 构建查询条件包装器（用于关联查询）
     *
     * @param bo 查询条件
     * @return 查询条件包装器
     */
    private QueryWrapper<SaleReturnItem> buildQueryWrapperWith(SaleReturnItemBo bo) {
        QueryWrapper<SaleReturnItem> wrapper = Wrappers.query();
        wrapper.eq(bo.getReturnId() != null, "item.return_id", bo.getReturnId())
            .eq(bo.getProductId() != null, "item.product_id", bo.getProductId())
            .like(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode())
            .like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName())
            .eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }

    /**
     * 根据明细ID查询销售退货批次列表
     *
     * @param itemId 明细ID
     * @return 批次列表
     */
    private List<SaleReturnItemBatch> queryBatchesByItemId(Long itemId) {
        try {
            // 通过SaleReturnItemBatchService查询批次
            LambdaQueryWrapper<SaleReturnItemBatch> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleReturnItemBatch::getItemId, itemId);
            // 注意：这里需要通过baseMapper直接查询，因为SaleReturnItemBatchService可能没有提供这个方法
            // 或者可以通过SaleReturnItemBatchService的queryList方法实现
            return saleReturnItemBatchService.queryList(new com.iotlaser.spms.erp.domain.bo.SaleReturnItemBatchBo() {{
                    setItemId(itemId);
                }}).stream()
                .map(vo -> {
                    SaleReturnItemBatch batch = new SaleReturnItemBatch();
                    batch.setBatchId(vo.getBatchId());
                    batch.setItemId(vo.getItemId());
                    batch.setInternalBatchNumber(vo.getInternalBatchNumber());
                    return batch;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("查询销售退货批次失败，明细ID：{}，错误：{}", itemId, e.getMessage());
            return List.of(); // 返回空列表，不影响主流程
        }
    }
}
