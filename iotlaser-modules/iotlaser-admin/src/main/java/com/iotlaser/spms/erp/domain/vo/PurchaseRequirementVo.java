package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购需求分析结果VO
 *
 * <AUTHOR> Assistant
 * @date 2025-06-24
 */
@Data
@ExcelIgnoreUnannotated
public class PurchaseRequirementVo {

    /**
     * 需求分析ID
     */
    private Long requirementId;

    /**
     * BOM清单ID
     */
    private Long bomId;

    /**
     * BOM清单编码
     */
    @ExcelProperty(value = "BOM清单编码")
    private String bomCode;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 生产需求数量
     */
    @ExcelProperty(value = "生产需求数量")
    private BigDecimal productionQuantity;

    /**
     * 需求紧急程度：URGENT(紧急)、HIGH(高)、MEDIUM(中)、LOW(低)
     */
    @ExcelProperty(value = "紧急程度")
    private String urgencyLevel;

    /**
     * 需求日期
     */
    @ExcelProperty(value = "需求日期")
    private LocalDate requirementDate;

    /**
     * 分析时间
     */
    @ExcelProperty(value = "分析时间")
    private LocalDateTime analysisTime;

    /**
     * 采购建议清单
     */
    private List<PurchaseSuggestionVo> purchaseSuggestions;

    /**
     * 总采购项目数
     */
    @ExcelProperty(value = "总采购项目数")
    private Integer totalPurchaseItems;

    /**
     * 总采购金额
     */
    @ExcelProperty(value = "总采购金额")
    private BigDecimal totalPurchaseAmount;

    /**
     * 预计交期
     */
    @ExcelProperty(value = "预计交期")
    private LocalDate estimatedDeliveryDate;

    /**
     * 分析状态：DRAFT(草稿)、CONFIRMED(已确认)、APPROVED(已审批)
     */
    @ExcelProperty(value = "分析状态")
    private String analysisStatus;

    /**
     * 分析人员ID
     */
    private Long analysisById;

    /**
     * 分析人员姓名
     */
    @ExcelProperty(value = "分析人员")
    private String analysisByName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 采购建议明细VO
     */
    @Data
    public static class PurchaseSuggestionVo {
        /**
         * 建议ID
         */
        private Long suggestionId;

        /**
         * 原材料ID
         */
        private Long materialId;

        /**
         * 原材料编码
         */
        private String materialCode;

        /**
         * 原材料名称
         */
        private String materialName;

        /**
         * 规格型号
         */
        private String specification;

        /**
         * 单位ID
         */
        private Long unitId;

        /**
         * 单位编码
         */
        private String unitCode;

        /**
         * 单位名称
         */
        private String unitName;

        /**
         * 需求数量
         */
        private BigDecimal requiredQuantity;

        /**
         * 当前库存
         */
        private BigDecimal currentStock;

        /**
         * 在途数量
         */
        private BigDecimal inTransitQuantity;

        /**
         * 建议采购数量
         */
        private BigDecimal suggestedPurchaseQuantity;

        /**
         * 最小采购量
         */
        private BigDecimal minPurchaseQuantity;

        /**
         * 包装规格
         */
        private BigDecimal packageSize;

        /**
         * 实际建议采购量（考虑包装规格）
         */
        private BigDecimal actualSuggestedQuantity;

        /**
         * 预估单价
         */
        private BigDecimal estimatedPrice;

        /**
         * 预估金额
         */
        private BigDecimal estimatedAmount;

        /**
         * 优先级：1-5，1为最高优先级
         */
        private Integer priority;

        /**
         * 建议供应商ID
         */
        private Long suggestedSupplierId;

        /**
         * 建议供应商编码
         */
        private String suggestedSupplierCode;

        /**
         * 建议供应商名称
         */
        private String suggestedSupplierName;

        /**
         * 供应商交期（天）
         */
        private Integer supplierLeadTime;

        /**
         * 建议交期
         */
        private LocalDate suggestedDeliveryDate;

        /**
         * ABC分类
         */
        private String abcCategory;

        /**
         * 库存周转率
         */
        private BigDecimal inventoryTurnover;

        /**
         * 安全库存量
         */
        private BigDecimal safetyStock;

        /**
         * 建议原因
         */
        private String suggestionReason;

        /**
         * 是否紧急采购
         */
        private Boolean isUrgent;

        /**
         * 备注
         */
        private String remark;
    }
}
