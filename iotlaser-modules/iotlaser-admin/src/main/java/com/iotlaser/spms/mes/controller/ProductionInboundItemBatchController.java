package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.ProductionInboundItemBatchBo;
import com.iotlaser.spms.mes.domain.vo.ProductionInboundItemBatchVo;
import com.iotlaser.spms.mes.service.IProductionInboundItemBatchService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产入库批次明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/productionInboundItemBatch")
public class ProductionInboundItemBatchController extends BaseController {

    private final IProductionInboundItemBatchService productionInboundItemBatchService;

    /**
     * 查询生产入库批次明细列表
     */
    @SaCheckPermission("mes:productionInboundItemBatch:list")
    @GetMapping("/list")
    public TableDataInfo<ProductionInboundItemBatchVo> list(ProductionInboundItemBatchBo bo, PageQuery pageQuery) {
        return productionInboundItemBatchService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产入库批次明细列表
     */
    @SaCheckPermission("mes:productionInboundItemBatch:export")
    @Log(title = "生产入库批次明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductionInboundItemBatchBo bo, HttpServletResponse response) {
        List<ProductionInboundItemBatchVo> list = productionInboundItemBatchService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产入库批次明细", ProductionInboundItemBatchVo.class, response);
    }

    /**
     * 获取生产入库批次明细详细信息
     *
     * @param batchId 主键
     */
    @SaCheckPermission("mes:productionInboundItemBatch:query")
    @GetMapping("/{batchId}")
    public R<ProductionInboundItemBatchVo> getInfo(@NotNull(message = "主键不能为空")
                                                   @PathVariable Long batchId) {
        return R.ok(productionInboundItemBatchService.queryById(batchId));
    }

    /**
     * 新增生产入库批次明细
     */
    @SaCheckPermission("mes:productionInboundItemBatch:add")
    @Log(title = "生产入库批次明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductionInboundItemBatchBo bo) {
        return toAjax(productionInboundItemBatchService.insertByBo(bo));
    }

    /**
     * 修改生产入库批次明细
     */
    @SaCheckPermission("mes:productionInboundItemBatch:edit")
    @Log(title = "生产入库批次明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductionInboundItemBatchBo bo) {
        return toAjax(productionInboundItemBatchService.updateByBo(bo));
    }

    /**
     * 删除生产入库批次明细
     *
     * @param batchIds 主键串
     */
    @SaCheckPermission("mes:productionInboundItemBatch:remove")
    @Log(title = "生产入库批次明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{batchIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] batchIds) {
        return toAjax(productionInboundItemBatchService.deleteWithValidByIds(List.of(batchIds), true));
    }
}
