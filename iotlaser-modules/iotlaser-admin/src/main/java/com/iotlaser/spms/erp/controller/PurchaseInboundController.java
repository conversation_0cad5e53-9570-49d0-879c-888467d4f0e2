package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 采购入库
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/purchaseInbound")
public class PurchaseInboundController extends BaseController {

    private final IPurchaseInboundService purchaseInboundService;

    /**
     * 查询采购入库列表
     */
    @SaCheckPermission("erp:purchaseInbound:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseInboundVo> list(PurchaseInboundBo bo, PageQuery pageQuery) {
        return purchaseInboundService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出采购入库列表
     */
    @SaCheckPermission("erp:purchaseInbound:export")
    @Log(title = "采购入库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseInboundBo bo, HttpServletResponse response) {
        List<PurchaseInboundVo> list = purchaseInboundService.queryList(bo);
        ExcelUtil.exportExcel(list, "采购入库", PurchaseInboundVo.class, response);
    }

    /**
     * 获取采购入库详细信息
     *
     * @param inboundId 主键
     */
    @SaCheckPermission("erp:purchaseInbound:query")
    @GetMapping("/{inboundId}")
    public R<PurchaseInboundVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long inboundId) {
        return R.ok(purchaseInboundService.queryById(inboundId));
    }

    /**
     * 新增采购入库
     */
    @SaCheckPermission("erp:purchaseInbound:add")
    @Log(title = "采购入库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PurchaseInboundBo bo) {
        return toAjax(purchaseInboundService.insertByBo(bo));
    }

    /**
     * 修改采购入库
     */
    @SaCheckPermission("erp:purchaseInbound:edit")
    @Log(title = "采购入库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PurchaseInboundBo bo) {
        return toAjax(purchaseInboundService.updateByBo(bo));
    }

    /**
     * 删除采购入库
     *
     * @param inboundIds 主键串
     */
    @SaCheckPermission("erp:purchaseInbound:remove")
    @Log(title = "采购入库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{inboundIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] inboundIds) {
        return toAjax(purchaseInboundService.deleteWithValidByIds(List.of(inboundIds), true));
    }

    /**
     * 确认采购入库单
     *
     * @param inboundId 入库单ID
     */
    @SaCheckPermission("erp:purchaseInbound:edit")
    @Log(title = "确认采购入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{inboundId}")
    public R<Void> confirm(@NotNull(message = "入库单ID不能为空") @PathVariable Long inboundId) {
        return toAjax(purchaseInboundService.confirmInbound(inboundId));
    }

    /**
     * 批量确认采购入库单
     *
     * @param inboundIds 入库单ID数组
     */
    @SaCheckPermission("erp:purchaseInbound:edit")
    @Log(title = "批量确认采购入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "入库单ID不能为空") @RequestBody Long[] inboundIds) {
        return toAjax(purchaseInboundService.batchConfirmInbounds(Arrays.asList(inboundIds)));
    }

    /**
     * 完成采购入库
     *
     * @param inboundId 入库单ID
     */
    @SaCheckPermission("erp:purchaseInbound:edit")
    @Log(title = "完成采购入库", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{inboundId}")
    public R<Void> complete(@NotNull(message = "入库单ID不能为空") @PathVariable Long inboundId) {
        return toAjax(purchaseInboundService.completeInbound(inboundId));
    }

    /**
     * 从采购订单创建入库单
     *
     * @param purchaseOrderId 采购订单ID
     */
    @SaCheckPermission("erp:purchaseInbound:add")
    @Log(title = "从采购订单创建入库单", businessType = BusinessType.INSERT)
    @PostMapping("/createFromPurchaseOrder/{purchaseOrderId}")
    public R<PurchaseInboundVo> createFromPurchaseOrder(@NotNull(message = "采购订单ID不能为空") @PathVariable Long purchaseOrderId) {
        return R.ok(purchaseInboundService.createFromPurchaseOrder(purchaseOrderId));
    }
}
