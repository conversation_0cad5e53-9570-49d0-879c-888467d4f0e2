package com.iotlaser.spms.mes.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.mes.enums.ProductionReturnStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 生产退料对象 mes_production_return
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mes_production_return")
public class ProductionReturn extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退料单ID
     */
    @TableId(value = "return_id")
    private Long returnId;

    /**
     * 退料单编号
     */
    private String returnCode;

    /**
     * 退料单名称
     */
    private String returnName;

    /**
     * 生产订单ID
     */
    private Long orderId;

    /**
     * 生产订单编码
     */
    private String orderCode;

    /**
     * 生产订单名称
     */
    private String orderName;

    /**
     * 检验单ID
     */
    private Long inspectionId;

    /**
     * 检验单编号
     */
    private String inspectionCode;

    /**
     * 检验单名称
     */
    private String inspectionName;

    /**
     * 退料时间
     */
    private Date returnTime;

    /**
     * 退料状态
     */
    private ProductionReturnStatus returnStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 明细
     */
    @TableField(exist = false)
    private List<ProductionReturnItem> items;
}
