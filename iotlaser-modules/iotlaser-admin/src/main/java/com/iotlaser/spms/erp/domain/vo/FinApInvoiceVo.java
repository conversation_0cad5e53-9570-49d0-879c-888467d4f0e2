package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinApInvoice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 供应商发票视图对象 erp_fin_ap_invoice
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinApInvoice.class)
public class FinApInvoiceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 应付ID
     */
    @ExcelProperty(value = "应付ID")
    private Long invoiceId;

    /**
     * 应付编号
     */
    @ExcelProperty(value = "应付编号")
    private String invoiceCode;

    /**
     * 应付名称
     */
    @ExcelProperty(value = "应付名称")
    private String invoiceName;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long supplierId;

    /**
     * 供应商编码
     */
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 发票号码
     */
    @ExcelProperty(value = "发票号码")
    private String invoiceNumber;

    /**
     * 开票日期
     */
    @ExcelProperty(value = "开票日期")
    private LocalDate invoiceDate;

    /**
     * 金额（含税）
     */
    @ExcelProperty(value = "金额（含税）")
    private BigDecimal amount;

    /**
     * 金额（不含税）
     */
    @ExcelProperty(value = "金额（不含税）")
    private BigDecimal amountExclusiveTax;

    /**
     * 总税额
     */
    @ExcelProperty(value = "总税额")
    private BigDecimal taxAmount;

    /**
     * 应付状态
     */
    @ExcelProperty(value = "应付状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_fin_ap_invoice_status")
    private String invoiceStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
