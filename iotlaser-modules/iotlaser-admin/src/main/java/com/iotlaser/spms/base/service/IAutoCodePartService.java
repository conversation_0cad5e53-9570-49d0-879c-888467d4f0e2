package com.iotlaser.spms.base.service;

import com.iotlaser.spms.base.domain.bo.AutoCodePartBo;
import com.iotlaser.spms.base.domain.vo.AutoCodePartVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 编码生成规则组成Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/03/11
 */
public interface IAutoCodePartService {

    /**
     * 查询编码生成规则组成
     *
     * @param partId 主键
     * @return 编码生成规则组成
     */
    AutoCodePartVo queryById(Long partId);

    /**
     * 分页查询编码生成规则组成列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 编码生成规则组成分页列表
     */
    TableDataInfo<AutoCodePartVo> queryPageList(AutoCodePartBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的编码生成规则组成列表
     *
     * @param bo 查询条件
     * @return 编码生成规则组成列表
     */
    List<AutoCodePartVo> queryList(AutoCodePartBo bo);

    /**
     * 新增编码生成规则组成
     *
     * @param bo 编码生成规则组成
     * @return 是否新增成功
     */
    Boolean insertByBo(AutoCodePartBo bo);

    /**
     * 修改编码生成规则组成
     *
     * @param bo 编码生成规则组成
     * @return 是否修改成功
     */
    Boolean updateByBo(AutoCodePartBo bo);

    /**
     * 校验并批量删除编码生成规则组成信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
