package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 管理费用对象 erp_fin_expense_invoice
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_expense_invoice")
public class FinExpenseInvoice extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 应付ID
     */
    @TableId(value = "invoice_id")
    private Long invoiceId;

    /**
     * 应付编号
     */
    private String invoiceCode;

    /**
     * 应付名称
     */
    private String invoiceName;

    /**
     * 收款方类型
     */
    private String payeeType;

    /**
     * 收款方ID
     */
    private Long payeeId;

    /**
     * 收款方编码
     */
    private String payeeCode;

    /**
     * 收款方名称
     */
    private String payeeName;

    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 开票日期
     */
    private LocalDate invoiceDate;

    /**
     * 金额（不含税）
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 金额（含税）
     */
    private BigDecimal amount;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 应付状态
     */
    private String invoiceStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
