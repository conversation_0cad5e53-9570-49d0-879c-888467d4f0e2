package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinStatementItemBo;
import com.iotlaser.spms.erp.domain.vo.FinStatementItemVo;
import com.iotlaser.spms.erp.service.IFinStatementItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 对账单明细
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finStatementItem")
public class FinStatementItemController extends BaseController {

    private final IFinStatementItemService finStatementItemService;

    /**
     * 查询对账单明细列表
     */
    @SaCheckPermission("erp:finStatementItem:list")
    @GetMapping("/list")
    public TableDataInfo<FinStatementItemVo> list(FinStatementItemBo bo, PageQuery pageQuery) {
        return finStatementItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出对账单明细列表
     */
    @SaCheckPermission("erp:finStatementItem:export")
    @Log(title = "对账单明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinStatementItemBo bo, HttpServletResponse response) {
        List<FinStatementItemVo> list = finStatementItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "对账单明细", FinStatementItemVo.class, response);
    }

    /**
     * 获取对账单明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("erp:finStatementItem:query")
    @GetMapping("/{itemId}")
    public R<FinStatementItemVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long itemId) {
        return R.ok(finStatementItemService.queryById(itemId));
    }

    /**
     * 新增对账单明细
     */
    @SaCheckPermission("erp:finStatementItem:add")
    @Log(title = "对账单明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinStatementItemBo bo) {
        return toAjax(finStatementItemService.insertByBo(bo));
    }

    /**
     * 修改对账单明细
     */
    @SaCheckPermission("erp:finStatementItem:edit")
    @Log(title = "对账单明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinStatementItemBo bo) {
        return toAjax(finStatementItemService.updateByBo(bo));
    }

    /**
     * 删除对账单明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("erp:finStatementItem:remove")
    @Log(title = "对账单明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(finStatementItemService.deleteWithValidByIds(List.of(itemIds), true));
    }
}
