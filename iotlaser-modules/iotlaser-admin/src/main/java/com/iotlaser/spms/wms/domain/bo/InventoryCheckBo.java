package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.InventoryCheck;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 库存盘点业务对象 wms_inventory_check
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InventoryCheck.class, reverseConvertGenerate = false)
public class InventoryCheckBo extends BaseEntity {

    /**
     * 盘点单ID
     */
    @NotNull(message = "盘点单ID不能为空", groups = {EditGroup.class})
    private Long checkId;

    /**
     * 盘点单编号
     */
    @NotBlank(message = "盘点单编号不能为空", groups = {EditGroup.class})
    private String checkCode;

    /**
     * 盘点单名称
     */
    private String checkName;

    /**
     * 盘点类型
     */
    private String checkType;

    /**
     * 盘点范围
     */
    private String checkScope;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 计划开始时间
     */
    private Date plannedStartTime;

    /**
     * 计划结束时间
     */
    private Date plannedEndTime;

    /**
     * 实际开始时间
     */
    private Date actualStartTime;

    /**
     * 实际结束时间
     */
    private Date actualEndTime;

    /**
     * 盘点状态
     */
    private String checkStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 盘点明细
     */
    private List<InventoryCheckItemBo> items;
}
