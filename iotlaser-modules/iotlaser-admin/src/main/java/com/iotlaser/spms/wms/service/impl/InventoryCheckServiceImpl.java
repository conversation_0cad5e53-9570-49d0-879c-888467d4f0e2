package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.wms.domain.InventoryCheck;
import com.iotlaser.spms.wms.domain.bo.*;
import com.iotlaser.spms.wms.domain.vo.InventoryBatchVo;
import com.iotlaser.spms.wms.domain.vo.InventoryCheckItemVo;
import com.iotlaser.spms.wms.domain.vo.InventoryCheckVo;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import com.iotlaser.spms.wms.enums.InventoryCheckStatus;
import com.iotlaser.spms.wms.enums.InventoryDirection;
import com.iotlaser.spms.wms.enums.SourceType;
import com.iotlaser.spms.wms.mapper.InventoryCheckMapper;
import com.iotlaser.spms.wms.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.iotlaser.spms.base.enums.GenCodeType.WMS_INVENTORY_CHECK_CODE;

/**
 * 库存盘点Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InventoryCheckServiceImpl implements IInventoryCheckService {

    private final InventoryCheckMapper baseMapper;
    private final IInventoryBatchService inventoryBatchService;
    private final IInventoryService inventoryService;
    private final IInventoryLogService inventoryLogService;
    private final IInventoryCheckItemService itemService;
    private final Gen gen;

    /**
     * 查询库存盘点
     *
     * @param checkId 主键
     * @return 库存盘点
     */
    @Override
    public InventoryCheckVo queryById(Long checkId) {
        return baseMapper.selectVoById(checkId);
    }

    /**
     * 分页查询库存盘点列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 库存盘点分页列表
     */
    @Override
    public TableDataInfo<InventoryCheckVo> queryPageList(InventoryCheckBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InventoryCheck> lqw = buildQueryWrapper(bo);
        Page<InventoryCheckVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的库存盘点列表
     *
     * @param bo 查询条件
     * @return 库存盘点列表
     */
    @Override
    public List<InventoryCheckVo> queryList(InventoryCheckBo bo) {
        LambdaQueryWrapper<InventoryCheck> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InventoryCheck> buildQueryWrapper(InventoryCheckBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InventoryCheck> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(InventoryCheck::getCheckId);
        lqw.eq(StringUtils.isNotBlank(bo.getCheckCode()), InventoryCheck::getCheckCode, bo.getCheckCode());
        lqw.like(StringUtils.isNotBlank(bo.getCheckName()), InventoryCheck::getCheckName, bo.getCheckName());
        lqw.eq(bo.getLocationId() != null, InventoryCheck::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), InventoryCheck::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), InventoryCheck::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getCheckStatus()), InventoryCheck::getCheckStatus, bo.getCheckStatus());

        lqw.gt(params.get("beginPlannedTime") != null && params.get("endPlannedTime") != null,
            InventoryCheck::getPlannedStartTime, params.get("beginPlannedTime"));
        lqw.lt(params.get("beginPlannedTime") != null && params.get("endPlannedTime") != null,
            InventoryCheck::getPlannedEndTime, params.get("endPlannedTime"));
        lqw.gt(params.get("beginActualTime") != null && params.get("endActualTime") != null,
            InventoryCheck::getActualStartTime, params.get("beginActualTime"));
        lqw.lt(params.get("beginActualTime") != null && params.get("endActualTime") != null,
            InventoryCheck::getActualEndTime, params.get("endActualTime"));

        return lqw;
    }

    /**
     * 新增库存盘点
     *
     * @param bo 库存盘点
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(InventoryCheckBo bo) {
        try {
            if (StringUtils.isEmpty(bo.getCheckCode())) {
                bo.setCheckCode(gen.code(WMS_INVENTORY_CHECK_CODE));
            }
            bo.setCheckStatus(InventoryCheckStatus.DRAFT.getValue());
            InventoryCheck add = MapstructUtils.convert(bo, InventoryCheck.class);
            validEntityBeforeSave(add);
            int result = baseMapper.insert(add);
            if (result > 0) {
                bo.setCheckId(add.getCheckId());
                if (bo.getItems() != null && !bo.getItems().isEmpty()) {
                    // 设置盘点明细的盘点单ID
                    bo.getItems().forEach(item -> {
                        item.setCheckId(add.getCheckId());
                    });
                    itemService.insertOrUpdateBatch(bo.getItems());
                }
                return true;
            }
            return false;
        } catch (Exception e) {
            throw new ServiceException("新增库存盘点失败：" + e.getMessage());
        }
    }

    /**
     * 修改库存盘点
     *
     * @param bo 库存盘点
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(InventoryCheckBo bo) {
        try {
            InventoryCheck update = MapstructUtils.convert(bo, InventoryCheck.class);
            validEntityBeforeSave(update);
            int result = baseMapper.updateById(update);
            if (result > 0) {
                if (bo.getItems() != null && !bo.getItems().isEmpty()) {
                    List<Long> itemIds = itemService.selectItemIdsByCheckId(bo.getCheckId());

                    bo.getItems().forEach(item -> {
                        item.setCheckId(bo.getCheckId());
                        itemIds.remove(item.getItemId());
                    });

                    itemService.insertOrUpdateBatch(bo.getItems());
                    itemService.deleteByIds(itemIds);
                }
                return true;
            }
            return false;
        } catch (Exception e) {
            throw new ServiceException("修改库存盘点失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InventoryCheck entity) {
        // 校验盘点单编号唯一性
        if (StringUtils.isNotBlank(entity.getCheckCode())) {
            LambdaQueryWrapper<InventoryCheck> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(InventoryCheck::getCheckCode, entity.getCheckCode());
            if (entity.getCheckId() != null) {
                wrapper.ne(InventoryCheck::getCheckId, entity.getCheckId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("盘点单编号已存在：" + entity.getCheckCode());
            }
        }
    }

    /**
     * 校验并批量删除库存盘点信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验盘点单状态，只有草稿状态的盘点单才能删除
            List<InventoryCheck> checks = baseMapper.selectByIds(ids);
            for (InventoryCheck check : checks) {
                if (!InventoryCheckStatus.DRAFT.getValue().equals(check.getCheckStatus())) {
                    throw new ServiceException("盘点单【" + check.getCheckCode() + "】状态为【" +
                        check.getCheckStatus() + "】，不允许删除");
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 开始盘点
     *
     * @param checkId 盘点单ID
     * @return 是否开始成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean startCheck(Long checkId) {
        InventoryCheck check = baseMapper.selectById(checkId);
        if (check == null) {
            throw new ServiceException("盘点单不存在");
        }

        // 校验状态
        if (!InventoryCheckStatus.DRAFT.getValue().equals(check.getCheckStatus())) {
            throw new ServiceException("盘点单【" + check.getCheckCode() + "】状态为【" +
                check.getCheckStatus() + "】，不允许开始盘点");
        }

        // 更新状态为盘点中
        check.setCheckStatus(InventoryCheckStatus.IN_PROGRESS.getValue());
        boolean result = baseMapper.updateById(check) > 0;

        if (result) {
            log.info("盘点单【{}】开始盘点", check.getCheckCode());
        }

        return result;
    }

    /**
     * 完成盘点
     *
     * @param checkId 盘点单ID
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeCheck(Long checkId) {
        InventoryCheck check = baseMapper.selectById(checkId);
        if (check == null) {
            throw new ServiceException("盘点单不存在");
        }

        // 校验状态
        if (!InventoryCheckStatus.IN_PROGRESS.getValue().equals(check.getCheckStatus())) {
            throw new ServiceException("盘点单【" + check.getCheckCode() + "】状态为【" +
                check.getCheckStatus() + "】，不允许完成盘点");
        }

        // 更新状态为已完成
        check.setCheckStatus(InventoryCheckStatus.COMPLETED.getValue());
        boolean result = baseMapper.updateById(check) > 0;

        if (result) {
            log.info("盘点单【{}】完成盘点", check.getCheckCode());
        }

        return result;
    }

    /**
     * 审核盘点
     *
     * @param checkId 盘点单ID
     * @return 是否审核成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean approveCheck(Long checkId) {
        InventoryCheck check = baseMapper.selectById(checkId);
        if (check == null) {
            throw new ServiceException("盘点单不存在");
        }

        // 校验状态
        if (!InventoryCheckStatus.COMPLETED.getValue().equals(check.getCheckStatus())) {
            throw new ServiceException("盘点单【" + check.getCheckCode() + "】状态为【" +
                check.getCheckStatus() + "】，不允许审核");
        }

        try {
            // 先调整库存，如果失败则不更新状态
            adjustInventoryByCheck(checkId);

            // 库存调整成功后，更新状态为已审核
            check.setCheckStatus(InventoryCheckStatus.APPROVED.getValue());
            boolean result = baseMapper.updateById(check) > 0;

            if (result) {
                log.info("盘点单【{}】审核通过，库存已调整", check.getCheckCode());
            }

            return result;
        } catch (Exception e) {
            log.error("盘点单【{}】审核失败，库存调整异常：{}", check.getCheckCode(), e.getMessage(), e);
            throw new ServiceException("盘点审核失败：" + e.getMessage());
        }
    }

    /**
     * 取消盘点
     *
     * @param checkId 盘点单ID
     * @param reason  取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelCheck(Long checkId, String reason) {
        InventoryCheck check = baseMapper.selectById(checkId);
        if (check == null) {
            throw new ServiceException("盘点单不存在");
        }

        // 校验状态，只有草稿和盘点中状态可以取消
        if (!InventoryCheckStatus.DRAFT.getValue().equals(check.getCheckStatus()) &&
            !InventoryCheckStatus.IN_PROGRESS.getValue().equals(check.getCheckStatus())) {
            throw new ServiceException("盘点单【" + check.getCheckCode() + "】状态为【" +
                check.getCheckStatus() + "】，不允许取消");
        }

        // 更新状态为已取消，并记录取消原因
        check.setCheckStatus(InventoryCheckStatus.CANCELLED.getValue());
        if (StringUtils.isNotBlank(reason)) {
            check.setRemark(StringUtils.isBlank(check.getRemark()) ?
                "取消原因：" + reason : check.getRemark() + "；取消原因：" + reason);
        }
        boolean result = baseMapper.updateById(check) > 0;

        if (result) {
            log.info("盘点单【{}】取消成功，原因：{}", check.getCheckCode(), reason);
        }

        return result;
    }

    /**
     * 生成盘点单
     *
     * @param locationId 仓库ID
     * @return 创建的盘点单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InventoryCheckVo generateCheck(Long locationId) {
        InventoryBatchBo queryBo = new InventoryBatchBo();
        if (locationId != null) {
            queryBo.setLocationId(locationId);
        }

        List<InventoryBatchVo> inventoryBatches = inventoryBatchService.queryList(queryBo);
        if (inventoryBatches.isEmpty()) {
            throw new ServiceException("该仓库/库区没有库存数据，无法生成盘点单");
        }

        // 创建盘点单
        InventoryCheck check = new InventoryCheck();
        check.setCheckCode(gen.code(WMS_INVENTORY_CHECK_CODE));
        check.setCheckName("系统生成盘点单");
        check.setLocationId(locationId);
        check.setCheckStatus(InventoryCheckStatus.DRAFT.getValue());
        check.setRemark("系统自动生成");

        // 插入盘点单
        boolean flag = baseMapper.insert(check) > 0;
        if (!flag) {
            throw new ServiceException("创建盘点单失败");
        }

        List<InventoryCheckItemBo> checkItemBos = inventoryBatches.stream().map(batch -> {
            InventoryCheckItemBo itemBo = new InventoryCheckItemBo();
            itemBo.setCheckId(check.getCheckId());
            itemBo.setProductId(batch.getProductId());
            itemBo.setProductCode(batch.getProductCode());
            itemBo.setProductName(batch.getProductName());
            itemBo.setUnitId(batch.getUnitId());
            itemBo.setUnitCode(batch.getUnitCode());
            itemBo.setUnitName(batch.getUnitName());
            itemBo.setLocationId(batch.getLocationId());
            itemBo.setLocationCode(batch.getLocationCode());
            itemBo.setLocationName(batch.getLocationName());
            // 记录批次信息
            itemBo.setInternalBatchNumber(batch.getInternalBatchNumber());
            itemBo.setSerialNumber(batch.getSerialNumber());
            // 修复类型转换：字段都是BigDecimal类型，直接设置
            itemBo.setBookQuantity(batch.getQuantity()); // 账面数量
            itemBo.setActualQuantity(BigDecimal.ZERO); // 实盘数量，待盘点
            itemBo.setDifferenceQuantity(BigDecimal.ZERO); // 差异数量
            itemBo.setRemark("系统生成");
            return itemBo;
        }).collect(Collectors.toList());

        itemService.insertOrUpdateBatch(checkItemBos);

        log.info("生成盘点单【{}】成功，包含{}个明细", check.getCheckCode(), checkItemBos.size());

        return queryById(check.getCheckId());
    }

    /**
     * 根据盘点结果调整库存
     *
     * @param checkId 盘点单ID
     */
    private void adjustInventoryByCheck(Long checkId) {
        List<InventoryCheckItemVo> items = itemService.selectListByCheckId(checkId);

        for (InventoryCheckItemVo item : items) {
            // 计算差异数量
            BigDecimal differenceQty = item.getActualQuantity().subtract(item.getBookQuantity());
            if (differenceQty.compareTo(BigDecimal.ZERO) != 0) {

                InventoryBatchBo queryBo = new InventoryBatchBo();
                queryBo.setProductId(item.getProductId());
                queryBo.setLocationId(item.getLocationId());
                if (StringUtils.isNotBlank(item.getBatchNumber())) {
                    queryBo.setInternalBatchNumber(item.getBatchNumber());
                }
                if (StringUtils.isNotBlank(item.getSerialNumber())) {
                    queryBo.setSerialNumber(item.getSerialNumber());
                }

                List<InventoryBatchVo> batches = inventoryBatchService.queryList(queryBo);
                InventoryBatchVo batch = batches.isEmpty() ? null : batches.get(0);

                if (batch != null) {
                    // 记录调整前的数量
                    BigDecimal beforeQuantity = batch.getQuantity();

                    InventoryBatchBo updateBo = new InventoryBatchBo();
                    updateBo.setInventoryBatchId(batch.getInventoryBatchId());
                    updateBo.setQuantity(item.getActualQuantity());
                    Boolean updateResult = inventoryBatchService.updateByBo(updateBo);
                    if (!updateResult) {
                        throw new ServiceException("更新库存批次失败：产品【" + item.getProductCode() + "】");
                    }

                    InventoryLogBo logBo = new InventoryLogBo();
                    logBo.setProductId(item.getProductId());
                    logBo.setProductCode(item.getProductCode());
                    logBo.setProductName(item.getProductName());
                    logBo.setUnitId(item.getUnitId());
                    logBo.setUnitCode(item.getUnitCode());
                    logBo.setUnitName(item.getUnitName());
                    logBo.setLocationId(item.getLocationId());
                    logBo.setLocationCode(item.getLocationCode());
                    logBo.setLocationName(item.getLocationName());
                    logBo.setBeforeQuantity(beforeQuantity); // 调整前数量
                    logBo.setQuantity(differenceQty.abs()); // 记录变动数量的绝对值
                    logBo.setAfterQuantity(item.getActualQuantity()); // 调整后数量
                    logBo.setDirection(differenceQty.compareTo(BigDecimal.ZERO) > 0 ?
                        InventoryDirection.IN.getValue() : InventoryDirection.OUT.getValue());
                    logBo.setSourceType(SourceType.INVENTORY_CHECK);
                    logBo.setSourceId(checkId);
                    // 获取盘点单编号
                    InventoryCheck checkEntity = baseMapper.selectById(checkId);
                    logBo.setSourceCode(checkEntity != null ? checkEntity.getCheckCode() : "");
                    logBo.setSourceName("库存盘点调整");
                    logBo.setReasonCode("INVENTORY_ADJUSTMENT");
                    logBo.setRecordTime(LocalDateTime.now());
                    logBo.setRemark("盘点调整：账面数量" + item.getBookQuantity() +
                        "，实盘数量" + item.getActualQuantity() +
                        "，差异数量" + differenceQty);

                    Boolean logResult = inventoryLogService.insertByBo(logBo);
                    if (!logResult) {
                        throw new ServiceException("记录库存日志失败：产品【" + item.getProductCode() + "】");
                    }

                    // 3. 更新库存汇总表（Inventory）
                    updateInventorySummary(item.getProductId(), differenceQty);

                    log.info("库存调整：产品【{}】位置【{}】批次【{}】从{}调整为{}",
                        item.getProductCode(), item.getLocationCode(),
                        item.getBatchNumber(), item.getBookQuantity(), item.getActualQuantity());
                } else {
                    throw new ServiceException("未找到对应的库存批次：产品【" + item.getProductCode() +
                        "】位置【" + item.getLocationCode() + "】批次【" + item.getBatchNumber() + "】");
                }
            }
        }
    }

    /**
     * 更新库存汇总表
     *
     * @param productId     产品ID
     * @param differenceQty 差异数量
     */
    private void updateInventorySummary(Long productId, BigDecimal differenceQty) {
        InventoryBo queryBo = new InventoryBo();
        queryBo.setProductId(productId);
        List<InventoryVo> inventories = inventoryService.queryList(queryBo);

        if (!inventories.isEmpty()) {
            InventoryVo inventory = inventories.get(0);
            InventoryBo updateBo = new InventoryBo();
            updateBo.setInventoryId(inventory.getInventoryId());
            BigDecimal newQuantity = inventory.getQuantity().add(differenceQty);
            updateBo.setQuantity(newQuantity);
            inventoryService.updateByBo(updateBo);
        } else {
            // 如果汇总记录不存在，需要重新计算该产品的总库存
            InventoryBatchBo batchQueryBo = new InventoryBatchBo();
            batchQueryBo.setProductId(productId);
            List<InventoryBatchVo> batches = inventoryBatchService.queryList(batchQueryBo);

            BigDecimal totalQuantity = batches.stream()
                .filter(batch -> batch.getQuantity().compareTo(BigDecimal.ZERO) > 0)
                .map(InventoryBatchVo::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 创建新的汇总记录（这里需要根据实际业务需求来完善）
            log.warn("产品【{}】的库存汇总记录不存在，当前总库存为：{}", productId, totalQuantity);
        }
    }
}
