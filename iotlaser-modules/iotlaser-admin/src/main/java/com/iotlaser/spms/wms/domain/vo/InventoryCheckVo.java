package com.iotlaser.spms.wms.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.wms.domain.InventoryCheck;
import com.iotlaser.spms.wms.domain.InventoryCheckItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 库存盘点视图对象 wms_inventory_check
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InventoryCheck.class)
public class InventoryCheckVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 盘点单ID
     */
    @ExcelProperty(value = "盘点单ID")
    private Long checkId;

    /**
     * 盘点单编号
     */
    @ExcelProperty(value = "盘点单编号")
    private String checkCode;

    /**
     * 盘点单名称
     */
    @ExcelProperty(value = "盘点单名称")
    private String checkName;

    /**
     * 盘点类型
     */
    @ExcelProperty(value = "盘点类型")
    private String checkType;

    /**
     * 盘点范围
     */
    @ExcelProperty(value = "盘点范围")
    private String checkScope;

    /**
     * 库位ID
     */
    @ExcelProperty(value = "库位ID")
    private Long locationId;

    /**
     * 库位编码
     */
    @ExcelProperty(value = "库位编码")
    private String locationCode;

    /**
     * 库位名称
     */
    @ExcelProperty(value = "库位名称")
    private String locationName;

    /**
     * 计划开始时间
     */
    @ExcelProperty(value = "计划开始时间")
    private Date plannedStartTime;

    /**
     * 计划结束时间
     */
    @ExcelProperty(value = "计划结束时间")
    private Date plannedEndTime;

    /**
     * 实际开始时间
     */
    @ExcelProperty(value = "实际开始时间")
    private Date actualStartTime;

    /**
     * 实际结束时间
     */
    @ExcelProperty(value = "实际结束时间")
    private Date actualEndTime;

    /**
     * 盘点状态
     */
    @ExcelProperty(value = "盘点状态")
    private String checkStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 盘点明细
     */
    private List<InventoryCheckItem> items;
}
