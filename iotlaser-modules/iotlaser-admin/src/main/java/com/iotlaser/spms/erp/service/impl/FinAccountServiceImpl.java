package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinAccount;
import com.iotlaser.spms.erp.domain.bo.FinAccountBo;
import com.iotlaser.spms.erp.domain.vo.FinAccountVo;
import com.iotlaser.spms.erp.mapper.FinAccountMapper;
import com.iotlaser.spms.erp.service.IFinAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 账户Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinAccountServiceImpl implements IFinAccountService {

    private final FinAccountMapper baseMapper;

    /**
     * 查询账户
     *
     * @param accountId 主键
     * @return 账户
     */
    @Override
    public FinAccountVo queryById(Long accountId) {
        return baseMapper.selectVoById(accountId);
    }

    /**
     * 分页查询账户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 账户分页列表
     */
    @Override
    public TableDataInfo<FinAccountVo> queryPageList(FinAccountBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinAccount> lqw = buildQueryWrapper(bo);
        Page<FinAccountVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的账户列表
     *
     * @param bo 查询条件
     * @return 账户列表
     */
    @Override
    public List<FinAccountVo> queryList(FinAccountBo bo) {
        LambdaQueryWrapper<FinAccount> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinAccount> buildQueryWrapper(FinAccountBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinAccount> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinAccount::getAccountId);
        lqw.eq(StringUtils.isNotBlank(bo.getAccountCode()), FinAccount::getAccountCode, bo.getAccountCode());
        lqw.like(StringUtils.isNotBlank(bo.getAccountName()), FinAccount::getAccountName, bo.getAccountName());
        lqw.eq(StringUtils.isNotBlank(bo.getAccountType()), FinAccount::getAccountType, bo.getAccountType());
        lqw.like(StringUtils.isNotBlank(bo.getBankName()), FinAccount::getBankName, bo.getBankName());
        lqw.eq(StringUtils.isNotBlank(bo.getAccountNumber()), FinAccount::getAccountNumber, bo.getAccountNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrency()), FinAccount::getCurrency, bo.getCurrency());
        lqw.eq(bo.getInitialBalance() != null, FinAccount::getInitialBalance, bo.getInitialBalance());
        lqw.eq(bo.getCurrentBalance() != null, FinAccount::getCurrentBalance, bo.getCurrentBalance());
        lqw.eq(StringUtils.isNotBlank(bo.getAccountStatus()), FinAccount::getAccountStatus, bo.getAccountStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinAccount::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增账户
     *
     * @param bo 账户
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinAccountBo bo) {
        FinAccount add = MapstructUtils.convert(bo, FinAccount.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAccountId(add.getAccountId());
        }
        return flag;
    }

    /**
     * 修改账户
     *
     * @param bo 账户
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinAccountBo bo) {
        FinAccount update = MapstructUtils.convert(bo, FinAccount.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinAccount entity) {
        // 校验账户编码唯一性
        if (StringUtils.isNotBlank(entity.getAccountCode())) {
            LambdaQueryWrapper<FinAccount> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinAccount::getAccountCode, entity.getAccountCode());
            if (entity.getAccountId() != null) {
                wrapper.ne(FinAccount::getAccountId, entity.getAccountId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("账户编码已存在：" + entity.getAccountCode());
            }
        }

        // 校验账户名称唯一性
        if (StringUtils.isNotBlank(entity.getAccountName())) {
            LambdaQueryWrapper<FinAccount> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinAccount::getAccountName, entity.getAccountName());
            if (entity.getAccountId() != null) {
                wrapper.ne(FinAccount::getAccountId, entity.getAccountId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("账户名称已存在：" + entity.getAccountName());
            }
        }

        // 校验银行账号唯一性
        if (StringUtils.isNotBlank(entity.getAccountNumber())) {
            LambdaQueryWrapper<FinAccount> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinAccount::getAccountNumber, entity.getAccountNumber());
            if (entity.getAccountId() != null) {
                wrapper.ne(FinAccount::getAccountId, entity.getAccountId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("银行账号已存在：" + entity.getAccountNumber());
            }
        }
    }

    /**
     * 校验并批量删除账户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验账户是否可以删除
            List<FinAccount> accounts = baseMapper.selectByIds(ids);
            for (FinAccount account : accounts) {
                // 1. 检查账户状态，只有非活跃状态的账户才能删除
                if ("ACTIVE".equals(account.getAccountStatus())) {
                    throw new ServiceException("账户【" + account.getAccountName() + "】状态为活跃，不允许删除");
                }

                // 2. 检查账户余额，余额不为零的账户不能删除
                // TODO: 在FinAccount实体中添加getBalance()方法
                // if (account.getBalance() != null && account.getBalance().compareTo(BigDecimal.ZERO) != 0) {
                //     throw new ServiceException("账户【" + account.getAccountName() + "】余额不为零，不允许删除");
                // }

                // 3. 检查是否有关联的流水记录
                // TODO: 添加finAccountLedgerService依赖注入
                // if (finAccountLedgerService.existsByAccountId(account.getAccountId())) {
                //     throw new ServiceException("账户【" + account.getAccountName() + "】存在流水记录，不允许删除");
                // }

                log.info("删除账户校验通过：{}", account.getAccountName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除账户成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除账户失败：{}", e.getMessage(), e);
            throw new ServiceException("删除账户失败：" + e.getMessage());
        }
    }

    /**
     * 更新账户余额
     *
     * @param accountId 账户ID
     * @param amount    变动金额 (正数为增加，负数为减少)
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAccountBalance(Long accountId, BigDecimal amount) {
        try {
            FinAccount account = baseMapper.selectById(accountId);
            if (account == null) {
                throw new ServiceException("账户不存在");
            }

            BigDecimal newBalance = account.getCurrentBalance().add(amount);
            if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("账户余额不足");
            }

            account.setCurrentBalance(newBalance);
            boolean result = baseMapper.updateById(account) > 0;

            log.info("账户余额更新成功 - 账户ID: {}, 变动金额: {}, 新余额: {}",
                accountId, amount, newBalance);

            return result;
        } catch (Exception e) {
            log.error("账户余额更新失败 - 账户ID: {}, 变动金额: {}, 错误: {}",
                accountId, amount, e.getMessage(), e);
            throw new ServiceException("账户余额更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取账户当前余额
     *
     * @param accountId 账户ID
     * @return 当前余额
     */
    @Override
    public BigDecimal getAccountBalance(Long accountId) {
        FinAccount account = baseMapper.selectById(accountId);
        if (account == null) {
            throw new ServiceException("账户不存在");
        }
        return account.getCurrentBalance();
    }
}
