package com.iotlaser.spms.erp.service;

import java.util.List;
import java.util.Map;

/**
 * 三单匹配服务接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/06/21
 */
public interface IThreeWayMatchService {

    /**
     * 获取三单匹配工作台数据
     *
     * @param supplierId 供应商ID（可选）
     * @param status     匹配状态（可选）
     * @return 三单匹配工作台数据
     */
    Map<String, Object> getThreeWayMatchWorkbench(Long supplierId, String status);

    /**
     * 执行三单匹配
     *
     * @param orderId      采购订单ID
     * @param inboundId    入库单ID
     * @param invoiceId    发票ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 匹配结果
     */
    Map<String, Object> performThreeWayMatch(Long orderId, Long inboundId, Long invoiceId,
                                             Long operatorId, String operatorName);

    /**
     * 获取智能匹配建议
     *
     * @param orderId 采购订单ID
     * @return 匹配建议列表
     */
    List<ThreeWayMatchSuggestion> getMatchSuggestions(Long orderId);

    /**
     * 批量三单匹配
     *
     * @param matchRequests 匹配请求列表
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 批量匹配结果
     */
    Map<String, Object> batchThreeWayMatch(List<ThreeWayMatchRequest> matchRequests,
                                           Long operatorId, String operatorName);

    /**
     * 获取匹配差异分析
     *
     * @param orderId   采购订单ID
     * @param inboundId 入库单ID
     * @param invoiceId 发票ID
     * @return 差异分析结果
     */
    Map<String, Object> analyzeMatchDifferences(Long orderId, Long inboundId, Long invoiceId);

    /**
     * 处理匹配差异
     *
     * @param matchId        匹配记录ID
     * @param differenceType 差异类型
     * @param handleMethod   处理方式
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 是否处理成功
     */
    Boolean handleMatchDifference(Long matchId, String differenceType, String handleMethod,
                                  Long operatorId, String operatorName);

    /**
     * 三单匹配建议内部类
     */
    class ThreeWayMatchSuggestion {
        private Long orderId;
        private String orderCode;
        private Long inboundId;
        private String inboundCode;
        private Long invoiceId;
        private String invoiceCode;
        private double matchScore;
        private String matchReason;
        private List<String> differences;

        // getters and setters
        public Long getOrderId() {
            return orderId;
        }

        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }

        public String getOrderCode() {
            return orderCode;
        }

        public void setOrderCode(String orderCode) {
            this.orderCode = orderCode;
        }

        public Long getInboundId() {
            return inboundId;
        }

        public void setInboundId(Long inboundId) {
            this.inboundId = inboundId;
        }

        public String getInboundCode() {
            return inboundCode;
        }

        public void setInboundCode(String inboundCode) {
            this.inboundCode = inboundCode;
        }

        public Long getInvoiceId() {
            return invoiceId;
        }

        public void setInvoiceId(Long invoiceId) {
            this.invoiceId = invoiceId;
        }

        public String getInvoiceCode() {
            return invoiceCode;
        }

        public void setInvoiceCode(String invoiceCode) {
            this.invoiceCode = invoiceCode;
        }

        public double getMatchScore() {
            return matchScore;
        }

        public void setMatchScore(double matchScore) {
            this.matchScore = matchScore;
        }

        public String getMatchReason() {
            return matchReason;
        }

        public void setMatchReason(String matchReason) {
            this.matchReason = matchReason;
        }

        public List<String> getDifferences() {
            return differences;
        }

        public void setDifferences(List<String> differences) {
            this.differences = differences;
        }
    }

    /**
     * 三单匹配请求内部类
     */
    class ThreeWayMatchRequest {
        private Long orderId;
        private Long inboundId;
        private Long invoiceId;
        private String remark;

        // getters and setters
        public Long getOrderId() {
            return orderId;
        }

        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }

        public Long getInboundId() {
            return inboundId;
        }

        public void setInboundId(Long inboundId) {
            this.inboundId = inboundId;
        }

        public Long getInvoiceId() {
            return invoiceId;
        }

        public void setInvoiceId(Long invoiceId) {
            this.invoiceId = invoiceId;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
}
