package com.iotlaser.spms.pro.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 工艺路线工序对象 pro_routing_step
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pro_routing_step")
public class RoutingStep extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 步骤ID
     */
    @TableId(value = "step_id")
    private Long stepId;

    /**
     * 路线ID
     */
    private Long routingId;

    /**
     * 工序ID
     */
    private Long processId;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 下一步骤ID
     */
    private Long nextStepId;

    /**
     * 返工步骤ID
     */
    private Long reworkStepId;

    /**
     * 准备时间(分钟)
     */
    private Long setupTime;

    /**
     * 加工时间(分钟)
     */
    private Long processingTime;

    /**
     * 质量检验规格要求
     */
    private String qualityCheckSpecs;

    /**
     * 工作重心
     */
    private String reportType;

    /**
     * 工序顺序
     */
    private Long orderNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
