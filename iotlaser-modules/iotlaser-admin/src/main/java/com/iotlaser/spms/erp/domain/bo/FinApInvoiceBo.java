package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinApInvoice;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 供应商发票业务对象 erp_fin_ap_invoice
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinApInvoice.class, reverseConvertGenerate = false)
public class FinApInvoiceBo extends BaseEntity {

    /**
     * 应付ID
     */
    @NotNull(message = "应付ID不能为空", groups = {EditGroup.class})
    private Long invoiceId;

    /**
     * 应付编号
     */
    private String invoiceCode;

    /**
     * 应付名称
     */
    private String invoiceName;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 开票日期
     */
    private LocalDate invoiceDate;

    /**
     * 金额（含税）
     */
    private BigDecimal amount;

    /**
     * 金额（不含税）
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 总税额
     */
    private BigDecimal taxAmount;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人
     */
    private String applicantName;

    /**
     * 经办人ID
     */
    private Long handlerId;

    /**
     * 经办人
     */
    private String handlerName;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审批人
     */
    private String approverName;

    /**
     * 应付状态
     */
    private String invoiceStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
