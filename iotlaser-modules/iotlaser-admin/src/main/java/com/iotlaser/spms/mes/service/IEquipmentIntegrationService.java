package com.iotlaser.spms.mes.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备管理集成Service接口
 * 高优先级功能：设备管理集成
 *
 * <AUTHOR>
 * @date 2025/06/16
 */
public interface IEquipmentIntegrationService {

    /**
     * 检查设备状态
     *
     * @param equipmentId 设备ID
     * @return 设备状态信息
     */
    Map<String, Object> checkEquipmentStatus(Long equipmentId);

    /**
     * 创建设备使用记录
     *
     * @param instanceCode 产品实例编码
     * @param stepId       工序ID
     * @param equipmentId  设备ID
     * @param operatorId   操作员ID
     * @param usageType    使用类型（START, END）
     * @return 使用记录ID
     */
    Long createEquipmentUsageRecord(String instanceCode, Long stepId, Long equipmentId,
                                    Long operatorId, String usageType);

    /**
     * 获取设备效率统计
     *
     * @param equipmentId 设备ID
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return 效率统计数据
     */
    Map<String, Object> getEquipmentEfficiencyStatistics(Long equipmentId, Date startDate, Date endDate);

    /**
     * 设备预防性维护检查
     *
     * @param equipmentId 设备ID
     * @return 维护检查结果
     */
    Map<String, Object> checkPreventiveMaintenance(Long equipmentId);

    /**
     * 设备故障报告
     *
     * @param equipmentId      设备ID
     * @param faultDescription 故障描述
     * @param faultType        故障类型
     * @param reporterId       报告人ID
     * @return 故障报告ID
     */
    Long reportEquipmentFault(Long equipmentId, String faultDescription, String faultType, Long reporterId);

    /**
     * 获取设备实时状态监控
     *
     * @param equipmentIds 设备ID列表
     * @return 实时状态信息
     */
    List<Map<String, Object>> getEquipmentRealTimeStatus(List<Long> equipmentIds);

    /**
     * 设备产能分析
     *
     * @param equipmentId  设备ID
     * @param analysisDate 分析日期
     * @return 产能分析结果
     */
    Map<String, Object> analyzeEquipmentCapacity(Long equipmentId, Date analysisDate);
}
