package com.iotlaser.spms.mes.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.mes.domain.ProductionIssueItem;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 生产领料明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
public interface ProductionIssueItemMapper extends BaseMapperPlus<ProductionIssueItem, ProductionIssueItemVo> {


    /**
     * 查询生产领料明细表及其关联信息
     */
    ProductionIssueItemVo queryByIdWith(@Param("itemId") Long itemId);

    /**
     * 分页查询生产领料明细表及其关联信息
     */
    List<ProductionIssueItemVo> queryPageListWith(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<ProductionIssueItem> wrapper);


}
