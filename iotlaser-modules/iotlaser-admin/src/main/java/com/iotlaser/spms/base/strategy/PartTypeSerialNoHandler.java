package com.iotlaser.spms.base.strategy;

import cn.hutool.core.util.ObjectUtil;
import com.iotlaser.spms.base.domain.bo.AutoCodeResultBo;
import com.iotlaser.spms.base.domain.vo.AutoCodePartVo;
import com.iotlaser.spms.base.domain.vo.AutoCodeResultVo;
import com.iotlaser.spms.base.enums.CycleMethod;
import com.iotlaser.spms.base.service.IAutoCodeResultService;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.enums.FormatsType;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Order(3)
public class PartTypeSerialNoHandler implements PartTypeTemplate {

    private final IAutoCodeResultService autoCodeResultService;

    @Override
    public String partHandle(AutoCodePartVo vo) {
        if ("Y".equals(vo.getCycleFlag())) {
            return generateSerialWithCycle(vo);
        } else {
            return generateSerialWithoutCycle(vo);
        }
    }

    /**
     * 根据循环方式生成序列号
     * <p>
     * 此方法根据提供的自动编码部分信息和循环方法代码来生成序列号
     * 它首先确定循环方法的有效性，然后根据循环方法生成循环查询参数
     * 最后，使用这些参数查找上一个周期的序列号，并根据当前规则计算下一个序列号
     *
     * @param vo 自动编码部分的视图对象，包含生成序列号所需的信息
     * @return 生成的序列号字符串
     * @throws IllegalArgumentException 如果循环方法代码无效或自定义循环方式未提供输入字符
     */
    private String generateSerialWithCycle(AutoCodePartVo vo) {
        // 获取循环方法代码
        CycleMethod cycleMethodEnum = vo.getCycleMethod();
        // 初始化循环查询参数为空
        String cycleQueryParam = "";
        // 根据循环方法代码获取对应的枚举对象
        // 检查循环方法是否有效，且不是其他自定义循环方式
        if (cycleMethodEnum == null && CycleMethod.OTHER != cycleMethodEnum) {
            throw new IllegalArgumentException("无效的循环方法代码: " + cycleMethodEnum);
        }
        // 处理自定义循环方式(OTHER)
        if (CycleMethod.OTHER == cycleMethodEnum) {
            cycleQueryParam = vo.getInputCharacter();
            // 如果自定义循环方式未提供输入字符，抛出异常
            if (StringUtils.isEmpty(cycleQueryParam)) {
                throw new IllegalArgumentException("自定义循环方式(OTHER)需要提供输入字符作为循环依据");
            }
        } else {
            // 根据不同的循环方法枚举值设置循环查询参数
            cycleQueryParam = switch (cycleMethodEnum) {
                case YEAR -> DateUtils.parseDateToStr(FormatsType.YYYY, DateUtils.getNowDate());
                case MONTH -> DateUtils.parseDateToStr(FormatsType.YYYYMM, DateUtils.getNowDate());
                case DAY -> DateUtils.parseDateToStr(FormatsType.YYYYMMDD, DateUtils.getNowDate());
                case HOUR -> DateUtils.parseDateToStr(FormatsType.YYYYMMDDHH, DateUtils.getNowDate());
                case MINUTE -> DateUtils.parseDateToStr(FormatsType.YYYYMMDDHHMM, DateUtils.getNowDate());
                default -> cycleQueryParam;
            };
        }
        // 根据循环查询参数和循环方法代码查找上一个周期的结果
        AutoCodeResultVo lastResultForCycle = findLastResultForCycle(vo.getRuleId(), cycleQueryParam, cycleMethodEnum);
        // 初始化下一个序列号
        long nextSerialNo;
        if (ObjectUtil.isNotNull(lastResultForCycle)) {
            // 如果找到上一个周期的结果，序列号递增
            Gen.IS_NEW_SERIAL_CYCLE_FLAG.set(false);
            nextSerialNo = lastResultForCycle.getLastSerialNo() + vo.getSeriaStep();
        } else {
            // 如果没有找到上一个周期的结果，使用起始序列号
            Gen.IS_NEW_SERIAL_CYCLE_FLAG.set(true);
            nextSerialNo = vo.getSeriaStartNo();
        }
        // 格式化序列号并返回
        return formatSerialNo(nextSerialNo, vo.getPartLength());
    }

    /**
     * 生成一个不包含循环的序列号
     * 该方法用于在序列号生成中处理非循环规则的情况
     *
     * @param vo 包含序列号生成规则信息的实体对象
     * @return 根据规则生成的序列号字符串
     */
    private String generateSerialWithoutCycle(AutoCodePartVo vo) {

        // 查找上一个序列号生成结果，用于确定新的序列号起点
        AutoCodeResultVo lastResultOverall = findLastResultForCycle(vo.getRuleId(), null, null);
        long nextSerialNo;
        // 如果存在上一个序列号生成结果
        if (ObjectUtil.isNotNull(lastResultOverall)) {
            // 设置标志表示这不是一个新的序列号循环
            Gen.IS_NEW_SERIAL_CYCLE_FLAG.set(false);
            // 计算下一个序列号
            nextSerialNo = lastResultOverall.getLastSerialNo() + vo.getSeriaStep();
        } else {
            // 如果不存在上一个序列号生成结果，表示这是一个新的序列号循环
            Gen.IS_NEW_SERIAL_CYCLE_FLAG.set(true);
            // 使用起始序列号作为下一个序列号
            nextSerialNo = vo.getSeriaStartNo();
        }
        // 格式化序列号并返回
        return formatSerialNo(nextSerialNo, vo.getPartLength());
    }

    /**
     * 根据规则ID和周期查询参数查找当前周期的最新生成结果
     *
     * @param ruleId          规则ID，用于标识特定的生成规则
     * @param cycleQueryParam 周期查询参数，根据不同的周期方法代码有不同的用途
     * @param method          周期方法代码，决定了如何处理周期查询参数
     * @return 返回最新的生成结果对象，包含查询到的数据
     */
    private AutoCodeResultVo findLastResultForCycle(Long ruleId, String cycleQueryParam, CycleMethod method) {
        // 创建查询对象并设置规则ID
        AutoCodeResultBo queryBo = new AutoCodeResultBo();
        queryBo.setRuleId(ruleId);
        // 根据周期方法代码处理查询参数
        if (method != null) {
            // 如果周期方法为其他特殊处理
            if (CycleMethod.OTHER == method) {
                queryBo.setLastInputChar(cycleQueryParam);
            } else if (StringUtils.isNotBlank(cycleQueryParam)) {
                // 对于其他周期方法，设置生成日期为查询参数
                queryBo.setGenDate(cycleQueryParam);
            }
        }
        // 调用服务方法查找最新的生成结果
        return autoCodeResultService.findLastResult(queryBo, method);
    }

    /**
     * 格式化序列号为指定长度的字符串
     * 如果指定的部分长度小于等于0，则直接返回序列号的字符串表示
     * 此方法主要用于生成固定长度的序列号字符串，以便在需要的地方使用
     *
     * @param serialNo   需要格式化的原始序列号
     * @param partLength 指定的序列号字符串长度
     * @return 格式化后的序列号字符串
     */
    private String formatSerialNo(long serialNo, long partLength) {
        // 当指定长度小于等于0时，直接返回序列号的字符串表示
        if (partLength <= 0) {
            return String.valueOf(serialNo);
        }
        // 构造格式化字符串，例如"%04d"表示四位数，不足四位则前面补零
        String format = "%0" + partLength + "d";
        // 使用格式化字符串将序列号格式化为指定长度的字符串
        return String.format(format, serialNo);
    }

}
