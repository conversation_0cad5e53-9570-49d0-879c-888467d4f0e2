package com.iotlaser.spms.mes.service;

import com.iotlaser.spms.mes.domain.bo.ProductionReportBo;
import com.iotlaser.spms.mes.domain.vo.ProductionReportVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 生产报工记录Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-15
 */
public interface IProductionReportService {

    /**
     * 查询生产报工记录
     *
     * @param reportId 主键
     * @return 生产报工记录
     */
    ProductionReportVo queryById(Long reportId);

    /**
     * 分页查询生产报工记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产报工记录分页列表
     */
    TableDataInfo<ProductionReportVo> queryPageList(ProductionReportBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的生产报工记录列表
     *
     * @param bo 查询条件
     * @return 生产报工记录列表
     */
    List<ProductionReportVo> queryList(ProductionReportBo bo);

    /**
     * 新增生产报工记录
     *
     * @param bo 生产报工记录
     * @return 是否新增成功
     */
    Boolean insertByBo(ProductionReportBo bo);

    /**
     * 修改生产报工记录
     *
     * @param bo 生产报工记录
     * @return 是否修改成功
     */
    Boolean updateByBo(ProductionReportBo bo);

    /**
     * 校验并批量删除生产报工记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 移动端开工报工
     *
     * @param orderId       生产订单ID
     * @param routingStepId 工艺步骤ID
     * @param operatorId    操作员ID
     * @return 产品实例编码
     */
    String startProduction(Long orderId, Long routingStepId, Long operatorId);

    /**
     * 移动端完工报工
     *
     * @param instanceCode 产品实例编码
     * @param finishQty    完工数量
     * @param qualifiedQty 合格数量
     * @param operatorId   操作员ID
     * @return 是否报工成功
     */
    Boolean finishProduction(String instanceCode, Integer finishQty, Integer qualifiedQty, Long operatorId);

    /**
     * 移动端暂停报工
     *
     * @param instanceCode 产品实例编码
     * @param pauseReason  暂停原因
     * @param operatorId   操作员ID
     * @return 是否暂停成功
     */
    Boolean pauseProduction(String instanceCode, String pauseReason, Long operatorId);

    /**
     * 移动端恢复报工
     *
     * @param instanceCode 产品实例编码
     * @param operatorId   操作员ID
     * @return 是否恢复成功
     */
    Boolean resumeProduction(String instanceCode, Long operatorId);
}
