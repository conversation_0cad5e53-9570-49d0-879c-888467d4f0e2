package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.SaleReturnItemBatchBo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnItemBatchVo;
import com.iotlaser.spms.erp.service.ISaleReturnItemBatchService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售退货批次明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/saleReturnItemBatch")
public class SaleReturnItemBatchController extends BaseController {

    private final ISaleReturnItemBatchService saleReturnItemBatchService;

    /**
     * 查询销售退货批次明细列表
     */
    @SaCheckPermission("erp:saleReturnItemBatch:list")
    @GetMapping("/list")
    public TableDataInfo<SaleReturnItemBatchVo> list(SaleReturnItemBatchBo bo, PageQuery pageQuery) {
        return saleReturnItemBatchService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出销售退货批次明细列表
     */
    @SaCheckPermission("erp:saleReturnItemBatch:export")
    @Log(title = "销售退货批次明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SaleReturnItemBatchBo bo, HttpServletResponse response) {
        List<SaleReturnItemBatchVo> list = saleReturnItemBatchService.queryList(bo);
        ExcelUtil.exportExcel(list, "销售退货批次明细", SaleReturnItemBatchVo.class, response);
    }

    /**
     * 获取销售退货批次明细详细信息
     *
     * @param batchId 主键
     */
    @SaCheckPermission("erp:saleReturnItemBatch:query")
    @GetMapping("/{batchId}")
    public R<SaleReturnItemBatchVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long batchId) {
        return R.ok(saleReturnItemBatchService.queryById(batchId));
    }

    /**
     * 新增销售退货批次明细
     */
    @SaCheckPermission("erp:saleReturnItemBatch:add")
    @Log(title = "销售退货批次明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SaleReturnItemBatchBo bo) {
        return toAjax(saleReturnItemBatchService.insertByBo(bo));
    }

    /**
     * 修改销售退货批次明细
     */
    @SaCheckPermission("erp:saleReturnItemBatch:edit")
    @Log(title = "销售退货批次明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SaleReturnItemBatchBo bo) {
        return toAjax(saleReturnItemBatchService.updateByBo(bo));
    }

    /**
     * 删除销售退货批次明细
     *
     * @param batchIds 主键串
     */
    @SaCheckPermission("erp:saleReturnItemBatch:remove")
    @Log(title = "销售退货批次明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{batchIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] batchIds) {
        return toAjax(saleReturnItemBatchService.deleteWithValidByIds(List.of(batchIds), true));
    }
}
