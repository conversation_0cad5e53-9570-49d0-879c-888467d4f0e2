package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.PurchaseReturn;
import com.iotlaser.spms.erp.enums.PurchaseReturnStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDate;
import java.util.List;

/**
 * 采购退货业务对象 erp_purchase_return
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PurchaseReturn.class, reverseConvertGenerate = false)
public class PurchaseReturnBo extends BaseEntity {

    /**
     * 退货单ID
     */
    @NotNull(message = "退货单ID不能为空", groups = {EditGroup.class})
    private Long returnId;

    /**
     * 退货单编号
     */
    @NotBlank(message = "退货单编号不能为空", groups = {EditGroup.class})
    private String returnCode;

    /**
     * 退货单名称
     */
    private String returnName;

    /**
     * 采购订单ID
     */
    private Long orderId;

    /**
     * 采购订单编码
     */
    private String orderCode;

    /**
     * 采购订单名称
     */
    private String orderName;

    /**
     * 入库单ID
     */
    private Long inboundId;

    /**
     * 入库单编号
     */
    private String inboundCode;

    /**
     * 入库单名称
     */
    private String inboundName;

    /**
     * 检验单ID
     */
    private Long inspectionId;

    /**
     * 检验单编号
     */
    private String inspectionCode;

    /**
     * 检验单名称
     */
    private String inspectionName;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 退货日期
     */
    private LocalDate returnDate;

    /**
     * 退货状态
     */
    private PurchaseReturnStatus returnStatus;

    /**
     * 退货申请人ID
     */
    private Long applicantId;

    /**
     * 退货申请人
     */
    private String applicantName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 明细
     */
    private List<PurchaseReturnItemBo> items;
}
