package com.iotlaser.spms.base.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.base.domain.AutoCodeRule;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;

/**
 * 编码生成规则视图对象 sys_auto_code_rule
 *
 * <AUTHOR>
 * @date 2025/03/11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AutoCodeRule.class)
public class AutoCodeRuleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 规则ID
     */
    @ExcelProperty(value = "规则ID")
    private Long ruleId;

    /**
     * 规则编码
     */
    @ExcelProperty(value = "规则编码")
    private String ruleCode;

    /**
     * 规则名称
     */
    @ExcelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String ruleDesc;

    /**
     * 最大长度
     */
    @ExcelProperty(value = "最大长度")
    private Integer maxLength;

    /**
     * 是否补齐
     */
    @ExcelProperty(value = "是否补齐", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String isPadded;

    /**
     * 补齐字符
     */
    @ExcelProperty(value = "补齐字符")
    private String paddedChar;

    /**
     * 补齐方式
     */
    @ExcelProperty(value = "补齐方式")
    private String paddedMethod;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 状态 （0正常 1停用）
     */
    @ExcelProperty(value = "状态 ", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

}
