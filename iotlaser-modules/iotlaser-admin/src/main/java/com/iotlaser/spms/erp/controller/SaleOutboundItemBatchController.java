package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundItemBatchBo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundItemBatchVo;
import com.iotlaser.spms.erp.service.ISaleOutboundItemBatchService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售出库批次明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/saleOutboundItemBatch")
public class SaleOutboundItemBatchController extends BaseController {

    private final ISaleOutboundItemBatchService saleOutboundItemBatchService;

    /**
     * 查询销售出库批次明细列表
     */
    @SaCheckPermission("erp:saleOutboundItemBatch:list")
    @GetMapping("/list")
    public TableDataInfo<SaleOutboundItemBatchVo> list(SaleOutboundItemBatchBo bo, PageQuery pageQuery) {
        return saleOutboundItemBatchService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出销售出库批次明细列表
     */
    @SaCheckPermission("erp:saleOutboundItemBatch:export")
    @Log(title = "销售出库批次明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SaleOutboundItemBatchBo bo, HttpServletResponse response) {
        List<SaleOutboundItemBatchVo> list = saleOutboundItemBatchService.queryList(bo);
        ExcelUtil.exportExcel(list, "销售出库批次明细", SaleOutboundItemBatchVo.class, response);
    }

    /**
     * 获取销售出库批次明细详细信息
     *
     * @param batchId 主键
     */
    @SaCheckPermission("erp:saleOutboundItemBatch:query")
    @GetMapping("/{batchId}")
    public R<SaleOutboundItemBatchVo> getInfo(@NotNull(message = "主键不能为空")
                                              @PathVariable Long batchId) {
        return R.ok(saleOutboundItemBatchService.queryById(batchId));
    }

    /**
     * 新增销售出库批次明细
     */
    @SaCheckPermission("erp:saleOutboundItemBatch:add")
    @Log(title = "销售出库批次明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SaleOutboundItemBatchBo bo) {
        return toAjax(saleOutboundItemBatchService.insertByBo(bo));
    }

    /**
     * 修改销售出库批次明细
     */
    @SaCheckPermission("erp:saleOutboundItemBatch:edit")
    @Log(title = "销售出库批次明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SaleOutboundItemBatchBo bo) {
        return toAjax(saleOutboundItemBatchService.updateByBo(bo));
    }

    /**
     * 删除销售出库批次明细
     *
     * @param batchIds 主键串
     */
    @SaCheckPermission("erp:saleOutboundItemBatch:remove")
    @Log(title = "销售出库批次明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{batchIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] batchIds) {
        return toAjax(saleOutboundItemBatchService.deleteWithValidByIds(List.of(batchIds), true));
    }
}
