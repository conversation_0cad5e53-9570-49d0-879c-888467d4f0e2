package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.wms.domain.bo.InventoryCheckBo;
import com.iotlaser.spms.wms.domain.vo.InventoryCheckVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 库存盘点Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-10
 */
public interface IInventoryCheckService {

    /**
     * 查询库存盘点
     *
     * @param checkId 主键
     * @return 库存盘点
     */
    InventoryCheckVo queryById(Long checkId);

    /**
     * 分页查询库存盘点列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 库存盘点分页列表
     */
    TableDataInfo<InventoryCheckVo> queryPageList(InventoryCheckBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的库存盘点列表
     *
     * @param bo 查询条件
     * @return 库存盘点列表
     */
    List<InventoryCheckVo> queryList(InventoryCheckBo bo);

    /**
     * 新增库存盘点
     *
     * @param bo 库存盘点
     * @return 是否新增成功
     */
    Boolean insertByBo(InventoryCheckBo bo);

    /**
     * 修改库存盘点
     *
     * @param bo 库存盘点
     * @return 是否修改成功
     */
    Boolean updateByBo(InventoryCheckBo bo);

    /**
     * 校验并批量删除库存盘点信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 开始盘点
     *
     * @param checkId 盘点单ID
     * @return 是否开始成功
     */
    Boolean startCheck(Long checkId);

    /**
     * 完成盘点
     *
     * @param checkId 盘点单ID
     * @return 是否完成成功
     */
    Boolean completeCheck(Long checkId);

    /**
     * 审核盘点
     *
     * @param checkId 盘点单ID
     * @return 是否审核成功
     */
    Boolean approveCheck(Long checkId);

    /**
     * 取消盘点
     *
     * @param checkId 盘点单ID
     * @param reason  取消原因
     * @return 是否取消成功
     */
    Boolean cancelCheck(Long checkId, String reason);

    /**
     * 生成盘点单
     *
     * @param locationId 仓库ID
     * @return 创建的盘点单
     */
    InventoryCheckVo generateCheck(Long locationId);
}
