package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 应付单明细对象 erp_fin_ap_invoice_item
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_ap_invoice_item")
public class FinApInvoiceItem extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 明细ID
     */
    @TableId(value = "item_id")
    private Long itemId;

    /**
     * 应付ID
     */
    private Long invoiceId;

    /**
     * 上游来源ID
     */
    private Long directSourceId;

    /**
     * 上游来源编号
     */
    private String directSourceCode;

    /**
     * 上游来源名称
     */
    private String directSourceName;

    /**
     * 上游来源类型
     */
    private String directSourceType;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 来源编号
     */
    private String sourceCode;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 来源明细ID
     */
    private Long sourceItemId;

    /**
     * 来源批次ID
     */
    private Long sourceBatchId;

    /**
     * 内部批次号
     */
    private String internalBatchNumber;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 单价 (含税)
     */
    private BigDecimal price;

    /**
     * 单价（不含税）
     */
    private BigDecimal priceExclusiveTax;

    /**
     * 金额 (含税)
     */
    private BigDecimal amount;

    /**
     * 金额（不含税）
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 税率 (%)
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
