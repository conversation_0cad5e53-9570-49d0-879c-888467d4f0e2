package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.PurchaseOrderBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 采购订单Service接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IPurchaseOrderService {

    /**
     * 查询采购订单
     *
     * @param orderId 主键
     * @return 采购订单
     */
    PurchaseOrderVo queryById(Long orderId);

    /**
     * 分页查询采购订单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购订单分页列表
     */
    TableDataInfo<PurchaseOrderVo> queryPageList(PurchaseOrderBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的采购订单列表
     *
     * @param bo 查询条件
     * @return 采购订单列表
     */
    List<PurchaseOrderVo> queryList(PurchaseOrderBo bo);

    /**
     * 新增采购订单
     *
     * @param bo 采购订单
     * @return 是否新增成功
     */
    Boolean insertByBo(PurchaseOrderBo bo);

    /**
     * 修改采购订单
     *
     * @param bo 采购订单
     * @return 是否修改成功
     */
    Boolean updateByBo(PurchaseOrderBo bo);

    /**
     * 校验并批量删除采购订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认采购订单
     *
     * @param orderId 订单ID
     * @return 是否确认成功
     */
    Boolean confirmOrder(Long orderId);

    /**
     * 批量确认采购订单
     *
     * @param orderIds 订单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmOrders(Collection<Long> orderIds);

    /**
     * 取消采购订单
     *
     * @param orderId 订单ID
     * @param reason  取消原因
     * @return 是否取消成功
     */
    Boolean cancelOrder(Long orderId, String reason);

    /**
     * 关闭采购订单
     *
     * @param orderId 订单ID
     * @return 是否关闭成功
     */
    Boolean closeOrder(Long orderId);

    /**
     * 更新订单收货状态
     *
     * @param orderId          订单ID
     * @param receivedQuantity 已收货数量映射 (itemId -> quantity)
     * @return 是否更新成功
     */
    Boolean updateReceivedStatus(Long orderId, Map<Long, BigDecimal> receivedQuantity);

    /**
     * 采购订单财务对账
     *
     * @param purchaseOrderId 采购订单ID
     * @return 对账结果
     */
    Map<String, Object> reconcilePurchaseOrder(Long purchaseOrderId);

    /**
     * 自动创建入库单
     *
     * @param orderId 采购订单ID
     * @return 是否创建成功
     */
    Boolean autoCreateInbound(Long orderId);

    /**
     * 批量自动创建入库单
     *
     * @param orderIds 采购订单ID列表
     * @return 创建成功的数量
     */
    Integer batchAutoCreateInbound(List<Long> orderIds);
}
