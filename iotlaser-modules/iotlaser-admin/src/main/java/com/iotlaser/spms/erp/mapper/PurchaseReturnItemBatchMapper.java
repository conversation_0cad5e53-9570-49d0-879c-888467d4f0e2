package com.iotlaser.spms.erp.mapper;

import com.iotlaser.spms.erp.domain.PurchaseReturnItemBatch;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnItemBatchVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 采购退货批次明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025/05/10
 */
public interface PurchaseReturnItemBatchMapper extends BaseMapperPlus<PurchaseReturnItemBatch, PurchaseReturnItemBatchVo> {

}
