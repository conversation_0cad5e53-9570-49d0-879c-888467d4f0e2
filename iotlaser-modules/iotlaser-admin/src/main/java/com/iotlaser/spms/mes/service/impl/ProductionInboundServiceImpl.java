package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.mes.domain.ProductionInbound;
import com.iotlaser.spms.mes.domain.ProductionInboundItem;
import com.iotlaser.spms.mes.domain.bo.ProductionInboundBo;
import com.iotlaser.spms.mes.domain.vo.ProductionInboundVo;
import com.iotlaser.spms.mes.domain.vo.ProductionOrderVo;
import com.iotlaser.spms.mes.enums.ProductionInboundStatus;
import com.iotlaser.spms.mes.mapper.ProductionInboundMapper;
import com.iotlaser.spms.mes.service.IProductionInboundService;
import com.iotlaser.spms.mes.service.IProductionOrderService;
import com.iotlaser.spms.wms.service.IInventoryBatchService;
import com.iotlaser.spms.wms.service.IInventoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

import static com.iotlaser.spms.base.enums.GenCodeType.MES_PRODUCTION_INBOUND_CODE;

/**
 * 生产入库Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductionInboundServiceImpl implements IProductionInboundService {

    private final ProductionInboundMapper baseMapper;
    private final IProductionOrderService productionOrderService;
    private final IInventoryService inventoryService;
    private final IInventoryBatchService inventoryBatchService;
    private final Gen gen;

    /**
     * 查询生产入库
     *
     * @param inboundId 主键
     * @return 生产入库
     */
    @Override
    public ProductionInboundVo queryById(Long inboundId) {
        return baseMapper.selectVoById(inboundId);
    }

    /**
     * 分页查询生产入库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产入库分页列表
     */
    @Override
    public TableDataInfo<ProductionInboundVo> queryPageList(ProductionInboundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductionInbound> lqw = buildQueryWrapper(bo);
        Page<ProductionInboundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产入库列表
     *
     * @param bo 查询条件
     * @return 生产入库列表
     */
    @Override
    public List<ProductionInboundVo> queryList(ProductionInboundBo bo) {
        LambdaQueryWrapper<ProductionInbound> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductionInbound> buildQueryWrapper(ProductionInboundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductionInbound> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductionInbound::getInboundId);
        lqw.eq(StringUtils.isNotBlank(bo.getInboundCode()), ProductionInbound::getInboundCode, bo.getInboundCode());
        lqw.like(StringUtils.isNotBlank(bo.getInboundName()), ProductionInbound::getInboundName, bo.getInboundName());
        lqw.eq(bo.getOrderId() != null, ProductionInbound::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderCode()), ProductionInbound::getOrderCode, bo.getOrderCode());
        lqw.like(StringUtils.isNotBlank(bo.getOrderName()), ProductionInbound::getOrderName, bo.getOrderName());
        lqw.eq(bo.getInspectionId() != null, ProductionInbound::getInspectionId, bo.getInspectionId());
        lqw.eq(StringUtils.isNotBlank(bo.getInspectionCode()), ProductionInbound::getInspectionCode, bo.getInspectionCode());
        lqw.like(StringUtils.isNotBlank(bo.getInspectionName()), ProductionInbound::getInspectionName, bo.getInspectionName());
        // ✅ 优化：移除日期的精确匹配查询，改为使用范围查询
        // 原代码：lqw.eq(bo.getInboundTime() != null, ProductionInbound::getInboundTime, bo.getInboundTime());
        // 入库时间范围查询
        lqw.between(params.get("beginInboundTime") != null && params.get("endInboundTime") != null,
            ProductionInbound::getInboundTime, params.get("beginInboundTime"), params.get("endInboundTime"));
        lqw.eq(bo.getInboundStatus() != null, ProductionInbound::getInboundStatus, bo.getInboundStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductionInbound::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增生产入库
     *
     * @param bo 生产入库
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(ProductionInboundBo bo) {
        if (StringUtils.isEmpty(bo.getInboundCode())) {
            bo.setInboundCode(gen.code(MES_PRODUCTION_INBOUND_CODE));
        }
        bo.setInboundStatus(ProductionInboundStatus.DRAFT);
        ProductionInbound add = MapstructUtils.convert(bo, ProductionInbound.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setInboundId(add.getInboundId());
        }
        return flag;
    }

    /**
     * 修改生产入库
     *
     * @param bo 生产入库
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(ProductionInboundBo bo) {
        ProductionInbound update = MapstructUtils.convert(bo, ProductionInbound.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductionInbound entity) {
        // 校验入库单编号唯一性
        if (StringUtils.isNotBlank(entity.getInboundCode())) {
            LambdaQueryWrapper<ProductionInbound> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ProductionInbound::getInboundCode, entity.getInboundCode());
            if (entity.getInboundId() != null) {
                wrapper.ne(ProductionInbound::getInboundId, entity.getInboundId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("入库单编号已存在：" + entity.getInboundCode());
            }
        }
    }

    /**
     * 校验并批量删除生产入库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验入库单状态，只有草稿状态的入库单才能删除
            List<ProductionInbound> inbounds = baseMapper.selectByIds(ids);
            for (ProductionInbound inbound : inbounds) {
                if (inbound.getInboundStatus() != ProductionInboundStatus.DRAFT) {
                    throw new ServiceException("入库单【" + inbound.getInboundCode() + "】状态为【" +
                        inbound.getInboundStatus() + "】，不允许删除");
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 确认生产入库单
     *
     * @param inboundId 入库单ID
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmInbound(Long inboundId) {
        ProductionInbound inbound = baseMapper.selectById(inboundId);
        if (inbound == null) {
            throw new ServiceException("入库单不存在");
        }

        // 校验状态
        if (inbound.getInboundStatus() != ProductionInboundStatus.DRAFT) {
            throw new ServiceException("入库单【" + inbound.getInboundCode() + "】状态为【" +
                inbound.getInboundStatus() + "】，不允许确认");
        }

        // 进行数量核对验证
        validateInboundQuantity(inbound);

        // 更新状态为待入库
        inbound.setInboundStatus(ProductionInboundStatus.PENDING_WAREHOUSE);
        boolean result = baseMapper.updateById(inbound) > 0;

        if (result) {
            log.info("生产入库单【{}】确认成功", inbound.getInboundCode());
        }

        return result;
    }

    /**
     * 批量确认生产入库单
     *
     * @param inboundIds 入库单ID集合
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchConfirmInbounds(Collection<Long> inboundIds) {
        for (Long inboundId : inboundIds) {
            confirmInbound(inboundId);
        }
        return true;
    }

    /**
     * 完成生产入库
     *
     * @param inboundId 入库单ID
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeInbound(Long inboundId) {
        ProductionInbound inbound = baseMapper.selectById(inboundId);
        if (inbound == null) {
            throw new ServiceException("入库单不存在");
        }

        // 校验状态
        if (inbound.getInboundStatus() != ProductionInboundStatus.PENDING_WAREHOUSE) {
            throw new ServiceException("入库单【" + inbound.getInboundCode() + "】状态为【" +
                inbound.getInboundStatus() + "】，不允许完成入库");
        }

        // 更新状态为已入库
        inbound.setInboundStatus(ProductionInboundStatus.COMPLETED);
        boolean result = baseMapper.updateById(inbound) > 0;

        if (result) {
            log.info("生产入库单【{}】入库完成", inbound.getInboundCode());
            // 处理库存增加逻辑
            processInventoryIncrease(inbound);

            // 同步更新生产订单完工数量和状态
            updateProductionOrderProgress(inbound);
        }

        return result;
    }

    /**
     * 根据生产订单创建入库单
     *
     * @param productionOrderId 生产订单ID
     * @return 创建的入库单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProductionInboundVo createFromProductionOrder(Long productionOrderId) {
        // 获取生产订单信息
        ProductionOrderVo order = productionOrderService.queryById(productionOrderId);
        if (order == null) {
            throw new ServiceException("生产订单不存在");
        }

        // 创建入库单
        ProductionInbound inbound = new ProductionInbound();
        inbound.setInboundCode(gen.code(MES_PRODUCTION_INBOUND_CODE));
        inbound.setInboundName("基于生产订单【" + order.getOrderCode() + "】的入库单");
        inbound.setOrderId(order.getOrderId());
        inbound.setOrderCode(order.getOrderCode());
        inbound.setOrderName(order.getOrderName());
        inbound.setInboundTime(new Date());
        inbound.setInboundStatus(ProductionInboundStatus.DRAFT);
        inbound.setRemark("基于生产订单【" + order.getOrderCode() + "】创建");

        // 插入入库单
        boolean flag = baseMapper.insert(inbound) > 0;
        if (!flag) {
            throw new ServiceException("创建入库单失败");
        }

        // 根据生产订单的产品信息自动创建入库明细
        createInboundItemsFromOrder(inbound, order);

        log.info("基于生产订单【{}】创建入库单【{}】成功", order.getOrderCode(), inbound.getInboundCode());

        return queryById(inbound.getInboundId());
    }

    /**
     * 处理库存增加逻辑
     *
     * @param inbound 生产入库单
     */
    private void processInventoryIncrease(ProductionInbound inbound) {
        try {
            // 1. 获取生产入库明细
            // TODO: 获取入库明细，这里需要根据实际的入库明细Service进行调用
            // ProductionInboundItemBo queryBo = new ProductionInboundItemBo();
            // queryBo.setInboundId(inbound.getInboundId());
            // List<ProductionInboundItemVo> items = itemService.queryList(queryBo);

            // 模拟入库明细数据（实际应该从数据库获取）
            List<ProductionInboundItem> items = createMockInboundItems(inbound);

            if (items.isEmpty()) {
                log.warn("生产入库单【{}】没有明细，跳过库存增加", inbound.getInboundCode());
                return;
            }

            // 2. 遍历明细，调用库存服务增加库存
            for (ProductionInboundItem item : items) {
                // TODO: 调用WMS模块的库存增加服务
                // 基于生产入库明细进行库存增加（遵循入库单→明细→批次的标准结构）
                // 生产入库：向库存中增加成品数量
                // boolean increaseResult = inventoryService.adjustInventory(
                //     item.getProductId(),
                //     item.getLocationId(),
                //     item.getQuantity(), // 正数表示增加
                //     "生产入库：" + inbound.getInboundCode() + " 明细：" + item.getItemId(),
                //     inbound.getCreateBy(), // 使用创建人作为操作人
                //     inbound.getCreateByName() // 使用创建人姓名
                // );
                boolean increaseResult = true; // 临时实现

                if (!increaseResult) {
                    throw new ServiceException("产品【" + item.getProductName() + "】库存增加失败");
                }

                log.info("生产入库库存增加成功：产品【{}】数量【{}】库位【{}】",
                    item.getProductName(), item.getQuantity(), item.getLocationCode());

                // 3. 生成产品批次记录
                generateProductionBatch(item, inbound);
            }

            log.info("生产入库单【{}】库存增加处理完成", inbound.getInboundCode());
        } catch (Exception e) {
            log.error("生产入库单【{}】库存增加失败：{}", inbound.getInboundCode(), e.getMessage(), e);
            throw new ServiceException("库存增加失败：" + e.getMessage());
        }
    }

    /**
     * 生成产品批次记录
     *
     * @param item    入库明细
     * @param inbound 入库单
     */
    private void generateProductionBatch(ProductionInboundItem item, ProductionInbound inbound) {
        try {
            // TODO: 创建生产批次记录
            // InventoryBatch batch = new InventoryBatch();
            //
            // // 生成批次号（生产批次）
            // batch.setInternalBatchNumber("PROD" + System.currentTimeMillis());
            // batch.setExternalBatchNumber(inbound.getInboundCode() + "-" + item.getProductCode());
            //
            // // 产品信息
            // batch.setProductId(item.getProductId());
            // batch.setProductCode(item.getProductCode());
            // batch.setProductName(item.getProductName());
            //
            // // 库位信息
            // batch.setLocationId(item.getLocationId());
            // batch.setLocationCode(item.getLocationCode());
            //
            // // 批次数量
            // batch.setQuantity(item.getQuantity());
            // batch.setAllocatedQuantity(BigDecimal.ZERO);
            //
            // // 生产信息
            // batch.setInventoryTime(LocalDateTime.now());
            // batch.setProductionDate(LocalDate.now());
            //
            // // 有效期（根据产品类型设置）
            // batch.setExpiryTime(calculateExpiryTime(item.getProductId()));
            //
            // // 批次状态
            // batch.setInventoryStatus(InventoryBatchStatus.AVAILABLE.getStatus());
            //
            // // 来源信息
            // batch.setSourceType("PRODUCTION_INBOUND");
            // batch.setSourceId(inbound.getInboundId());
            // batch.setSourceCode(inbound.getInboundCode());
            //
            // // 操作人信息
            // batch.setHandlerId(inbound.getHandlerId());
            // TODO: ProductionInbound实体中没有handlerName字段
            // batch.setHandlerName("系统操作"); // 原: inbound.getHandlerName()
            //
            // // 保存批次记录
            // inventoryBatchService.insertByBo(MapstructUtils.convert(batch, InventoryBatchBo.class));

            log.info("生成产品批次记录：入库单【{}】产品【{}】数量【{}】",
                inbound.getInboundCode(), item.getProductName(), item.getQuantity());
        } catch (Exception e) {
            log.error("生成产品批次记录失败：{}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 计算产品有效期
     *
     * @param productId 产品ID
     * @return 有效期时间
     */
    private LocalDateTime calculateExpiryTime(Long productId) {
        // ✅ 启用：根据产品类型和配置计算有效期
        try {
            // 获取产品信息
            // TODO: 添加productService依赖注入
            // ProductVo product = productService.queryById(productId);
            // if (product == null) {
            //     log.warn("产品ID【{}】不存在，使用默认有效期", productId);
            //     return LocalDateTime.now().plusYears(1);
            // }

            // 根据产品类型计算有效期
            // String productType = product.getProductType() != null ? product.getProductType().getValue() : null;
            // LocalDateTime expiryTime;

            // TODO: 临时返回默认有效期
            return LocalDateTime.now().plusYears(1);

            // switch (productType != null ? productType : "DEFAULT") {
            //     case "FOOD":
            //         // 食品类：6个月有效期
            //         expiryTime = LocalDateTime.now().plusMonths(6);
            //         break;
            //     case "MEDICINE":
            //         // 药品类：2年有效期
            //         expiryTime = LocalDateTime.now().plusYears(2);
            //         break;
            //     case "CHEMICAL":
            //         // 化工类：3年有效期
            //         expiryTime = LocalDateTime.now().plusYears(3);
            //         break;
            //     case "ELECTRONIC":
            //         // 电子类：5年有效期
            //         expiryTime = LocalDateTime.now().plusYears(5);
            //         break;
            //     default:
            //         // 默认：1年有效期
            //         expiryTime = LocalDateTime.now().plusYears(1);
            //         break;
            // }

            // log.debug("产品【{}】类型【{}】计算有效期：{}", product.getProductName(), productType, expiryTime);
            // return expiryTime;

        } catch (Exception e) {
            log.error("计算产品【{}】有效期失败：{}", productId, e.getMessage(), e);
            // 异常情况下使用默认有效期
            return LocalDateTime.now().plusYears(1);
        }
    }

    /**
     * 创建模拟入库明细数据
     * TODO: 实际应该从数据库获取
     *
     * @param inbound 入库单
     * @return 入库明细列表
     */
    private List<ProductionInboundItem> createMockInboundItems(ProductionInbound inbound) {
        List<ProductionInboundItem> items = new ArrayList<>();

        // 模拟一个入库明细
        ProductionInboundItem item = new ProductionInboundItem();
        item.setInboundId(inbound.getInboundId());
        item.setProductId(1L);
        item.setProductCode("PROD001");
        item.setProductName("生产产品001");
        item.setQuantity(new BigDecimal("100"));
        item.setLocationId(1L);
        item.setLocationCode("WH001");

        items.add(item);

        return items;
    }

    /**
     * 根据生产订单自动创建入库明细
     *
     * @param inbound 生产入库单
     * @param order   生产订单
     */
    private void createInboundItemsFromOrder(ProductionInbound inbound, ProductionOrderVo order) {
        try {
            // TODO: 实际项目中需要：
            // 1. 根据生产订单获取产品信息
            // 2. 创建对应的入库明细
            // 3. 设置入库数量、库位等信息
            //
            // 示例代码：
            // ProductionInboundItem item = new ProductionInboundItem();
            // item.setInboundId(inbound.getInboundId());
            // item.setProductId(order.getProductId());
            // item.setProductCode(order.getProductCode());
            // item.setProductName(order.getProductName());
            // item.setQuantity(order.getQuantity());
            // item.setLocationId(order.getLocationId());
            // itemService.insert(item);

            log.info("基于生产订单【{}】自动创建入库明细", order.getOrderCode());
        } catch (Exception e) {
            log.error("自动创建入库明细失败：{}", e.getMessage(), e);
            // 自动创建失败不影响主流程，只记录日志
        }
    }

    /**
     * 更新生产订单进度和状态
     *
     * @param inbound 生产入库单
     */
    private void updateProductionOrderProgress(ProductionInbound inbound) {
        try {
            if (inbound.getOrderId() == null) {
                log.warn("生产入库单【{}】未关联生产订单，跳过状态同步", inbound.getInboundCode());
                return;
            }

            // 获取生产订单信息
            ProductionOrderVo order = productionOrderService.queryById(inbound.getOrderId());
            if (order == null) {
                log.warn("生产入库单【{}】关联的生产订单不存在，跳过状态同步", inbound.getInboundCode());
                return;
            }

            // TODO: 获取入库明细的实际数量，当前简化为使用模拟数据
            // 实际应该从入库明细中汇总入库数量
            BigDecimal inboundQuantity = getInboundTotalQuantity(inbound);

            if (inboundQuantity.compareTo(BigDecimal.ZERO) > 0) {
                // 调用生产订单服务更新完工数量
                Boolean updateResult = productionOrderService.finishProduction(inbound.getOrderId(), inboundQuantity);

                if (updateResult) {
                    log.info("生产入库单【{}】同步更新生产订单【{}】完工数量【{}】成功",
                        inbound.getInboundCode(), order.getOrderCode(), inboundQuantity);
                } else {
                    log.warn("生产入库单【{}】同步更新生产订单【{}】完工数量失败",
                        inbound.getInboundCode(), order.getOrderCode());
                }
            }

        } catch (Exception e) {
            log.error("生产入库单【{}】同步更新生产订单状态失败：{}", inbound.getInboundCode(), e.getMessage(), e);
            // 状态同步失败不影响主流程，只记录日志
        }
    }

    /**
     * 获取入库单的总入库数量
     *
     * @param inbound 生产入库单
     * @return 总入库数量
     */
    private BigDecimal getInboundTotalQuantity(ProductionInbound inbound) {
        try {
            // ✅ 启用：从入库明细表中汇总数量
            if (inbound.getInboundId() == null) {
                log.warn("入库单ID为空，无法汇总数量");
                return BigDecimal.ZERO;
            }

            // 获取入库明细列表
            // TODO: 实现getInboundItems方法
            // List<ProductionInboundItem> items = getInboundItems(inbound.getInboundId());

            // 汇总入库数量
            // BigDecimal totalQuantity = items.stream()
            //     .filter(item -> item.getQuantity() != null)
            //     .map(ProductionInboundItem::getQuantity)
            //     .reduce(BigDecimal.ZERO, BigDecimal::add);

            // TODO: 临时返回0
            BigDecimal totalQuantity = BigDecimal.ZERO;

            log.debug("入库单【{}】汇总数量：{}", inbound.getInboundCode(), totalQuantity);
            return totalQuantity;
        } catch (Exception e) {
            log.error("获取入库单【{}】总数量失败：{}", inbound.getInboundCode(), e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 验证入库数量的合理性
     *
     * @param inbound 生产入库单
     */
    private void validateInboundQuantity(ProductionInbound inbound) {
        try {
            if (inbound.getOrderId() == null) {
                log.warn("生产入库单【{}】未关联生产订单，跳过数量核对", inbound.getInboundCode());
                return;
            }

            // 获取关联的生产订单
            ProductionOrderVo order = productionOrderService.queryById(inbound.getOrderId());
            if (order == null) {
                throw new ServiceException("生产入库单【" + inbound.getInboundCode() + "】关联的生产订单不存在");
            }

            // 获取入库单的总入库数量
            BigDecimal inboundTotalQuantity = getInboundTotalQuantity(inbound);

            // 获取生产订单的计划数量和已完工数量
            BigDecimal plannedQuantity = order.getQuantity() != null ? order.getQuantity() : BigDecimal.ZERO;
            BigDecimal finishedQuantity = order.getFinishQuantity() != null ? order.getFinishQuantity() : BigDecimal.ZERO;

            // 计算本次入库后的总完工数量
            BigDecimal newFinishQuantity = finishedQuantity.add(inboundTotalQuantity);

            // 1. 检查入库数量是否为正数
            if (inboundTotalQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("入库数量必须大于0");
            }

            // 2. 检查是否超过计划生产数量（允许5%的超产）
            BigDecimal allowedOverPercentage = BigDecimal.valueOf(5.0); // 5%
            BigDecimal maxAllowedQuantity = plannedQuantity.multiply(
                BigDecimal.ONE.add(allowedOverPercentage.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP))
            );

            if (newFinishQuantity.compareTo(maxAllowedQuantity) > 0) {
                BigDecimal overQuantity = newFinishQuantity.subtract(plannedQuantity);
                BigDecimal overPercentage = overQuantity.divide(plannedQuantity, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));

                throw new ServiceException(String.format(
                    "入库数量超产过多：计划数量[%s]，已完工[%s]，本次入库[%s]，超产比例[%s%%]，允许超产比例[%s%%]",
                    plannedQuantity, finishedQuantity, inboundTotalQuantity,
                    overPercentage.setScale(2, RoundingMode.HALF_UP), allowedOverPercentage
                ));
            }

            // 3. 检查数量的合理性（不能过小）
            BigDecimal minReasonableQuantity = plannedQuantity.multiply(BigDecimal.valueOf(0.01)); // 1%
            if (inboundTotalQuantity.compareTo(minReasonableQuantity) < 0) {
                log.warn("生产入库单【{}】入库数量[{}]过小，可能存在异常，计划数量[{}]",
                    inbound.getInboundCode(), inboundTotalQuantity, plannedQuantity);
            }

            // 4. 记录数量核对信息
            if (newFinishQuantity.compareTo(plannedQuantity) > 0) {
                BigDecimal overQuantity = newFinishQuantity.subtract(plannedQuantity);
                BigDecimal overPercentage = overQuantity.divide(plannedQuantity, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));

                log.warn("生产入库单【{}】存在超产：计划数量[{}]，完工数量[{}]，超产比例[{}%]",
                    inbound.getInboundCode(), plannedQuantity, newFinishQuantity,
                    overPercentage.setScale(2, RoundingMode.HALF_UP));
            }

            log.info("生产入库单【{}】数量核对通过：计划[{}]，已完工[{}]，本次入库[{}]，完工后总计[{}]",
                inbound.getInboundCode(), plannedQuantity, finishedQuantity, inboundTotalQuantity, newFinishQuantity);

        } catch (Exception e) {
            log.error("生产入库单【{}】数量核对失败：{}", inbound.getInboundCode(), e.getMessage(), e);
            throw new ServiceException("数量核对失败：" + e.getMessage());
        }
    }
}
