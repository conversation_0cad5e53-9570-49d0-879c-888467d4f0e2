package com.iotlaser.spms.mes.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.pro.domain.Product;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;

/**
 * 生产入库明细对象 mes_production_inbound_item
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mes_production_inbound_item")
public class ProductionInboundItem extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 明细ID
     */
    @TableId(value = "item_id")
    private Long itemId;

    /**
     * 入库单ID
     */
    private Long inboundId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 待完成数量
     */
    private BigDecimal quantity;

    /**
     * 已完成数量
     * 修正：从Long类型改为BigDecimal类型，保证数量计算精度
     */
    private BigDecimal finishQuantity;

    /**
     * 单价（含税）
     */
    private BigDecimal price;

    /**
     * 单价（不含税）
     */
    private BigDecimal priceExclusiveTax;

    /**
     * 金额（含税）
     */
    private BigDecimal amount;

    /**
     * 金额（不含税）
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


    /**
     * 产品信息表 (ONE_TO_ONE)
     */
    @TableField(exist = false)
    private Product product;


    /**
     * 生产入库批次明细表 (ONE_TO_MANY)
     */
    @TableField(exist = false)
    private List<ProductionInboundItemBatch> batches;


}
