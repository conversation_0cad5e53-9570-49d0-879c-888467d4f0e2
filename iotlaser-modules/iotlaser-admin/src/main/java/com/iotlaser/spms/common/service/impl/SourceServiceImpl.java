package com.iotlaser.spms.common.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.iotlaser.spms.common.domain.Source;
import com.iotlaser.spms.common.domain.SourceInfo;
import com.iotlaser.spms.common.domain.SourceItem;
import com.iotlaser.spms.common.domain.bo.SourceBo;
import com.iotlaser.spms.common.domain.bo.SourceItemBo;
import com.iotlaser.spms.common.domain.vo.SourceItemVo;
import com.iotlaser.spms.common.domain.vo.SourceVo;
import com.iotlaser.spms.common.mapper.SourceItemMapper;
import com.iotlaser.spms.common.mapper.SourceMapper;
import com.iotlaser.spms.common.service.ISourceService;
import com.iotlaser.spms.common.utils.SourceUtils;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@RequiredArgsConstructor
@Service
public class SourceServiceImpl implements ISourceService {

    private final SourceMapper baseMapper;
    private final SourceItemMapper itemMapper;

    /**
     * 查询产品明细表及其关联信息
     *
     * @param sourceType 查询分类
     * @param id         主键
     * @return 产品入库明细表
     */
    @Override
    public SourceVo queryByIdWith(String sourceType, Long id) {
        SourceInfo info = SourceUtils.getInfo(sourceType);
        return baseMapper.queryByIdWith(info, id);
    }

    /**
     * 分页查询产品明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品入库明细表分页列表
     */
    @Override
    public TableDataInfo<SourceVo> queryPageListWith(SourceBo bo, PageQuery pageQuery) {
        SourceInfo info = SourceUtils.getInfo(bo.getSourceType());
        QueryWrapper<Source> queryWrapper = buildQueryWrapperWith(info, bo);
        List<SourceVo> result = baseMapper.queryPageListWith(info, pageQuery.build(), queryWrapper);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<Source> buildQueryWrapperWith(SourceInfo info, SourceBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<Source> wrapper = Wrappers.query();
        wrapper.eq("main.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("main." + info.getId());
        wrapper.eq(bo.getId() != null, "main." + info.getId(), bo.getId());
        wrapper.eq(bo.getCode() != null, "main." + info.getCode(), bo.getCode());
        wrapper.eq(bo.getName() != null, "main." + info.getName(), bo.getName());
        wrapper.between(params.get("beginTime") != null && params.get("endTime") != null,
            "main." + info.getTime(), params.get("beginTime"), params.get("endTime"));
        return wrapper;
    }

    /**
     * 查询产品明细表及其关联信息
     *
     * @param sourceType 查询分类
     * @param itemId     主键
     * @return 产品入库明细表
     */
    @Override
    public SourceItemVo queryItemByIdWith(String sourceType, Long itemId) {
        SourceInfo info = SourceUtils.getInfo(sourceType);
        boolean isJoinBatch = StringUtils.isNotEmpty(info.getBatch());
        return itemMapper.queryItemByIdWith(sourceType, info.getMain(), info.getId(), info.getBatch(), isJoinBatch, itemId);
    }

    /**
     * 分页查询产品明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品入库明细表分页列表
     */
    @Override
    public TableDataInfo<SourceItemVo> queryItemPageListWith(SourceItemBo bo, PageQuery pageQuery) {
        SourceInfo info = SourceUtils.getInfo(bo.getSourceType());
        boolean isJoinBatch = StringUtils.isNotEmpty(info.getBatch());
        QueryWrapper<SourceItem> queryWrapper = buildQueryWrapperItemWith(info, bo);
        List<SourceItemVo> result = itemMapper.queryItemPageListWith(bo.getSourceType(), info.getMain(), info.getId(), info.getBatch(), isJoinBatch, pageQuery.build(), queryWrapper);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<SourceItem> buildQueryWrapperItemWith(SourceInfo info, SourceItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<SourceItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getMainId() != null, "item." + info.getId(), bo.getMainId());
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        wrapper.eq(bo.getLocationId() != null, "item.location_id", bo.getLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getLocationCode()), "item.location_code", bo.getLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getLocationName()), "item.location_name", bo.getLocationName());
        wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        wrapper.eq(bo.getFinishQuantity() != null, "item.finish_quantity", bo.getFinishQuantity());
        wrapper.eq(bo.getPrice() != null, "item.price", bo.getPrice());
        wrapper.notIn(ObjUtil.isNotNull(bo.getProduct()) && StringUtils.isNotBlank(bo.getProduct().getExcludeProductIds()), "item.product_id", StringUtils.splitTo(bo.getProduct().getExcludeProductIds(), Convert::toLong));
        return wrapper;
    }
}
