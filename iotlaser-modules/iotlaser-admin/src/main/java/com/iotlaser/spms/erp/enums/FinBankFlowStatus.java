package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 银行流水状态枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinBankFlowStatus implements IDictEnum<String> {

    IMPORTED("imported", "已导入", "银行流水已导入系统"),
    MATCHED("matched", "已匹配", "已匹配到业务单据"),
    PARTIALLY_MATCHED("partially_matched", "部分匹配", "部分金额已匹配"),
    UNMATCHED("unmatched", "未匹配", "未找到匹配的业务单据"),
    MANUAL_MATCHED("manual_matched", "手工匹配", "手工指定匹配关系"),
    RECONCILED("reconciled", "已对账", "已完成银行对账"),
    DISPUTED("disputed", "有争议", "存在争议需要处理"),
    IGNORED("ignored", "已忽略", "标记为忽略的流水"),
    DELETED("deleted", "已删除", "已删除的流水记录");

    public final static String DICT_CODE = "erp_fin_bank_flow_status";
    public final static String DICT_NAME = "银行流水状态";
    public final static String DICT_DESC = "管理银行流水的处理状态，从导入、匹配到对账完成的完整流程状态";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 银行流水状态枚举
     */
    public static FinBankFlowStatus getByValue(String value) {
        for (FinBankFlowStatus flowStatus : values()) {
            if (flowStatus.getValue().equals(value)) {
                return flowStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否已匹配
     *
     * @return 是否已匹配
     */
    public boolean isMatched() {
        return this == MATCHED || this == PARTIALLY_MATCHED || this == MANUAL_MATCHED;
    }

    /**
     * 判断是否可以匹配
     *
     * @return 是否可以匹配
     */
    public boolean canMatch() {
        return this == IMPORTED || this == UNMATCHED || this == PARTIALLY_MATCHED;
    }

    /**
     * 判断是否已完成处理
     *
     * @return 是否已完成处理
     */
    public boolean isCompleted() {
        return this == RECONCILED || this == IGNORED || this == DELETED;
    }

    /**
     * 判断是否需要人工处理
     *
     * @return 是否需要人工处理
     */
    public boolean requiresManualHandling() {
        return this == UNMATCHED || this == DISPUTED;
    }

    /**
     * 判断是否可以删除
     *
     * @return 是否可以删除
     */
    public boolean isDeletable() {
        return this == IMPORTED || this == UNMATCHED || this == IGNORED;
    }

    /**
     * 获取下一个可能的状态
     *
     * @return 下一个可能的状态列表
     */
    public FinBankFlowStatus[] getNextPossibleStates() {
        switch (this) {
            case IMPORTED:
                return new FinBankFlowStatus[]{MATCHED, PARTIALLY_MATCHED, UNMATCHED, IGNORED, DELETED};
            case UNMATCHED:
                return new FinBankFlowStatus[]{MATCHED, PARTIALLY_MATCHED, MANUAL_MATCHED, IGNORED, DELETED};
            case PARTIALLY_MATCHED:
                return new FinBankFlowStatus[]{MATCHED, MANUAL_MATCHED, DISPUTED};
            case MATCHED:
            case MANUAL_MATCHED:
                return new FinBankFlowStatus[]{RECONCILED, DISPUTED};
            case DISPUTED:
                return new FinBankFlowStatus[]{MATCHED, MANUAL_MATCHED, IGNORED};
            case RECONCILED:
            case IGNORED:
            case DELETED:
            default:
                return new FinBankFlowStatus[]{};
        }
    }
}
