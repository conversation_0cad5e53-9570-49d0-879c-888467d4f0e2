package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinStatementItem;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 对账单明细业务对象 erp_fin_statement_item
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinStatementItem.class, reverseConvertGenerate = false)
public class FinStatementItemBo extends BaseEntity {

    /**
     * 明细ID
     */
    @NotNull(message = "明细ID不能为空", groups = {EditGroup.class})
    private Long itemId;

    /**
     * 对账ID
     */
    @NotNull(message = "对账ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long statementId;

    /**
     * 组号
     */
    private String groupId;

    /**
     * 组内余额
     */
    private Long groupBalance;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 来源明细ID
     */
    private Long sourceItemId;

    /**
     * 借方金额 (应收增加)
     */
    private Long amountDebit;

    /**
     * 贷方金额 (应收减少)
     */
    private Long amountCredit;

    /**
     * 对账标记
     */
    private String markFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
