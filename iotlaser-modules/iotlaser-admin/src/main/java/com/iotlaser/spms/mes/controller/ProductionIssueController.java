package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.ProductionIssueBo;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueVo;
import com.iotlaser.spms.mes.service.IProductionIssueService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产领料
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/productionIssue")
public class ProductionIssueController extends BaseController {

    private final IProductionIssueService productionIssueService;

    /**
     * 查询生产领料列表
     */
    @SaCheckPermission("mes:productionIssue:list")
    @GetMapping("/list")
    public TableDataInfo<ProductionIssueVo> list(ProductionIssueBo bo, PageQuery pageQuery) {
        return productionIssueService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产领料列表
     */
    @SaCheckPermission("mes:productionIssue:export")
    @Log(title = "生产领料", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductionIssueBo bo, HttpServletResponse response) {
        List<ProductionIssueVo> list = productionIssueService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产领料", ProductionIssueVo.class, response);
    }

    /**
     * 获取生产领料详细信息
     *
     * @param issueId 主键
     */
    @SaCheckPermission("mes:productionIssue:query")
    @GetMapping("/{issueId}")
    public R<ProductionIssueVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long issueId) {
        return R.ok(productionIssueService.queryById(issueId));
    }

    /**
     * 新增生产领料
     */
    @SaCheckPermission("mes:productionIssue:add")
    @Log(title = "生产领料", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductionIssueBo bo) {
        return toAjax(productionIssueService.insertByBo(bo));
    }

    /**
     * 修改生产领料
     */
    @SaCheckPermission("mes:productionIssue:edit")
    @Log(title = "生产领料", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductionIssueBo bo) {
        return toAjax(productionIssueService.updateByBo(bo));
    }

    /**
     * 删除生产领料
     *
     * @param issueIds 主键串
     */
    @SaCheckPermission("mes:productionIssue:remove")
    @Log(title = "生产领料", businessType = BusinessType.DELETE)
    @DeleteMapping("/{issueIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] issueIds) {
        return toAjax(productionIssueService.deleteWithValidByIds(List.of(issueIds), true));
    }

    /**
     * 确认生产领料单
     *
     * @param issueId 领料单ID
     */
    @SaCheckPermission("mes:productionIssue:edit")
    @Log(title = "确认生产领料单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{issueId}")
    public R<Void> confirm(@NotNull(message = "领料单ID不能为空") @PathVariable Long issueId) {
        return toAjax(productionIssueService.confirmIssue(issueId));
    }

    /**
     * 批量确认生产领料单
     *
     * @param issueIds 领料单ID集合
     */
    @SaCheckPermission("mes:productionIssue:edit")
    @Log(title = "批量确认生产领料单", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "领料单ID不能为空") @RequestBody Long[] issueIds) {
        return toAjax(productionIssueService.batchConfirmIssues(List.of(issueIds)));
    }

    /**
     * 完成生产领料出库
     *
     * @param issueId 领料单ID
     */
    @SaCheckPermission("mes:productionIssue:edit")
    @Log(title = "完成生产领料出库", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{issueId}")
    public R<Void> complete(@NotNull(message = "领料单ID不能为空") @PathVariable Long issueId) {
        return toAjax(productionIssueService.completeIssue(issueId));
    }

    /**
     * 取消生产领料单
     *
     * @param issueId 领料单ID
     * @param reason  取消原因
     */
    @SaCheckPermission("mes:productionIssue:edit")
    @Log(title = "取消生产领料单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{issueId}")
    public R<Void> cancel(@NotNull(message = "领料单ID不能为空") @PathVariable Long issueId,
                          @RequestParam(required = false) String reason) {
        return toAjax(productionIssueService.cancelIssue(issueId, reason));
    }

    /**
     * 根据生产订单创建领料单
     *
     * @param productionOrderId 生产订单ID
     */
    @SaCheckPermission("mes:productionIssue:add")
    @Log(title = "从生产订单创建领料单", businessType = BusinessType.INSERT)
    @PostMapping("/createFromProductionOrder/{productionOrderId}")
    public R<ProductionIssueVo> createFromProductionOrder(@NotNull(message = "生产订单ID不能为空") @PathVariable Long productionOrderId) {
        return R.ok(productionIssueService.createFromProductionOrder(productionOrderId));
    }
}
