package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinAccount;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 账户业务对象 erp_fin_account
 *
 * <AUTHOR> Kai
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinAccount.class, reverseConvertGenerate = false)
public class FinAccountBo extends BaseEntity {

    /**
     * 账户ID
     */
    @NotNull(message = "账户ID不能为空", groups = {EditGroup.class})
    private Long accountId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 银行账号/支付账号
     */
    private String accountNumber;

    /**
     * 币种
     */
    private String currency;

    /**
     * 期初余额
     */
    @DecimalMin(value = "0.00", message = "期初余额不能小于0")
    private BigDecimal initialBalance;

    /**
     * 当前余额(由流水实时更新)
     */
    private BigDecimal currentBalance;

    /**
     * 账户状态
     */
    private String accountStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
