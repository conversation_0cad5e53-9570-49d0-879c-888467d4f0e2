package com.iotlaser.spms.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 财务对账结果视图对象
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Data
public class FinancialReconciliationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 销售订单ID
     */
    private Long orderId;

    /**
     * 销售订单编号
     */
    private String orderCode;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户编号
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 订单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate orderDate;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单不含税金额
     */
    private BigDecimal orderAmountExclusiveTax;

    /**
     * 订单税额
     */
    private BigDecimal orderTaxAmount;

    /**
     * 已开票金额
     */
    private BigDecimal invoicedAmount;

    /**
     * 已收款金额
     */
    private BigDecimal receivedAmount;

    /**
     * 未开票金额
     */
    private BigDecimal uninvoicedAmount;

    /**
     * 未收款金额
     */
    private BigDecimal unreceivedAmount;

    /**
     * 对账状态
     */
    private ReconciliationStatus reconciliationStatus;

    /**
     * 差异金额
     */
    private BigDecimal differenceAmount;

    /**
     * 差异说明
     */
    private String differenceRemark;

    /**
     * 对账时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reconciliationTime;

    /**
     * 应收发票列表
     */
    private List<FinArReceivableVo> receivables;

    /**
     * 收款单列表
     */
    private List<FinArReceiptOrderVo> receipts;

    /**
     * 核销记录列表
     */
    private List<FinArReceiptReceivableLinkVo> applyRecords;

    /**
     * 对账状态枚举
     */
    public enum ReconciliationStatus {
        /**
         * 对账一致
         */
        MATCHED("对账一致"),

        /**
         * 存在差异
         */
        DIFFERENCE("存在差异"),

        /**
         * 部分对账
         */
        PARTIAL("部分对账"),

        /**
         * 未对账
         */
        UNRECONCILED("未对账");

        private final String description;

        ReconciliationStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 对账差异详情
     */
    @Data
    public static class ReconciliationDifference {
        /**
         * 差异类型
         */
        private String differenceType;

        /**
         * 差异金额
         */
        private BigDecimal differenceAmount;

        /**
         * 差异说明
         */
        private String differenceRemark;
    }
}
