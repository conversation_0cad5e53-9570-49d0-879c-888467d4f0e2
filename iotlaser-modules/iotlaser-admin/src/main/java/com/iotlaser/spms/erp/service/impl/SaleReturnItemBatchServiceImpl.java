package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.SaleReturnItemBatch;
import com.iotlaser.spms.erp.domain.bo.SaleReturnItemBatchBo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnItemBatchVo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import com.iotlaser.spms.erp.enums.SaleReturnStatus;
import com.iotlaser.spms.erp.mapper.SaleReturnItemBatchMapper;
import com.iotlaser.spms.erp.service.ISaleReturnItemBatchService;
import com.iotlaser.spms.erp.service.ISaleReturnService;
import com.iotlaser.spms.wms.service.IInventoryBatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 销售退货批次明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleReturnItemBatchServiceImpl implements ISaleReturnItemBatchService {

    private final SaleReturnItemBatchMapper baseMapper;
    @Lazy
    @Autowired
    private ISaleReturnService saleReturnService;
    private final IInventoryBatchService inventoryBatchService;

    /**
     * 查询销售退货批次明细
     *
     * @param batchId 主键
     * @return 销售退货批次明细
     */
    @Override
    public SaleReturnItemBatchVo queryById(Long batchId) {
        return baseMapper.selectVoById(batchId);
    }

    /**
     * 分页查询销售退货批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售退货批次明细分页列表
     */
    @Override
    public TableDataInfo<SaleReturnItemBatchVo> queryPageList(SaleReturnItemBatchBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleReturnItemBatch> lqw = buildQueryWrapper(bo);
        Page<SaleReturnItemBatchVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售退货批次明细列表
     *
     * @param bo 查询条件
     * @return 销售退货批次明细列表
     */
    @Override
    public List<SaleReturnItemBatchVo> queryList(SaleReturnItemBatchBo bo) {
        LambdaQueryWrapper<SaleReturnItemBatch> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleReturnItemBatch> buildQueryWrapper(SaleReturnItemBatchBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleReturnItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleReturnItemBatch::getBatchId);
        lqw.eq(bo.getItemId() != null, SaleReturnItemBatch::getItemId, bo.getItemId());
        lqw.eq(bo.getReturnId() != null, SaleReturnItemBatch::getReturnId, bo.getReturnId());
        lqw.eq(bo.getInventoryBatchId() != null, SaleReturnItemBatch::getInventoryBatchId, bo.getInventoryBatchId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), SaleReturnItemBatch::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), SaleReturnItemBatch::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), SaleReturnItemBatch::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getProductId() != null, SaleReturnItemBatch::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), SaleReturnItemBatch::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), SaleReturnItemBatch::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, SaleReturnItemBatch::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), SaleReturnItemBatch::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), SaleReturnItemBatch::getUnitName, bo.getUnitName());
        // ✅ 优化：移除数量和价格的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, SaleReturnItemBatch::getQuantity, bo.getQuantity());
        // 原代码：lqw.eq(bo.getPrice() != null, SaleReturnItemBatch::getPrice, bo.getPrice());
        // TODO: 如需要可以后续添加数量和价格的范围查询支持
        lqw.eq(bo.getLocationId() != null, SaleReturnItemBatch::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), SaleReturnItemBatch::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), SaleReturnItemBatch::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleReturnItemBatch::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增销售退货批次明细
     *
     * @param bo 销售退货批次明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SaleReturnItemBatchBo bo) {
        SaleReturnItemBatch add = MapstructUtils.convert(bo, SaleReturnItemBatch.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBatchId(add.getBatchId());
        }
        return flag;
    }

    /**
     * 修改销售退货批次明细
     *
     * @param bo 销售退货批次明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SaleReturnItemBatchBo bo) {
        SaleReturnItemBatch update = MapstructUtils.convert(bo, SaleReturnItemBatch.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleReturnItemBatch entity) {
        // 校验必填字段
        if (entity.getItemId() == null) {
            throw new ServiceException("退货明细不能为空");
        }
        if (entity.getInventoryBatchId() == null) {
            throw new ServiceException("库存批次不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("退货数量必须大于0");
        }
        if (StringUtils.isBlank(entity.getInternalBatchNumber())) {
            throw new ServiceException("内部批次号不能为空");
        }
    }

    /**
     * 校验并批量删除销售退货批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验批次明细是否可以删除
            List<SaleReturnItemBatch> batches = baseMapper.selectByIds(ids);
            for (SaleReturnItemBatch batch : batches) {
                // 1. 检查主表状态，只有草稿状态的退货批次才能删除
                SaleReturnVo saleReturn = saleReturnService.queryById(batch.getReturnId());
                if (saleReturn == null) {
                    throw new ServiceException("销售退货批次关联的退货单不存在，批次号：" + batch.getInternalBatchNumber());
                }
                if (!SaleReturnStatus.DRAFT.getValue().equals(saleReturn.getReturnStatus())) {
                    throw new ServiceException("销售退货批次所属退货单【" + saleReturn.getReturnName() +
                        "】状态为【" + saleReturn.getReturnStatus() + "】，不允许删除批次");
                }

                // 2. 检查是否已关联库存记录
                // 通过内部批次号查询是否已生成库存批次
                if (StringUtils.isNotBlank(batch.getInternalBatchNumber())) {
                    // TODO: 添加queryByInternalBatchNumber方法
                    // InventoryBatchVo inventoryBatch = inventoryBatchService.queryByInternalBatchNumber(batch.getInternalBatchNumber());
                    // if (inventoryBatch != null) {
                    //     // 检查库存批次状态，如果已锁定或其他状态，不允许删除
                    //     log.info("检查销售退货批次关联的库存状态 - 批次号: {}, 库存状态: {}",
                    //         batch.getInternalBatchNumber(), inventoryBatch.getInventoryStatus());
                    //
                    //     // TODO: 根据库存批次状态进行更详细的校验
                    //     // 当前简化处理：如果已关联库存记录，则不允许删除
                    //     throw new ServiceException("销售退货批次【" + batch.getInternalBatchNumber() +
                    //         "】已关联库存记录，不允许删除");
                    // }
                }

                log.info("删除销售退货批次明细校验通过，批次号：{}", batch.getInternalBatchNumber());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除销售退货批次成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除销售退货批次失败：{}", e.getMessage(), e);
            throw new ServiceException("删除销售退货批次失败：" + e.getMessage());
        }
    }
}
