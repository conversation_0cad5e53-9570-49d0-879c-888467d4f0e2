package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.wms.domain.InventoryCheckItem;
import com.iotlaser.spms.wms.domain.bo.InventoryCheckItemBo;
import com.iotlaser.spms.wms.domain.vo.InventoryCheckItemVo;
import com.iotlaser.spms.wms.mapper.InventoryCheckItemMapper;
import com.iotlaser.spms.wms.service.IInventoryCheckItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 库存盘点明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InventoryCheckItemServiceImpl implements IInventoryCheckItemService {

    private final InventoryCheckItemMapper baseMapper;

    /**
     * 查询库存盘点明细
     *
     * @param itemId 主键
     * @return 库存盘点明细
     */
    @Override
    public InventoryCheckItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询库存盘点明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 库存盘点明细分页列表
     */
    @Override
    public TableDataInfo<InventoryCheckItemVo> queryPageList(InventoryCheckItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InventoryCheckItem> lqw = buildQueryWrapper(bo);
        Page<InventoryCheckItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的库存盘点明细列表
     *
     * @param bo 查询条件
     * @return 库存盘点明细列表
     */
    @Override
    public List<InventoryCheckItemVo> queryList(InventoryCheckItemBo bo) {
        LambdaQueryWrapper<InventoryCheckItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InventoryCheckItem> buildQueryWrapper(InventoryCheckItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InventoryCheckItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(InventoryCheckItem::getItemId);
        lqw.eq(bo.getCheckId() != null, InventoryCheckItem::getCheckId, bo.getCheckId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), InventoryCheckItem::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), InventoryCheckItem::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), InventoryCheckItem::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getProductId() != null, InventoryCheckItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), InventoryCheckItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), InventoryCheckItem::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, InventoryCheckItem::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), InventoryCheckItem::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), InventoryCheckItem::getUnitName, bo.getUnitName());
        lqw.eq(bo.getLocationId() != null, InventoryCheckItem::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), InventoryCheckItem::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), InventoryCheckItem::getLocationName, bo.getLocationName());
        lqw.eq(bo.getBookQuantity() != null, InventoryCheckItem::getBookQuantity, bo.getBookQuantity());
        lqw.eq(bo.getActualQuantity() != null, InventoryCheckItem::getActualQuantity, bo.getActualQuantity());
        lqw.eq(bo.getDifferenceQuantity() != null, InventoryCheckItem::getDifferenceQuantity, bo.getDifferenceQuantity());
        lqw.eq(bo.getPrice() != null, InventoryCheckItem::getPrice, bo.getPrice());
        lqw.eq(bo.getDifferenceAmount() != null, InventoryCheckItem::getDifferenceAmount, bo.getDifferenceAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getItemStatus()), InventoryCheckItem::getItemStatus, bo.getItemStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), InventoryCheckItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增库存盘点明细
     *
     * @param bo 库存盘点明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(InventoryCheckItemBo bo) {
        InventoryCheckItem add = MapstructUtils.convert(bo, InventoryCheckItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改库存盘点明细
     *
     * @param bo 库存盘点明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(InventoryCheckItemBo bo) {
        InventoryCheckItem update = MapstructUtils.convert(bo, InventoryCheckItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InventoryCheckItem entity) {
        // 校验必填字段
        if (entity.getCheckId() == null) {
            throw new ServiceException("盘点单不能为空");
        }
        if (entity.getProductId() == null) {
            throw new ServiceException("产品不能为空");
        }
        if (entity.getLocationId() == null) {
            throw new ServiceException("库位不能为空");
        }
        if (entity.getBookQuantity() == null || entity.getBookQuantity().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("账面数量不能为负数");
        }
        if (entity.getActualQuantity() == null || entity.getActualQuantity().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("实盘数量不能为负数");
        }

        // 计算盘点差异
        BigDecimal difference = entity.getActualQuantity().subtract(entity.getBookQuantity());
        entity.setDifferenceQuantity(difference);

        // 校验同一盘点单中产品+库位不能重复
        if (entity.getCheckId() != null && entity.getProductId() != null && entity.getLocationId() != null) {
            LambdaQueryWrapper<InventoryCheckItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(InventoryCheckItem::getCheckId, entity.getCheckId());
            wrapper.eq(InventoryCheckItem::getProductId, entity.getProductId());
            wrapper.eq(InventoryCheckItem::getLocationId, entity.getLocationId());
            if (entity.getItemId() != null) {
                wrapper.ne(InventoryCheckItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一盘点单中不能重复添加相同产品和库位的组合");
            }
        }
    }

    /**
     * 校验并批量删除库存盘点明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验明细是否可以删除
            List<InventoryCheckItem> items = baseMapper.selectByIds(ids);
            for (InventoryCheckItem item : items) {
                // 检查关联的盘点单状态
                // 检查关联的盘点单状态
                log.info("删除盘点明细，产品：{}，库位：{}", item.getProductName(), item.getLocationName());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据盘点单ID查询明细ID列表
     *
     * @param checkId 盘点单ID
     * @return 明细ID列表
     */
    @Override
    public List<Long> selectItemIdsByCheckId(Long checkId) {
        LambdaQueryWrapper<InventoryCheckItem> wrapper = Wrappers.lambdaQuery();
        wrapper.select(InventoryCheckItem::getItemId);
        wrapper.eq(InventoryCheckItem::getCheckId, checkId);
        return baseMapper.selectList(wrapper).stream()
            .map(InventoryCheckItem::getItemId)
            .collect(Collectors.toList());
    }

    /**
     * 批量插入或更新库存盘点明细
     *
     * @param items 明细BO集合
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<InventoryCheckItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }

        try {
            List<InventoryCheckItem> entities = MapstructUtils.convert(items, InventoryCheckItem.class);

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新库存盘点明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新库存盘点明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID集合删除库存盘点明细
     *
     * @param ids ID集合
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除库存盘点明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除库存盘点明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除失败：" + e.getMessage());
        }
    }


    /**
     * 根据盘点单ID查询明细列表
     * ✅ 修正：返回VO而非Entity
     *
     * @param checkId 盘点单ID
     * @return 明细VO列表
     */
    @Override
    public List<InventoryCheckItemVo> selectListByCheckId(Long checkId) {
        LambdaQueryWrapper<InventoryCheckItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(InventoryCheckItem::getCheckId, checkId);
        return baseMapper.selectVoList(wrapper);
    }
}
