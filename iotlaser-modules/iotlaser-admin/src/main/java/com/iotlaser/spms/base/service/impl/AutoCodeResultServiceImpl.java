package com.iotlaser.spms.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.AutoCodeResult;
import com.iotlaser.spms.base.domain.bo.AutoCodeResultBo;
import com.iotlaser.spms.base.domain.vo.AutoCodeResultVo;
import com.iotlaser.spms.base.enums.CycleMethod;
import com.iotlaser.spms.base.mapper.AutoCodeResultMapper;
import com.iotlaser.spms.base.service.IAutoCodeResultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Service
public class AutoCodeResultServiceImpl implements IAutoCodeResultService {

    private final AutoCodeResultMapper baseMapper;

    @Override
    public AutoCodeResultVo queryById(Long codeId) {
        return baseMapper.selectVoById(codeId);
    }

    @Override
    public TableDataInfo<AutoCodeResultVo> queryPageList(AutoCodeResultBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AutoCodeResult> lqw = buildQueryWrapper(bo, null);
        Page<AutoCodeResultVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<AutoCodeResultVo> queryList(AutoCodeResultBo bo) {
        LambdaQueryWrapper<AutoCodeResult> lqw = buildQueryWrapper(bo, null);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public AutoCodeResultVo findLastResult(AutoCodeResultBo bo, CycleMethod method) {
        LambdaQueryWrapper<AutoCodeResult> lqw = buildQueryWrapper(bo, method);
        lqw.last("LIMIT 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public AutoCodeResultVo findLastResult(AutoCodeResultBo bo) {
        return findLastResult(bo, null);
    }

    /**
     * 构建查询条件封装对象
     *
     * @param bo     自动代码结果的业务对象，包含查询条件
     * @param method 循环方法代码，用于确定查询逻辑
     * @return LambdaQueryWrapper<AutoCodeResult> 查询条件封装对象
     */
    private LambdaQueryWrapper<AutoCodeResult> buildQueryWrapper(AutoCodeResultBo bo, CycleMethod method) {
        // 获取查询参数
        Map<String, Object> params = bo.getParams();
        // 创建LambdaQueryWrapper对象
        LambdaQueryWrapper<AutoCodeResult> lqw = Wrappers.lambdaQuery();

        lqw.orderByDesc(AutoCodeResult::getCreateTime);
        // 根据规则ID构建查询条件
        lqw.eq(bo.getRuleId() != null, AutoCodeResult::getRuleId, bo.getRuleId());
        // 根据循环方法代码构建查询条件
        if (method != null) {
            if (CycleMethod.OTHER == method) {
                // 当循环方法为其他时，根据最后输入字符构建查询条件
                lqw.eq(StringUtils.isNotBlank(bo.getLastInputChar()), AutoCodeResult::getLastInputChar, bo.getLastInputChar());
            } else if (StringUtils.isNotBlank(bo.getGenDate())) {
                // 根据生成日期构建查询条件
                // 当循环方法不为其他时，使用生成日期的右模糊查询
                lqw.likeRight(AutoCodeResult::getGenDate, bo.getGenDate());
            }
        } else {
            // 当循环方法代码为空时，根据生成日期和最后输入字符构建查询条件
            lqw.eq(StringUtils.isNotBlank(bo.getGenDate()), AutoCodeResult::getGenDate, bo.getGenDate());
            lqw.eq(StringUtils.isNotBlank(bo.getLastInputChar()), AutoCodeResult::getLastInputChar, bo.getLastInputChar());
        }
        // 根据生成索引构建查询条件
        lqw.eq(bo.getGenIndex() != null, AutoCodeResult::getGenIndex, bo.getGenIndex());
        // 根据最后结果构建查询条件
        lqw.eq(StringUtils.isNotBlank(bo.getLastResult()), AutoCodeResult::getLastResult, bo.getLastResult());
        // 根据最后序列号构建查询条件
        lqw.eq(bo.getLastSerialNo() != null, AutoCodeResult::getLastSerialNo, bo.getLastSerialNo());
        // 返回查询条件封装对象
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(AutoCodeResultBo bo) {
        try {
            AutoCodeResult add = MapstructUtils.convert(bo, AutoCodeResult.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增编码生成记录失败");
            }

            bo.setCodeId(add.getCodeId());
            log.info("新增编码生成记录成功：规则ID【{}】最后结果【{}】", add.getRuleId(), add.getLastResult());
            return true;
        } catch (Exception e) {
            log.error("新增编码生成记录失败：{}", e.getMessage(), e);
            throw new ServiceException("新增编码生成记录失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(AutoCodeResultBo bo) {
        try {
            AutoCodeResult update = MapstructUtils.convert(bo, AutoCodeResult.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改编码生成记录失败：记录不存在或数据未变更");
            }

            log.info("修改编码生成记录成功：规则ID【{}】最后结果【{}】", update.getRuleId(), update.getLastResult());
            return true;
        } catch (Exception e) {
            log.error("修改编码生成记录失败：{}", e.getMessage(), e);
            throw new ServiceException("修改编码生成记录失败：" + e.getMessage());
        }
    }

    private void validEntityBeforeSave(AutoCodeResult entity) {
        // 校验必填字段
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验编码生成记录是否可以删除
            List<AutoCodeResult> results = baseMapper.selectByIds(ids);
            for (AutoCodeResult result : results) {
                log.info("删除编码生成记录校验：规则ID【{}】最后结果【{}】", result.getRuleId(), result.getLastResult());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除编码生成记录成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除编码生成记录失败：{}", e.getMessage(), e);
            throw new ServiceException("删除编码生成记录失败：" + e.getMessage());
        }
    }
}
