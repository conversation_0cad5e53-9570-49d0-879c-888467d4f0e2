package com.iotlaser.spms.common.utils;

import org.dromara.common.core.exception.ServiceException;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 价税分离计算工具类
 * <p>
 * 提供统一的价税分离计算方法，确保跨模块计算逻辑的一致性
 *
 * <AUTHOR>
 * @date 2025/06/19
 */
public class TaxCalculationUtils {

    /**
     * 税率精度：小数点后4位
     */
    private static final int TAX_RATE_SCALE = 4;

    /**
     * 金额精度：小数点后2位
     */
    private static final int AMOUNT_SCALE = 2;

    /**
     * 舍入模式：四舍五入
     */
    private static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 智能计算价税分离字段
     *
     * @param quantity           数量
     * @param taxRate            税率 (%)
     * @param price              单价（含税） (可选)
     * @param priceExclusiveTax  单价（不含税） (可选)
     * @param amount             金额（含税） (可选)
     * @param amountExclusiveTax 金额（不含税） (可选)
     * @return 计算结果
     */
    public static TaxCalculationResult calculate(BigDecimal quantity, BigDecimal taxRate,
                                                 BigDecimal price, BigDecimal priceExclusiveTax,
                                                 BigDecimal amount, BigDecimal amountExclusiveTax) {
        // 校验必要字段
        validateInputs(quantity, taxRate);

        TaxCalculationResult result = new TaxCalculationResult();
        result.setQuantity(quantity);
        result.setTaxRate(taxRate);

        // 根据输入的字段计算其他字段
        if (priceExclusiveTax != null) {
            // 基于单价（不含税）计算
            calculateFromPriceExclusiveTax(result, priceExclusiveTax);
        } else if (price != null) {
            // 基于单价（含税）计算
            calculateFromPrice(result, price);
        } else if (amountExclusiveTax != null) {
            // 基于金额（不含税）计算
            calculateFromAmountExclusiveTax(result, amountExclusiveTax);
        } else if (amount != null) {
            // 基于金额（含税）计算
            calculateFromTotalAmount(result, amount);
        } else {
            throw new ServiceException("必须提供单价或金额信息");
        }

        return result;
    }

    /**
     * 基于单价（不含税）计算其他字段
     */
    private static void calculateFromPriceExclusiveTax(TaxCalculationResult result, BigDecimal priceExclusiveTax) {
        result.setPriceExclusiveTax(priceExclusiveTax);

        // 计算金额（不含税） = 单价（不含税） × 数量
        BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(result.getQuantity())
            .setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setAmountExclusiveTax(amountExclusiveTax);

        // 计算税额 = 金额（不含税） × 税率
        BigDecimal taxAmount = amountExclusiveTax.multiply(
                result.getTaxRate().divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE))
            .setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setTaxAmount(taxAmount);

        // 计算单价（含税） = 单价（不含税） × (1 + 税率)
        BigDecimal price = priceExclusiveTax.multiply(
                BigDecimal.ONE.add(result.getTaxRate().divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE)))
            .setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setPrice(price);

        // 计算金额（含税） = 金额（不含税） + 税额
        BigDecimal amount = amountExclusiveTax.add(taxAmount);
        result.setAmount(amount);
    }

    /**
     * 基于单价（含税）计算其他字段
     */
    private static void calculateFromPrice(TaxCalculationResult result, BigDecimal price) {
        result.setPrice(price);

        // 计算金额（含税） = 单价（含税） × 数量
        BigDecimal amount = price.multiply(result.getQuantity())
            .setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setAmount(amount);

        // 计算单价（不含税） = 单价（含税） ÷ (1 + 税率)
        BigDecimal priceExclusiveTax = price.divide(
            BigDecimal.ONE.add(result.getTaxRate().divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE)),
            AMOUNT_SCALE, ROUNDING_MODE);
        result.setPriceExclusiveTax(priceExclusiveTax);

        // 计算金额（不含税） = 单价（不含税） × 数量
        BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(result.getQuantity())
            .setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setAmountExclusiveTax(amountExclusiveTax);

        // 计算税额 = 金额（含税） - 金额（不含税）
        BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        result.setTaxAmount(taxAmount);
    }

    /**
     * 基于金额（不含税）计算其他字段
     */
    private static void calculateFromAmountExclusiveTax(TaxCalculationResult result, BigDecimal amountExclusiveTax) {
        result.setAmountExclusiveTax(amountExclusiveTax);

        // 计算单价（不含税） = 金额（不含税） ÷ 数量
        BigDecimal priceExclusiveTax = amountExclusiveTax.divide(result.getQuantity(), AMOUNT_SCALE, ROUNDING_MODE);
        result.setPriceExclusiveTax(priceExclusiveTax);

        // 计算税额 = 金额（不含税） × 税率
        BigDecimal taxAmount = amountExclusiveTax.multiply(
                result.getTaxRate().divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE))
            .setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setTaxAmount(taxAmount);

        // 计算金额（含税） = 金额（不含税） + 税额
        BigDecimal amount = amountExclusiveTax.add(taxAmount);
        result.setAmount(amount);

        // 计算单价（含税） = 金额（含税） ÷ 数量
        BigDecimal price = amount.divide(result.getQuantity(), AMOUNT_SCALE, ROUNDING_MODE);
        result.setPrice(price);
    }

    /**
     * 基于金额（含税）计算其他字段
     */
    private static void calculateFromTotalAmount(TaxCalculationResult result, BigDecimal amount) {
        result.setAmount(amount);

        // 计算单价（含税） = 金额（含税） ÷ 数量
        BigDecimal price = amount.divide(result.getQuantity(), AMOUNT_SCALE, ROUNDING_MODE);
        result.setPrice(price);

        // 计算金额（不含税） = 金额（含税） ÷ (1 + 税率)
        BigDecimal amountExclusiveTax = amount.divide(
            BigDecimal.ONE.add(result.getTaxRate().divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE)),
            AMOUNT_SCALE, ROUNDING_MODE);
        result.setAmountExclusiveTax(amountExclusiveTax);

        // 计算单价（不含税） = 金额（不含税） ÷ 数量
        BigDecimal priceExclusiveTax = amountExclusiveTax.divide(result.getQuantity(), AMOUNT_SCALE, ROUNDING_MODE);
        result.setPriceExclusiveTax(priceExclusiveTax);

        // 计算税额 = 金额（含税） - 金额（不含税）
        BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        result.setTaxAmount(taxAmount);
    }

    /**
     * 校验输入参数
     */
    private static void validateInputs(BigDecimal quantity, BigDecimal taxRate) {
        if (quantity == null || quantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("数量必须大于0");
        }

        if (taxRate == null) {
            throw new ServiceException("税率不能为空");
        }

        // 校验税率范围 (0-100%)
        if (taxRate.compareTo(BigDecimal.ZERO) < 0 || taxRate.compareTo(new BigDecimal("100")) > 0) {
            throw new ServiceException("税率必须在0-100%之间");
        }
    }

    /**
     * 计算税额
     *
     * @param amountExclusiveTax 金额（不含税）
     * @param taxRate            税率 (%)
     * @return 税额
     */
    public static BigDecimal calculateTaxAmount(BigDecimal amountExclusiveTax, BigDecimal taxRate) {
        if (amountExclusiveTax == null || taxRate == null) {
            return BigDecimal.ZERO;
        }
        return amountExclusiveTax.multiply(taxRate.divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE))
            .setScale(AMOUNT_SCALE, ROUNDING_MODE);
    }

    /**
     * 计算金额（含税）
     *
     * @param amountExclusiveTax 金额（不含税）
     * @param taxAmount          税额
     * @return 金额（含税）
     */
    public static BigDecimal calculateTotalAmount(BigDecimal amountExclusiveTax, BigDecimal taxAmount) {
        if (amountExclusiveTax == null) {
            amountExclusiveTax = BigDecimal.ZERO;
        }
        if (taxAmount == null) {
            taxAmount = BigDecimal.ZERO;
        }
        return amountExclusiveTax.add(taxAmount);
    }

    /**
     * 计算金额（不含税）
     *
     * @param amount  金额（含税）
     * @param taxRate 税率 (%)
     * @return 金额（不含税）
     */
    public static BigDecimal calculateAmountExclusiveTax(BigDecimal amount, BigDecimal taxRate) {
        if (amount == null || taxRate == null) {
            return BigDecimal.ZERO;
        }
        return amount.divide(
            BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE)),
            AMOUNT_SCALE, ROUNDING_MODE);
    }

    /**
     * 价税分离计算结果
     */
    public static class TaxCalculationResult {
        private BigDecimal quantity;
        private BigDecimal price;                // 单价（含税）
        private BigDecimal priceExclusiveTax;    // 单价（不含税）
        private BigDecimal amount;          // 金额（含税）
        private BigDecimal amountExclusiveTax;   // 金额（不含税）
        private BigDecimal taxRate;              // 税率
        private BigDecimal taxAmount;            // 税额

        // Getters and Setters
        public BigDecimal getQuantity() {
            return quantity;
        }

        public void setQuantity(BigDecimal quantity) {
            this.quantity = quantity;
        }

        public BigDecimal getPrice() {
            return price;
        }

        public void setPrice(BigDecimal price) {
            this.price = price;
        }

        public BigDecimal getPriceExclusiveTax() {
            return priceExclusiveTax;
        }

        public void setPriceExclusiveTax(BigDecimal priceExclusiveTax) {
            this.priceExclusiveTax = priceExclusiveTax;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public BigDecimal getAmountExclusiveTax() {
            return amountExclusiveTax;
        }

        public void setAmountExclusiveTax(BigDecimal amountExclusiveTax) {
            this.amountExclusiveTax = amountExclusiveTax;
        }

        public BigDecimal getTaxRate() {
            return taxRate;
        }

        public void setTaxRate(BigDecimal taxRate) {
            this.taxRate = taxRate;
        }

        public BigDecimal getTaxAmount() {
            return taxAmount;
        }

        public void setTaxAmount(BigDecimal taxAmount) {
            this.taxAmount = taxAmount;
        }
    }
}
