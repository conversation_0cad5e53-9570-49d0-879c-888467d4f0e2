package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.SaleOrderItemBatch;
import com.iotlaser.spms.erp.domain.bo.SaleOrderItemBatchBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderItemBatchVo;
import com.iotlaser.spms.erp.mapper.SaleOrderItemBatchMapper;
import com.iotlaser.spms.erp.service.ISaleOrderItemBatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 销售订单批次明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleOrderItemBatchServiceImpl implements ISaleOrderItemBatchService {

    private final SaleOrderItemBatchMapper baseMapper;

    /**
     * 查询销售订单批次明细
     *
     * @param batchId 主键
     * @return 销售订单批次明细
     */
    @Override
    public SaleOrderItemBatchVo queryById(Long batchId) {
        return baseMapper.selectVoById(batchId);
    }

    /**
     * 分页查询销售订单批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售订单批次明细分页列表
     */
    @Override
    public TableDataInfo<SaleOrderItemBatchVo> queryPageList(SaleOrderItemBatchBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleOrderItemBatch> lqw = buildQueryWrapper(bo);
        Page<SaleOrderItemBatchVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售订单批次明细列表
     *
     * @param bo 查询条件
     * @return 销售订单批次明细列表
     */
    @Override
    public List<SaleOrderItemBatchVo> queryList(SaleOrderItemBatchBo bo) {
        LambdaQueryWrapper<SaleOrderItemBatch> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleOrderItemBatch> buildQueryWrapper(SaleOrderItemBatchBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleOrderItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleOrderItemBatch::getBatchId);
        lqw.eq(bo.getItemId() != null, SaleOrderItemBatch::getItemId, bo.getItemId());
        lqw.eq(bo.getOrderId() != null, SaleOrderItemBatch::getOrderId, bo.getOrderId());
        lqw.eq(bo.getInventoryBatchId() != null, SaleOrderItemBatch::getInventoryBatchId, bo.getInventoryBatchId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), SaleOrderItemBatch::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), SaleOrderItemBatch::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), SaleOrderItemBatch::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getProductId() != null, SaleOrderItemBatch::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), SaleOrderItemBatch::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), SaleOrderItemBatch::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, SaleOrderItemBatch::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), SaleOrderItemBatch::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), SaleOrderItemBatch::getUnitName, bo.getUnitName());
        // ✅ 优化：移除数量和价格的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, SaleOrderItemBatch::getQuantity, bo.getQuantity());
        // 原代码：lqw.eq(bo.getPrice() != null, SaleOrderItemBatch::getPrice, bo.getPrice());
        // TODO: 如需要可以后续添加数量和价格的范围查询支持
        lqw.eq(bo.getLocationId() != null, SaleOrderItemBatch::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), SaleOrderItemBatch::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), SaleOrderItemBatch::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleOrderItemBatch::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增销售订单批次明细
     *
     * @param bo 销售订单批次明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SaleOrderItemBatchBo bo) {
        SaleOrderItemBatch add = MapstructUtils.convert(bo, SaleOrderItemBatch.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBatchId(add.getBatchId());
        }
        return flag;
    }

    /**
     * 修改销售订单批次明细
     *
     * @param bo 销售订单批次明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SaleOrderItemBatchBo bo) {
        SaleOrderItemBatch update = MapstructUtils.convert(bo, SaleOrderItemBatch.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleOrderItemBatch entity) {
        // 校验必填字段
        if (entity.getItemId() == null) {
            throw new ServiceException("订单明细不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("批次数量必须大于0");
        }
        if (StringUtils.isBlank(entity.getInternalBatchNumber())) {
            throw new ServiceException("内部批次号不能为空");
        }
        if (entity.getLocationId() == null) {
            throw new ServiceException("库位不能为空");
        }
    }

    /**
     * 校验并批量删除销售订单批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验批次明细是否可以删除
            List<SaleOrderItemBatch> batches = baseMapper.selectByIds(ids);
            for (SaleOrderItemBatch batch : batches) {
                log.info("删除销售订单批次明细，批次号：{}", batch.getInternalBatchNumber());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
