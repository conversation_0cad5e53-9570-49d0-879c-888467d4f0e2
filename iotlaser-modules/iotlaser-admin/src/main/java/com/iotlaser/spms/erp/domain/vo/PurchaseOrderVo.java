package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.PurchaseOrder;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.enums.PurchaseOrderType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 采购订单视图对象 erp_purchase_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PurchaseOrder.class)
public class PurchaseOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 订单编号
     */
    @ExcelProperty(value = "订单编号")
    private String orderCode;

    /**
     * 订单名称
     */
    @ExcelProperty(value = "订单名称")
    private String orderName;

    /**
     * 来源ID
     */
    @ExcelProperty(value = "来源ID")
    private Long sourceId;

    /**
     * 来源编号
     */
    @ExcelProperty(value = "来源编号")
    private String sourceCode;

    /**
     * 来源名称
     */
    @ExcelProperty(value = "来源名称")
    private String sourceName;

    /**
     * 来源类型
     */
    @ExcelProperty(value = "来源类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_purchase_order_type")
    private PurchaseOrderType sourceType;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long supplierId;

    /**
     * 供应商编码
     */
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 下单日期
     */
    @ExcelProperty(value = "下单日期")
    private LocalDate orderDate;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_purchase_order_status")
    private PurchaseOrderStatus orderStatus;

    /**
     * 申请人ID
     */
    @ExcelProperty(value = "申请人ID")
    private Long applicantId;

    /**
     * 申请人
     */
    @ExcelProperty(value = "申请人")
    private String applicantName;

    /**
     * 采购员ID
     */
    @ExcelProperty(value = "采购员ID")
    private Long handlerId;

    /**
     * 采购员
     */
    @ExcelProperty(value = "采购员")
    private String handlerName;

    /**
     * 审批人ID
     */
    @ExcelProperty(value = "审批人ID")
    private Long approverId;

    /**
     * 审批人
     */
    @ExcelProperty(value = "审批人")
    private String approverName;

    /**
     * 审批通过时间
     */
    @ExcelProperty(value = "审批通过时间")
    private LocalDateTime approveTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

    /**
     * 明细
     */
    private List<PurchaseOrderItemVo> items;

}
