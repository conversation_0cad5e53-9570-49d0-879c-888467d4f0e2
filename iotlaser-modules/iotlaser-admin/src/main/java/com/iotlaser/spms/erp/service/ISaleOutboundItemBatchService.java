package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.SaleOutboundItemBatchBo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundItemBatchVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 销售出库批次明细Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
public interface ISaleOutboundItemBatchService {

    /**
     * 查询销售出库批次明细
     *
     * @param batchId 主键
     * @return 销售出库批次明细
     */
    SaleOutboundItemBatchVo queryById(Long batchId);

    /**
     * 分页查询销售出库批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售出库批次明细分页列表
     */
    TableDataInfo<SaleOutboundItemBatchVo> queryPageList(SaleOutboundItemBatchBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的销售出库批次明细列表
     *
     * @param bo 查询条件
     * @return 销售出库批次明细列表
     */
    List<SaleOutboundItemBatchVo> queryList(SaleOutboundItemBatchBo bo);

    /**
     * 新增销售出库批次明细
     *
     * @param bo 销售出库批次明细
     * @return 是否新增成功
     */
    Boolean insertByBo(SaleOutboundItemBatchBo bo);

    /**
     * 修改销售出库批次明细
     *
     * @param bo 销售出库批次明细
     * @return 是否修改成功
     */
    Boolean updateByBo(SaleOutboundItemBatchBo bo);

    /**
     * 校验并批量删除销售出库批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
