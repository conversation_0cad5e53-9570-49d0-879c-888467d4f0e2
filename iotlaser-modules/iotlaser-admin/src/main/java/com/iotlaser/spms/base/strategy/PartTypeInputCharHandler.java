package com.iotlaser.spms.base.strategy;

import com.iotlaser.spms.base.domain.vo.AutoCodePartVo;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 输入字符处理
 *
 * <AUTHOR>
 * @date 2025/5/14
 */
@Component
@Order(0)
public class PartTypeInputCharHandler implements PartTypeTemplate {

    /**
     * 部分处理函数，用于处理自动代码生成中的特定部分
     * 此函数的目的是根据给定的长度限制，对输入的字符进行验证和处理
     * 如果输入字符为空，则直接返回空字符串
     * 如果输入字符的长度超过了指定的部分长度且部分长度大于0，则抛出异常
     * 这是为了确保输入字符的长度符合预期的限制，从而避免潜在的错误或异常
     *
     * @param vo 包含输入字符和部分长度的自动代码生成部分对象
     * @return 处理后的输入字符，如果输入为空或不符合长度要求，则返回空字符串或抛出异常
     * @throws IllegalArgumentException 如果输入字符的长度超过了指定的部分长度且部分长度大于0
     */
    @Override
    public String partHandle(AutoCodePartVo vo) {
        // 获取输入字符
        String inputCharacter = vo.getInputCharacter();
        // 检查输入字符是否为空，为空则直接返回空字符串
        if (StringUtils.isEmpty(inputCharacter)) {
            return "";
        }
        // 检查输入字符的长度是否超过了指定的部分长度
        if (vo.getPartLength() != null && inputCharacter.length() > vo.getPartLength()) {
            // 如果部分长度大于0且输入字符长度超过部分长度，则抛出异常
            if (vo.getPartLength() > 0) {
                throw new IllegalArgumentException("传入字符的长度 (" + inputCharacter.length() + ") 超出定义的部分长度 (" + vo.getPartLength() + ")！");
            }
        }
        // 返回处理后的输入字符
        return inputCharacter;
    }

}
