package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.SaleOrderItem;
import com.iotlaser.spms.erp.domain.vo.SaleOrderItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 销售订单明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface SaleOrderItemMapper extends BaseMapperPlus<SaleOrderItem, SaleOrderItemVo> {

    /**
     * 查询销售订单明细表及其关联信息
     */
    SaleOrderItemVo queryByIdWith(@Param("itemId") Long itemId);

    /**
     * 分页查询销售订单明细表及其关联信息
     */
    List<SaleOrderItemVo> queryPageListWith(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<SaleOrderItem> wrapper);

}
