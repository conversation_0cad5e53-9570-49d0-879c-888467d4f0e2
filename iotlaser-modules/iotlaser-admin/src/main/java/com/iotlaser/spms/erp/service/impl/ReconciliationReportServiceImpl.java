package com.iotlaser.spms.erp.service.impl;

import com.iotlaser.spms.erp.domain.vo.FinancialReconciliationVo;
import com.iotlaser.spms.erp.domain.vo.ReconciliationReportVo;
import com.iotlaser.spms.erp.service.IFinancialReconciliationService;
import com.iotlaser.spms.erp.service.IReconciliationReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 对账报表服务实现类
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ReconciliationReportServiceImpl implements IReconciliationReportService {

    private final IFinancialReconciliationService financialReconciliationService;

    /**
     * 生成汇总对账报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 汇总报表
     */
    @Override
    public ReconciliationReportVo generateSummaryReport(LocalDate startDate, LocalDate endDate) {
        try {
            log.info("开始生成汇总对账报表 - 日期范围: {} 到 {}", startDate, endDate);

            ReconciliationReportVo report = new ReconciliationReportVo();
            report.setReportType(ReconciliationReportVo.ReportType.SUMMARY);
            report.setReportTitle("财务对账汇总报表");
            report.setStartDate(startDate);
            report.setEndDate(endDate);
            report.setGenerateTime(LocalDateTime.now());

            // 获取统计信息
            IFinancialReconciliationService.ReconciliationStatistics statistics =
                financialReconciliationService.getReconciliationStatistics(startDate, endDate);

            // 转换为报表统计信息
            ReconciliationReportVo.SummaryStatistics summaryStats = new ReconciliationReportVo.SummaryStatistics();
            summaryStats.setTotalOrders(statistics.getTotalOrders());
            summaryStats.setMatchedOrders(statistics.getMatchedOrders());
            summaryStats.setDifferenceOrders(statistics.getDifferenceOrders());
            summaryStats.setUnreconciledOrders(statistics.getUnreconciledOrders());
            summaryStats.setTotalOrderAmount(statistics.getTotalOrderAmount());
            summaryStats.setTotalReceivedAmount(statistics.getTotalReceivedAmount());
            summaryStats.setTotalInvoicedAmount(statistics.getTotalInvoicedAmount());
            summaryStats.setTotalDifferenceAmount(statistics.getTotalDifferenceAmount());

            // 计算比率
            if (statistics.getTotalOrders() > 0) {
                BigDecimal totalOrders = new BigDecimal(statistics.getTotalOrders());
                summaryStats.setMatchedRate(new BigDecimal(statistics.getMatchedOrders())
                    .divide(totalOrders, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
            }

            if (statistics.getTotalOrderAmount() != null && statistics.getTotalOrderAmount().compareTo(BigDecimal.ZERO) > 0) {
                summaryStats.setReceivedRate(statistics.getTotalReceivedAmount()
                    .divide(statistics.getTotalOrderAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
                summaryStats.setInvoicedRate(statistics.getTotalInvoicedAmount()
                    .divide(statistics.getTotalOrderAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
            }

            report.setSummaryStatistics(summaryStats);

            // 获取异常提醒
            List<ReconciliationReportVo.ReconciliationAlert> alerts = getReconciliationAlerts(startDate, endDate);
            report.setAlerts(alerts);

            log.info("汇总对账报表生成完成 - 总订单: {}, 一致: {}, 差异: {}",
                statistics.getTotalOrders(), statistics.getMatchedOrders(), statistics.getDifferenceOrders());

            return report;
        } catch (Exception e) {
            log.error("生成汇总对账报表失败 - 日期范围: {} 到 {}, 错误: {}", startDate, endDate, e.getMessage(), e);
            throw new ServiceException("生成汇总对账报表失败：" + e.getMessage());
        }
    }

    /**
     * 生成明细对账报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 明细报表
     */
    @Override
    public ReconciliationReportVo generateDetailReport(LocalDate startDate, LocalDate endDate) {
        try {
            log.info("开始生成明细对账报表 - 日期范围: {} 到 {}", startDate, endDate);

            ReconciliationReportVo report = new ReconciliationReportVo();
            report.setReportType(ReconciliationReportVo.ReportType.DETAIL);
            report.setReportTitle("财务对账明细报表");
            report.setStartDate(startDate);
            report.setEndDate(endDate);
            report.setGenerateTime(LocalDateTime.now());

            // 获取对账明细（这里需要实现按日期范围查询的方法）
            // TODO: 实现按日期范围获取对账明细的方法
            List<FinancialReconciliationVo> reconciliationDetails = new ArrayList<>();
            report.setReconciliationDetails(reconciliationDetails);

            // 生成汇总统计
            ReconciliationReportVo.SummaryStatistics summaryStats = generateSummaryFromDetails(reconciliationDetails);
            report.setSummaryStatistics(summaryStats);

            log.info("明细对账报表生成完成 - 明细数: {}", reconciliationDetails.size());

            return report;
        } catch (Exception e) {
            log.error("生成明细对账报表失败 - 日期范围: {} 到 {}, 错误: {}", startDate, endDate, e.getMessage(), e);
            throw new ServiceException("生成明细对账报表失败：" + e.getMessage());
        }
    }

    /**
     * 生成差异对账报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 差异报表
     */
    @Override
    public ReconciliationReportVo generateDifferenceReport(LocalDate startDate, LocalDate endDate) {
        try {
            log.info("开始生成差异对账报表 - 日期范围: {} 到 {}", startDate, endDate);

            ReconciliationReportVo report = new ReconciliationReportVo();
            report.setReportType(ReconciliationReportVo.ReportType.DIFFERENCE);
            report.setReportTitle("财务对账差异报表");
            report.setStartDate(startDate);
            report.setEndDate(endDate);
            report.setGenerateTime(LocalDateTime.now());

            // 获取差异报告
            List<FinancialReconciliationVo> differenceReconciliations =
                financialReconciliationService.getDifferenceReport(startDate, endDate);

            // 转换为差异明细
            List<ReconciliationReportVo.DifferenceDetail> differenceDetails = differenceReconciliations.stream()
                .map(this::convertToDifferenceDetail)
                .collect(Collectors.toList());

            report.setDifferenceDetails(differenceDetails);

            // 生成汇总统计
            ReconciliationReportVo.SummaryStatistics summaryStats = generateSummaryFromDetails(differenceReconciliations);
            report.setSummaryStatistics(summaryStats);

            log.info("差异对账报表生成完成 - 差异数: {}", differenceDetails.size());

            return report;
        } catch (Exception e) {
            log.error("生成差异对账报表失败 - 日期范围: {} 到 {}, 错误: {}", startDate, endDate, e.getMessage(), e);
            throw new ServiceException("生成差异对账报表失败：" + e.getMessage());
        }
    }

    /**
     * 生成客户对账报表
     *
     * @param customerId 客户ID（可选，为空时生成所有客户）
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return 客户对账报表
     */
    @Override
    public ReconciliationReportVo generateCustomerReport(Long customerId, LocalDate startDate, LocalDate endDate) {
        try {
            log.info("开始生成客户对账报表 - 客户ID: {}, 日期范围: {} 到 {}", customerId, startDate, endDate);

            ReconciliationReportVo report = new ReconciliationReportVo();
            report.setReportType(ReconciliationReportVo.ReportType.CUSTOMER);
            report.setReportTitle(customerId != null ? "客户对账报表" : "全部客户对账报表");
            report.setStartDate(startDate);
            report.setEndDate(endDate);
            report.setGenerateTime(LocalDateTime.now());

            List<FinancialReconciliationVo> reconciliations;
            if (customerId != null) {
                // 按客户查询
                reconciliations = financialReconciliationService.reconcileByCustomer(customerId, startDate, endDate);
            } else {
                // 查询所有客户（这里需要实现按日期范围查询的方法）
                // TODO: 实现按日期范围获取所有对账记录的方法
                reconciliations = new ArrayList<>();
            }

            // 按客户分组汇总
            Map<Long, List<FinancialReconciliationVo>> customerGroups = reconciliations.stream()
                .collect(Collectors.groupingBy(FinancialReconciliationVo::getCustomerId));

            List<ReconciliationReportVo.CustomerReconciliationSummary> customerSummaries = customerGroups.entrySet().stream()
                .map(entry -> generateCustomerSummary(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

            report.setCustomerSummaries(customerSummaries);

            // 生成汇总统计
            ReconciliationReportVo.SummaryStatistics summaryStats = generateSummaryFromDetails(reconciliations);
            report.setSummaryStatistics(summaryStats);

            log.info("客户对账报表生成完成 - 客户数: {}, 订单数: {}", customerSummaries.size(), reconciliations.size());

            return report;
        } catch (Exception e) {
            log.error("生成客户对账报表失败 - 客户ID: {}, 日期范围: {} 到 {}, 错误: {}",
                customerId, startDate, endDate, e.getMessage(), e);
            throw new ServiceException("生成客户对账报表失败：" + e.getMessage());
        }
    }

    /**
     * 获取对账异常提醒
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 异常提醒列表
     */
    @Override
    public List<ReconciliationReportVo.ReconciliationAlert> getReconciliationAlerts(LocalDate startDate, LocalDate endDate) {
        try {
            List<ReconciliationReportVo.ReconciliationAlert> alerts = new ArrayList<>();

            // 检查大额差异提醒（差异金额超过10000元）
            List<ReconciliationReportVo.ReconciliationAlert> largeDifferenceAlerts =
                checkLargeDifferenceAlerts(new BigDecimal("10000"), startDate, endDate);
            alerts.addAll(largeDifferenceAlerts);

            // 检查长期逾期提醒（超过30天）
            List<ReconciliationReportVo.ReconciliationAlert> overdueAlerts =
                checkOverdueAlerts(30, startDate, endDate);
            alerts.addAll(overdueAlerts);

            // 检查缺失发票提醒
            List<ReconciliationReportVo.ReconciliationAlert> missingInvoiceAlerts =
                checkMissingInvoiceAlerts(startDate, endDate);
            alerts.addAll(missingInvoiceAlerts);

            log.info("对账异常提醒检查完成 - 提醒数: {}", alerts.size());

            return alerts;
        } catch (Exception e) {
            log.error("获取对账异常提醒失败 - 日期范围: {} 到 {}, 错误: {}", startDate, endDate, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查大额差异提醒
     *
     * @param threshold 差异金额阈值
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 大额差异提醒列表
     */
    @Override
    public List<ReconciliationReportVo.ReconciliationAlert> checkLargeDifferenceAlerts(
        BigDecimal threshold, LocalDate startDate, LocalDate endDate) {
        try {
            List<ReconciliationReportVo.ReconciliationAlert> alerts = new ArrayList<>();

            // 获取差异报告
            List<FinancialReconciliationVo> differences =
                financialReconciliationService.getDifferenceReport(startDate, endDate);

            for (FinancialReconciliationVo reconciliation : differences) {
                if (reconciliation.getDifferenceAmount() != null &&
                    reconciliation.getDifferenceAmount().abs().compareTo(threshold) > 0) {

                    ReconciliationReportVo.ReconciliationAlert alert = new ReconciliationReportVo.ReconciliationAlert();
                    alert.setAlertType(ReconciliationReportVo.ReconciliationAlert.AlertType.LARGE_DIFFERENCE);
                    alert.setAlertLevel(ReconciliationReportVo.ReconciliationAlert.AlertLevel.HIGH);
                    alert.setAlertTitle("大额对账差异");
                    alert.setAlertMessage(String.format("订单 %s 存在大额对账差异，差异金额: %.2f 元",
                        reconciliation.getOrderCode(), reconciliation.getDifferenceAmount()));
                    alert.setOrderId(reconciliation.getOrderId());
                    alert.setOrderCode(reconciliation.getOrderCode());
                    alert.setAmount(reconciliation.getDifferenceAmount().abs());
                    alert.setAlertTime(LocalDateTime.now());

                    alerts.add(alert);
                }
            }

            return alerts;
        } catch (Exception e) {
            log.error("检查大额差异提醒失败 - 阈值: {}, 错误: {}", threshold, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查长期逾期提醒
     *
     * @param overdueDays 逾期天数阈值
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return 长期逾期提醒列表
     */
    @Override
    public List<ReconciliationReportVo.ReconciliationAlert> checkOverdueAlerts(
        Integer overdueDays, LocalDate startDate, LocalDate endDate) {
        try {
            List<ReconciliationReportVo.ReconciliationAlert> alerts = new ArrayList<>();

            // TODO: 实现逾期检查逻辑
            // 这里需要根据订单日期和当前日期计算逾期天数
            // 并检查未收款的订单

            log.info("长期逾期提醒检查完成 - 阈值: {} 天, 提醒数: {}", overdueDays, alerts.size());

            return alerts;
        } catch (Exception e) {
            log.error("检查长期逾期提醒失败 - 阈值: {} 天, 错误: {}", overdueDays, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查缺失发票提醒
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 缺失发票提醒列表
     */
    @Override
    public List<ReconciliationReportVo.ReconciliationAlert> checkMissingInvoiceAlerts(LocalDate startDate, LocalDate endDate) {
        try {
            List<ReconciliationReportVo.ReconciliationAlert> alerts = new ArrayList<>();

            // TODO: 实现缺失发票检查逻辑
            // 检查已收款但未开票的订单

            log.info("缺失发票提醒检查完成 - 提醒数: {}", alerts.size());

            return alerts;
        } catch (Exception e) {
            log.error("检查缺失发票提醒失败 - 错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 导出对账报表到Excel
     *
     * @param reportType 报表类型
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return Excel文件字节数组
     */
    @Override
    public byte[] exportReportToExcel(ReconciliationReportVo.ReportType reportType, LocalDate startDate, LocalDate endDate) {
        try {
            log.info("开始导出对账报表到Excel - 类型: {}, 日期范围: {} 到 {}", reportType, startDate, endDate);

            // TODO: 实现Excel导出功能
            // 可以使用EasyExcel或Apache POI来实现

            log.info("对账报表Excel导出完成");

            return new byte[0]; // 临时返回空数组
        } catch (Exception e) {
            log.error("导出对账报表到Excel失败 - 类型: {}, 错误: {}", reportType, e.getMessage(), e);
            throw new ServiceException("导出对账报表到Excel失败：" + e.getMessage());
        }
    }

    /**
     * 发送对账异常邮件提醒
     *
     * @param alerts     异常提醒列表
     * @param recipients 收件人邮箱列表
     * @return 是否发送成功
     */
    @Override
    public Boolean sendAlertEmail(List<ReconciliationReportVo.ReconciliationAlert> alerts, List<String> recipients) {
        try {
            if (alerts.isEmpty() || recipients.isEmpty()) {
                log.info("无异常提醒或收件人，跳过邮件发送");
                return true;
            }

            log.info("开始发送对账异常邮件提醒 - 提醒数: {}, 收件人数: {}", alerts.size(), recipients.size());

            // TODO: 实现邮件发送功能
            // 可以集成Spring Mail或其他邮件服务

            log.info("对账异常邮件发送完成");

            return true;
        } catch (Exception e) {
            log.error("发送对账异常邮件失败 - 错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取对账KPI指标
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return KPI指标
     */
    @Override
    public ReconciliationKPI getReconciliationKPI(LocalDate startDate, LocalDate endDate) {
        try {
            log.info("开始计算对账KPI指标 - 日期范围: {} 到 {}", startDate, endDate);

            ReconciliationKPI kpi = new ReconciliationKPI();

            // 获取统计信息
            IFinancialReconciliationService.ReconciliationStatistics statistics =
                financialReconciliationService.getReconciliationStatistics(startDate, endDate);

            // 计算对账及时率（假设7天内完成对账为及时）
            if (statistics.getTotalOrders() > 0) {
                BigDecimal totalOrders = new BigDecimal(statistics.getTotalOrders());

                // 对账准确率 = 对账一致订单数 / 总订单数
                kpi.setAccuracyRate(new BigDecimal(statistics.getMatchedOrders())
                    .divide(totalOrders, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));

                // TODO: 实现其他KPI指标的计算
                kpi.setTimelyRate(new BigDecimal("95.00")); // 临时值
                kpi.setAverageCycleDays(new BigDecimal("3.5")); // 临时值
                kpi.setDifferenceResolveRate(new BigDecimal("90.00")); // 临时值
            }

            // 计算收款及时率和开票及时率
            if (statistics.getTotalOrderAmount() != null && statistics.getTotalOrderAmount().compareTo(BigDecimal.ZERO) > 0) {
                kpi.setReceivedTimelyRate(statistics.getTotalReceivedAmount()
                    .divide(statistics.getTotalOrderAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
                kpi.setInvoiceTimelyRate(statistics.getTotalInvoicedAmount()
                    .divide(statistics.getTotalOrderAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
            }

            log.info("对账KPI指标计算完成 - 准确率: {}%, 收款及时率: {}%",
                kpi.getAccuracyRate(), kpi.getReceivedTimelyRate());

            return kpi;
        } catch (Exception e) {
            log.error("计算对账KPI指标失败 - 日期范围: {} 到 {}, 错误: {}", startDate, endDate, e.getMessage(), e);
            throw new ServiceException("计算对账KPI指标失败：" + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 从对账明细生成汇总统计
     */
    private ReconciliationReportVo.SummaryStatistics generateSummaryFromDetails(List<FinancialReconciliationVo> details) {
        ReconciliationReportVo.SummaryStatistics stats = new ReconciliationReportVo.SummaryStatistics();

        stats.setTotalOrders(details.size());

        long matchedCount = details.stream()
            .filter(d -> d.getReconciliationStatus() == FinancialReconciliationVo.ReconciliationStatus.MATCHED)
            .count();
        stats.setMatchedOrders((int) matchedCount);

        long differenceCount = details.stream()
            .filter(d -> d.getReconciliationStatus() == FinancialReconciliationVo.ReconciliationStatus.DIFFERENCE)
            .count();
        stats.setDifferenceOrders((int) differenceCount);

        long unreconciledCount = details.stream()
            .filter(d -> d.getReconciliationStatus() == FinancialReconciliationVo.ReconciliationStatus.UNRECONCILED)
            .count();
        stats.setUnreconciledOrders((int) unreconciledCount);

        // 计算金额汇总
        BigDecimal totalOrderAmount = details.stream()
            .map(d -> d.getOrderAmount() != null ? d.getOrderAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setTotalOrderAmount(totalOrderAmount);

        BigDecimal totalReceivedAmount = details.stream()
            .map(d -> d.getReceivedAmount() != null ? d.getReceivedAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setTotalReceivedAmount(totalReceivedAmount);

        BigDecimal totalInvoicedAmount = details.stream()
            .map(d -> d.getInvoicedAmount() != null ? d.getInvoicedAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setTotalInvoicedAmount(totalInvoicedAmount);

        BigDecimal totalDifferenceAmount = details.stream()
            .map(d -> d.getDifferenceAmount() != null ? d.getDifferenceAmount().abs() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setTotalDifferenceAmount(totalDifferenceAmount);

        return stats;
    }

    /**
     * 转换为差异明细
     */
    private ReconciliationReportVo.DifferenceDetail convertToDifferenceDetail(FinancialReconciliationVo reconciliation) {
        ReconciliationReportVo.DifferenceDetail detail = new ReconciliationReportVo.DifferenceDetail();
        detail.setOrderId(reconciliation.getOrderId());
        detail.setOrderCode(reconciliation.getOrderCode());
        detail.setCustomerName(reconciliation.getCustomerName());
        detail.setOrderAmount(reconciliation.getOrderAmount());
        detail.setReceivedAmount(reconciliation.getReceivedAmount());
        detail.setInvoicedAmount(reconciliation.getInvoicedAmount());
        detail.setDifferenceAmount(reconciliation.getDifferenceAmount());
        detail.setDifferenceRemark(reconciliation.getDifferenceRemark());
        detail.setOrderDate(reconciliation.getOrderDate());

        // 判断差异类型
        if (reconciliation.getDifferenceAmount() != null) {
            if (reconciliation.getDifferenceAmount().compareTo(BigDecimal.ZERO) > 0) {
                detail.setDifferenceType("收款超过开票");
            } else {
                detail.setDifferenceType("开票超过收款");
            }
        }

        return detail;
    }

    /**
     * 生成客户汇总
     */
    private ReconciliationReportVo.CustomerReconciliationSummary generateCustomerSummary(
        Long customerId, List<FinancialReconciliationVo> customerReconciliations) {

        ReconciliationReportVo.CustomerReconciliationSummary summary = new ReconciliationReportVo.CustomerReconciliationSummary();

        if (!customerReconciliations.isEmpty()) {
            FinancialReconciliationVo first = customerReconciliations.get(0);
            summary.setCustomerId(customerId);
            summary.setCustomerCode(first.getCustomerCode());
            summary.setCustomerName(first.getCustomerName());
        }

        summary.setOrderCount(customerReconciliations.size());

        // 计算金额汇总
        BigDecimal totalOrderAmount = customerReconciliations.stream()
            .map(r -> r.getOrderAmount() != null ? r.getOrderAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        summary.setTotalOrderAmount(totalOrderAmount);

        BigDecimal totalReceivedAmount = customerReconciliations.stream()
            .map(r -> r.getReceivedAmount() != null ? r.getReceivedAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        summary.setTotalReceivedAmount(totalReceivedAmount);

        BigDecimal totalInvoicedAmount = customerReconciliations.stream()
            .map(r -> r.getInvoicedAmount() != null ? r.getInvoicedAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        summary.setTotalInvoicedAmount(totalInvoicedAmount);

        // 计算未收款和未开票金额
        summary.setUnreceivedAmount(totalOrderAmount.subtract(totalReceivedAmount));
        summary.setUninvoicedAmount(totalOrderAmount.subtract(totalInvoicedAmount));
        summary.setDifferenceAmount(totalReceivedAmount.subtract(totalInvoicedAmount));

        // 计算比率
        if (totalOrderAmount.compareTo(BigDecimal.ZERO) > 0) {
            summary.setReceivedRate(totalReceivedAmount
                .divide(totalOrderAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
            summary.setInvoicedRate(totalInvoicedAmount
                .divide(totalOrderAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
        }

        return summary;
    }
}
