package com.iotlaser.spms.base.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.base.domain.bo.AutoCodeRuleBo;
import com.iotlaser.spms.base.domain.vo.AutoCodeRuleVo;
import com.iotlaser.spms.base.service.IAutoCodeRuleService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 编码生成规则
 *
 * <AUTHOR> Kai
 * @date 2025/03/11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/base/autoCodeRule")
public class AutoCodeRuleController extends BaseController {

    private final IAutoCodeRuleService sysAutoCodeRuleService;

    /**
     * 查询编码生成规则列表
     */
    @SaCheckPermission("base:autoCodeRule:list")
    @GetMapping("/list")
    public TableDataInfo<AutoCodeRuleVo> list(AutoCodeRuleBo bo, PageQuery pageQuery) {
        return sysAutoCodeRuleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出编码生成规则列表
     */
    @SaCheckPermission("base:autoCodeRule:export")
    @Log(title = "编码生成规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AutoCodeRuleBo bo, HttpServletResponse response) {
        List<AutoCodeRuleVo> list = sysAutoCodeRuleService.queryList(bo);
        ExcelUtil.exportExcel(list, "编码生成规则", AutoCodeRuleVo.class, response);
    }

    /**
     * 获取编码生成规则详细信息
     *
     * @param ruleId 主键
     */
    @SaCheckPermission("base:autoCodeRule:query")
    @GetMapping("/{ruleId}")
    public R<AutoCodeRuleVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long ruleId) {
        return R.ok(sysAutoCodeRuleService.queryById(ruleId));
    }

    /**
     * 新增编码生成规则
     */
    @SaCheckPermission("base:autoCodeRule:add")
    @Log(title = "编码生成规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AutoCodeRuleBo bo) {
        return toAjax(sysAutoCodeRuleService.insertByBo(bo));
    }

    /**
     * 修改编码生成规则
     */
    @SaCheckPermission("base:autoCodeRule:edit")
    @Log(title = "编码生成规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AutoCodeRuleBo bo) {
        return toAjax(sysAutoCodeRuleService.updateByBo(bo));
    }

    /**
     * 删除编码生成规则
     *
     * @param ruleIds 主键串
     */
    @SaCheckPermission("base:autoCodeRule:remove")
    @Log(title = "编码生成规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ruleIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ruleIds) {
        return toAjax(sysAutoCodeRuleService.deleteWithValidByIds(List.of(ruleIds), true));
    }
}
