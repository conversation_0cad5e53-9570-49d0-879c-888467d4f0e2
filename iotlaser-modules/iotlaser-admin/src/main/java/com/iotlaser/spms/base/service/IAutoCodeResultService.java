package com.iotlaser.spms.base.service;

import com.iotlaser.spms.base.domain.bo.AutoCodeResultBo;
import com.iotlaser.spms.base.domain.vo.AutoCodeResultVo;
import com.iotlaser.spms.base.enums.CycleMethod;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 编码生成记录Service接口
 *
 * <AUTHOR>
 * @date 2025/03/11
 */
public interface IAutoCodeResultService {

    /**
     * 查询编码生成记录
     *
     * @param codeId 主键
     * @return 编码生成记录
     */
    AutoCodeResultVo queryById(Long codeId);

    /**
     * 分页查询编码生成记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 编码生成记录分页列表
     */
    TableDataInfo<AutoCodeResultVo> queryPageList(AutoCodeResultBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的编码生成记录列表
     *
     * @param bo 查询条件
     * @return 编码生成记录列表
     */
    List<AutoCodeResultVo> queryList(AutoCodeResultBo bo);

    /**
     * 新增编码生成记录
     *
     * @param bo 编码生成记录
     * @return 是否新增成功
     */
    Boolean insertByBo(AutoCodeResultBo bo);

    /**
     * 修改编码生成记录
     *
     * @param bo 编码生成记录
     * @return 是否修改成功
     */
    Boolean updateByBo(AutoCodeResultBo bo);

    /**
     * 校验并批量删除编码生成记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    AutoCodeResultVo findLastResult(AutoCodeResultBo bo);

    AutoCodeResultVo findLastResult(AutoCodeResultBo bo, CycleMethod method);
}
