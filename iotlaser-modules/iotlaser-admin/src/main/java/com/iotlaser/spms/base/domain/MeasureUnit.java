package com.iotlaser.spms.base.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 计量单位对象 base_measure_unit
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("base_measure_unit")
public class MeasureUnit extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 单位ID
     */
    @TableId(value = "unit_id")
    private Long unitId;

    /**
     * 上级节点
     */
    private Long parentId;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 换算比例
     */
    private BigDecimal unitRatio;

    /**
     * 是否主单位
     */
    private String primaryFlag;

    /**
     * 排列顺序
     */
    private Long orderNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

}
