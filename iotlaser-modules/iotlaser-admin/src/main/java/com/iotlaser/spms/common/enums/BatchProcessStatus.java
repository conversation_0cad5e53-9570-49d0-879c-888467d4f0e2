package com.iotlaser.spms.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统一的批次处理状态枚举
 * 用于所有出入库批次表的状态管理
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Getter
@AllArgsConstructor
public enum BatchProcessStatus {

    /**
     * 待处理 - 批次已创建，等待处理
     */
    PENDING("PENDING", "待处理"),

    /**
     * 处理中 - 批次正在处理中
     */
    PROCESSING("PROCESSING", "处理中"),

    /**
     * 已完成 - 批次处理完成
     */
    COMPLETED("COMPLETED", "已完成"),

    /**
     * 部分完成 - 批次部分处理完成
     */
    PARTIALLY_COMPLETED("PARTIALLY_COMPLETED", "部分完成"),

    /**
     * 已取消 - 批次处理被取消
     */
    CANCELLED("CANCELLED", "已取消"),

    /**
     * 处理失败 - 批次处理失败
     */
    FAILED("FAILED", "处理失败"),

    /**
     * 已过期 - 批次已过期
     */
    EXPIRED("EXPIRED", "已过期"),

    /**
     * 已冻结 - 批次被冻结，暂停处理
     */
    FROZEN("FROZEN", "已冻结");

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据代码获取状态枚举
     *
     * @param code 状态代码
     * @return 状态枚举
     */
    public static BatchProcessStatus fromCode(String code) {
        if (code == null) {
            return null;
        }

        for (BatchProcessStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }

        throw new IllegalArgumentException("无效的批次处理状态代码：" + code);
    }

    /**
     * 检查状态流转是否合法
     *
     * @param fromStatus 原状态
     * @param toStatus   目标状态
     * @return 是否合法
     */
    public static boolean isValidTransition(BatchProcessStatus fromStatus, BatchProcessStatus toStatus) {
        if (fromStatus == null || toStatus == null) {
            return true; // 初始状态或空状态允许任意转换
        }

        // 终态不能再变更
        if (fromStatus.isFinalStatus()) {
            return fromStatus == toStatus;
        }

        // 定义合法的状态流转
        switch (fromStatus) {
            case PENDING:
                return toStatus == PROCESSING || toStatus == CANCELLED || toStatus == FROZEN;
            case PROCESSING:
                return toStatus == COMPLETED || toStatus == PARTIALLY_COMPLETED ||
                    toStatus == FAILED || toStatus == CANCELLED || toStatus == FROZEN;
            case PARTIALLY_COMPLETED:
                return toStatus == COMPLETED || toStatus == PROCESSING ||
                    toStatus == CANCELLED || toStatus == FROZEN;
            case FROZEN:
                return toStatus == PENDING || toStatus == PROCESSING || toStatus == CANCELLED;
            default:
                return false;
        }
    }

    /**
     * 检查状态是否为终态（不可再变更）
     *
     * @return 是否为终态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED || this == FAILED || this == EXPIRED;
    }

    /**
     * 检查状态是否为活跃状态（可以继续处理）
     *
     * @return 是否为活跃状态
     */
    public boolean isActiveStatus() {
        return this == PENDING || this == PROCESSING || this == PARTIALLY_COMPLETED;
    }

    @Override
    public String toString() {
        return this.description;
    }
}
