package com.iotlaser.spms.common.controller;

import com.iotlaser.spms.common.domain.bo.SourceBo;
import com.iotlaser.spms.common.domain.bo.SourceItemBo;
import com.iotlaser.spms.common.domain.vo.SourceItemVo;
import com.iotlaser.spms.common.domain.vo.SourceVo;
import com.iotlaser.spms.common.service.ISourceService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.QueryGroup;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/source/document")
public class SourceController extends BaseController {

    private final ISourceService inboundItemService;

    /**
     * 获取产品明细表以及关联详细信息
     *
     * @param sourceType 来源类型
     * @param id         主键
     * @return 明细及关联信息
     */
    @GetMapping("with/{sourceType}/{id}")
    public R<SourceVo> queryByIdWith(@NotNull(message = "来源类型不能为空") @PathVariable String sourceType, @NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(inboundItemService.queryByIdWith(sourceType, id));
    }

    /**
     * 查询产品明细表列表以及关联详细信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 明细及关联信息
     */
    @GetMapping("with/list")
    public TableDataInfo<SourceVo> queryPageListWith(@Validated(QueryGroup.class) SourceBo bo, PageQuery pageQuery) {
        return inboundItemService.queryPageListWith(bo, pageQuery);
    }

    /**
     * 获取产品明细表以及关联详细信息
     *
     * @param sourceType 来源类型
     * @param id         主键
     * @return 明细及关联信息
     */
    @GetMapping("item/with/{sourceType}/{id}")
    public R<SourceItemVo> queryItemByIdWith(@NotNull(message = "来源类型不能为空") @PathVariable String sourceType, @NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(inboundItemService.queryItemByIdWith(sourceType, id));
    }

    /**
     * 查询产品明细表列表以及关联详细信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 明细及关联信息
     */
    @GetMapping("item/with/list")
    public TableDataInfo<SourceItemVo> queryItemPageListWith(@Validated(QueryGroup.class) SourceItemBo bo, PageQuery pageQuery) {
        return inboundItemService.queryItemPageListWith(bo, pageQuery);
    }

}
