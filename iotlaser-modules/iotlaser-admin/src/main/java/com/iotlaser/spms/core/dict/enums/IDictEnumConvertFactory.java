package com.iotlaser.spms.core.dict.enums;

import cn.hutool.core.util.StrUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;
import org.springframework.stereotype.Component;

/**
 * 用户前端直接接收枚举
 */
@Component
public class IDictEnumConvertFactory implements ConverterFactory<String, IDictEnum> {


    public static <T extends IDictEnum> Object getIEnum(Class<T> enumType, String source) {
        for (T enumObj : enumType.getEnumConstants()) {
            if (source.equals(String.valueOf(enumObj.getValue()))) {
                return enumObj;
            }
        }
        return null;
    }

    @NotNull
    @Override
    public <T extends IDictEnum> Converter<String, T> getConverter(@NotNull Class<T> targetType) {
        return new StringToEnumConverter<>(targetType);
    }

    @SuppressWarnings("all")
    private static class StringToEnumConverter<T extends IDictEnum> implements Converter<String, T> {
        private Class<T> enumType;

        public StringToEnumConverter(Class<T> enumType) {
            this.enumType = enumType;
        }

        @Override
        public T convert(String source) {
            if (StrUtil.isEmpty(source)) {
                return null;
            }
            return (T) IDictEnumConvertFactory.getIEnum(this.enumType, source);
        }
    }
}
