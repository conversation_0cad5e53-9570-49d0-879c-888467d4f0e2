package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.SaleReturnItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 销售退货明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/08
 */
public interface ISaleReturnItemService {

    /**
     * 查询销售退货明细
     *
     * @param itemId 主键
     * @return 销售退货明细
     */
    SaleReturnItemVo queryById(Long itemId);

    /**
     * 分页查询销售退货明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售退货明细分页列表
     */
    TableDataInfo<SaleReturnItemVo> queryPageList(SaleReturnItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的销售退货明细列表
     *
     * @param bo 查询条件
     * @return 销售退货明细列表
     */
    List<SaleReturnItemVo> queryList(SaleReturnItemBo bo);

    /**
     * 新增销售退货明细
     *
     * @param bo 销售退货明细
     * @return 是否新增成功
     */
    Boolean insertByBo(SaleReturnItemBo bo);

    /**
     * 修改销售退货明细
     *
     * @param bo 销售退货明细
     * @return 是否修改成功
     */
    Boolean updateByBo(SaleReturnItemBo bo);

    /**
     * 校验并批量删除销售退货明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据退货单ID查询明细ID列表
     *
     * @param returnId 退货单ID
     * @return 明细ID列表
     */
    List<Long> selectItemIdsByReturnId(Long returnId);

    /**
     * 批量插入或更新销售退货明细
     *
     * @param items 明细BO集合
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<SaleReturnItemBo> items);

    /**
     * 根据ID集合删除销售退货明细
     *
     * @param ids ID集合
     * @return 是否删除成功
     */
    Boolean deleteByIds(Collection<Long> ids);

    /**
     * 查询销售退货明细表及其关联信息
     *
     * @param itemId 主键
     * @return 销售退货明细表
     */
    SaleReturnItemVo queryByIdWith(Long itemId);

    /**
     * 分页查询销售退货明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售退货明细表分页列表
     */
    TableDataInfo<SaleReturnItemVo> queryPageListWith(SaleReturnItemBo bo, PageQuery pageQuery);
}
