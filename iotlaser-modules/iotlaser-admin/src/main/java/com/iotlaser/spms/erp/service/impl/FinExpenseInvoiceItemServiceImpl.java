package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinExpenseInvoiceItem;
import com.iotlaser.spms.erp.domain.bo.FinExpenseInvoiceItemBo;
import com.iotlaser.spms.erp.domain.vo.FinExpenseInvoiceItemVo;
import com.iotlaser.spms.erp.mapper.FinExpenseInvoiceItemMapper;
import com.iotlaser.spms.erp.service.IFinExpenseInvoiceItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 管理费用明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-20
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinExpenseInvoiceItemServiceImpl implements IFinExpenseInvoiceItemService {

    private final FinExpenseInvoiceItemMapper baseMapper;

    /**
     * 查询管理费用明细
     *
     * @param itemId 主键
     * @return 管理费用明细
     */
    @Override
    public FinExpenseInvoiceItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询管理费用明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 管理费用明细分页列表
     */
    @Override
    public TableDataInfo<FinExpenseInvoiceItemVo> queryPageList(FinExpenseInvoiceItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinExpenseInvoiceItem> lqw = buildQueryWrapper(bo);
        Page<FinExpenseInvoiceItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的管理费用明细列表
     *
     * @param bo 查询条件
     * @return 管理费用明细列表
     */
    @Override
    public List<FinExpenseInvoiceItemVo> queryList(FinExpenseInvoiceItemBo bo) {
        LambdaQueryWrapper<FinExpenseInvoiceItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinExpenseInvoiceItem> buildQueryWrapper(FinExpenseInvoiceItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinExpenseInvoiceItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinExpenseInvoiceItem::getItemId);
        lqw.eq(bo.getInvoiceId() != null, FinExpenseInvoiceItem::getInvoiceId, bo.getInvoiceId());
        lqw.eq(bo.getQuantity() != null, FinExpenseInvoiceItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getPrice() != null, FinExpenseInvoiceItem::getPrice, bo.getPrice());
        lqw.eq(bo.getPriceExclusiveTax() != null, FinExpenseInvoiceItem::getPriceExclusiveTax, bo.getPriceExclusiveTax());
        lqw.eq(bo.getAmount() != null, FinExpenseInvoiceItem::getAmount, bo.getAmount());
        lqw.eq(bo.getAmountExclusiveTax() != null, FinExpenseInvoiceItem::getAmountExclusiveTax, bo.getAmountExclusiveTax());
        lqw.eq(bo.getTaxRate() != null, FinExpenseInvoiceItem::getTaxRate, bo.getTaxRate());
        lqw.eq(bo.getTaxAmount() != null, FinExpenseInvoiceItem::getTaxAmount, bo.getTaxAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinExpenseInvoiceItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增管理费用明细
     *
     * @param bo 管理费用明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinExpenseInvoiceItemBo bo) {
        FinExpenseInvoiceItem add = MapstructUtils.convert(bo, FinExpenseInvoiceItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改管理费用明细
     *
     * @param bo 管理费用明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinExpenseInvoiceItemBo bo) {
        FinExpenseInvoiceItem update = MapstructUtils.convert(bo, FinExpenseInvoiceItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinExpenseInvoiceItem entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除管理费用明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验管理费用明细是否可以删除
            List<FinExpenseInvoiceItem> items = baseMapper.selectByIds(ids);
            for (FinExpenseInvoiceItem item : items) {
                // TODO: 检查主表状态，只有草稿状态的费用明细才能删除
                // 暂时注释掉，因为finExpenseInvoiceService未注入
                // FinExpenseInvoice expense = finExpenseInvoiceService.queryById(item.getInvoiceId());
                // if (expense != null && !"DRAFT".equals(expense.getInvoiceStatus()) && !"PENDING".equals(expense.getInvoiceStatus())) {
                //     throw new ServiceException("费用明细所属管理费用【" + expense.getInvoiceName() +
                //         "】状态为【" + expense.getInvoiceStatus() + "】，不允许删除明细");
                // }

                log.info("删除管理费用明细校验通过：明细ID【{}】", item.getItemId());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除管理费用明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除管理费用明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除管理费用明细失败：" + e.getMessage());
        }
    }
}
