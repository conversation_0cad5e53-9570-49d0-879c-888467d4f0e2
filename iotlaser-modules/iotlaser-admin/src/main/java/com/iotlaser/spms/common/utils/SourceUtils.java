package com.iotlaser.spms.common.utils;

import com.iotlaser.spms.common.domain.SourceInfo;

import static com.iotlaser.spms.common.constant.SourceConstant.SOURCE_DOCUMENT;

public class SourceUtils {

    /**
     * 初始化源文档信息映射
     * 该方法用于在系统启动时初始化各种业务单据的源文档信息
     * 源文档信息包括销售订单、销售出库、销售退货入库、
     * 采购订单、采购入库、采购退货出库、
     * 生产发料出库、生产退货入库、生产入库、
     * 调拨出库和调拨入库等
     */
    public static void init() {
        // 销售相关单据
        SOURCE_DOCUMENT.put("SALE_ORDER", SourceInfo.builder().type("SALE_ORDER").main("erp_sale_order").id("order_id").code("order_code").name("order_name").time("order_time").status("order_status").item("erp_sale_order_item").build());
        SOURCE_DOCUMENT.put("SALE_OUTBOUND", SourceInfo.builder().type("SALE_OUTBOUND").main("erp_sale_outbound").id("outbound_id").code("outbound_code").name("outbound_name").time("outbound_time").status("outbound_status").item("erp_sale_outbound_item").batch("erp_sale_outbound_item_batch").build());
        SOURCE_DOCUMENT.put("SALE_RETURN_INBOUND", SourceInfo.builder().type("SALE_RETURN_INBOUND").main("erp_sale_return").id("return_id").code("return_code").name("return_name").time("return_time").status("return_status").item("erp_sale_return_item").batch("erp_sale_return_item_batch").build());

        // 采购相关单据
        SOURCE_DOCUMENT.put("PURCHASE_ORDER", SourceInfo.builder().type("PURCHASE_ORDER").main("erp_purchase_order").id("order_id").code("order_code").name("order_name").time("order_time").status("order_status").item("erp_purchase_order_item").build());
        SOURCE_DOCUMENT.put("PURCHASE_INBOUND", SourceInfo.builder().type("PURCHASE_INBOUND").main("erp_purchase_inbound").id("inbound_id").code("inbound_code").name("inbound_name").time("inbound_time").status("inbound_status").item("erp_purchase_inbound_item").batch("erp_purchase_inbound_item_batch").build());
        SOURCE_DOCUMENT.put("PURCHASE_RETURN_OUTBOUND", SourceInfo.builder().type("PURCHASE_RETURN_OUTBOUND").main("erp_purchase_return").id("return_id").code("return_code").name("return_name").time("return_time").status("return_status").item("erp_purchase_return_item").batch("erp_purchase_return_item_batch").build());

        // 生产相关单据
        SOURCE_DOCUMENT.put("PRODUCTION_ISSUE_OUTBOUND", SourceInfo.builder().type("PRODUCTION_ISSUE_OUTBOUND").main("mes_production_issue").id("issue_id").code("issue_code").name("issue_name").time("issue_time").status("issue_status").item("mes_production_issue_item").batch("mes_production_issue_item_batch").build());
        SOURCE_DOCUMENT.put("PRODUCTION_RETURN_INBOUND", SourceInfo.builder().type("PRODUCTION_RETURN_INBOUND").main("mes_production_return").id("return_id").code("return_code").name("return_name").time("return_time").status("return_status").item("mes_production_return_item").batch("mes_production_return_item_batch").build());
        SOURCE_DOCUMENT.put("PRODUCTION_INBOUND", SourceInfo.builder().type("PRODUCTION_INBOUND").main("mes_production_inbound").id("inbound_id").code("inbound_code").name("inbound_name").time("inbound_time").status("inbound_status").item("mes_production_inbound_item").batch("mes_production_inbound_item_batch").build());

        // 调拨相关单据
        SOURCE_DOCUMENT.put("TRANSFER_OUTBOUND", SourceInfo.builder().type("TRANSFER_OUTBOUND").main("wms_transfer").id("transfer_id").code("transfer_code").name("transfer_name").time("transfer_time").status("transfer_status").item("wms_transfer_item").batch("wms_transfer_item_batch").build());
        SOURCE_DOCUMENT.put("TRANSFER_INBOUND", SourceInfo.builder().type("TRANSFER_INBOUND").main("wms_transfer").id("transfer_id").code("transfer_code").name("transfer_name").time("transfer_time").status("transfer_status").item("wms_transfer_item").batch("wms_transfer_item_batch").build());
    }

    /**
     * 根据文档类型获取文档信息
     * 此方法用于从预定义的文档信息映射中检索特定类型的文档信息
     * 如果映射为空，则会先调用init()方法进行初始化
     *
     * @param sourceType 文档类型字符串，用作映射的键来获取对应的文档信息
     * @return SourceInfo对象，包含文档的具体信息如果找不到匹配的类型，则返回null
     */
    public static SourceInfo getInfo(String sourceType) {
        // 检查文档信息映射是否为空，为空则进行初始化
        if (SOURCE_DOCUMENT.isEmpty()) {
            init();
        }
        // 根据提供的文档类型返回对应的文档信息
        return SOURCE_DOCUMENT.get(sourceType);
    }

}
