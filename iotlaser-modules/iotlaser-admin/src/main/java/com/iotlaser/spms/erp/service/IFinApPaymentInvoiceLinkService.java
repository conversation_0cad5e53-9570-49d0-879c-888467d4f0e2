package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinApPaymentInvoiceLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentInvoiceLinkVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 付款单与发票核销关系Service接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
public interface IFinApPaymentInvoiceLinkService {

    /**
     * 查询付款单与发票核销关系
     *
     * @param linkId 主键
     * @return 付款单与发票核销关系
     */
    FinApPaymentInvoiceLinkVo queryById(Long linkId);

    /**
     * 分页查询付款单与发票核销关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 付款单与发票核销关系分页列表
     */
    TableDataInfo<FinApPaymentInvoiceLinkVo> queryPageList(FinApPaymentInvoiceLinkBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的付款单与发票核销关系列表
     *
     * @param bo 查询条件
     * @return 付款单与发票核销关系列表
     */
    List<FinApPaymentInvoiceLinkVo> queryList(FinApPaymentInvoiceLinkBo bo);

    /**
     * 新增付款单与发票核销关系
     *
     * @param bo 付款单与发票核销关系
     * @return 是否新增成功
     */
    Boolean insertByBo(FinApPaymentInvoiceLinkBo bo);

    /**
     * 修改付款单与发票核销关系
     *
     * @param bo 付款单与发票核销关系
     * @return 是否修改成功
     */
    Boolean updateByBo(FinApPaymentInvoiceLinkBo bo);

    /**
     * 校验并批量删除付款单与发票核销关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取发票已核销金额
     *
     * @param invoiceId 发票ID
     * @return 已核销金额
     */
    BigDecimal getAppliedAmountByInvoiceId(Long invoiceId);

    /**
     * 获取付款单已核销金额
     *
     * @param paymentId 付款单ID
     * @return 已核销金额
     */
    BigDecimal getAppliedAmountByPaymentId(Long paymentId);

    /**
     * 付款单与应付单主单据级核销
     *
     * @param paymentId     付款单ID
     * @param invoiceId     应付单ID
     * @param appliedAmount 核销金额
     * @param remark        备注
     * @return 是否核销成功
     */
    Boolean applyPaymentToInvoice(Long paymentId, Long invoiceId,
                                  BigDecimal appliedAmount, String remark);

    /**
     * 撤销付款单与应付单核销
     *
     * @param linkId 核销关系ID
     * @param reason 撤销原因
     * @return 是否撤销成功
     */
    Boolean cancelPaymentInvoiceLink(Long linkId, String reason);

    /**
     * 查询应付单的核销状态
     *
     * @param invoiceId 应付单ID
     * @return 核销记录列表
     */
    List<FinApPaymentInvoiceLinkVo> queryInvoiceApplyStatus(Long invoiceId);

    /**
     * 查询付款单的核销记录
     *
     * @param paymentId 付款单ID
     * @return 核销记录列表
     */
    List<FinApPaymentInvoiceLinkVo> queryPaymentApplyRecords(Long paymentId);

    /**
     * 检查是否存在核销记录
     *
     * @param invoiceId 发票ID
     * @return 是否存在核销记录
     */
    Boolean existsByInvoiceId(Long invoiceId);

    /**
     * 检查是否存在核销记录
     *
     * @param paymentId 付款单ID
     * @return 是否存在核销记录
     */
    Boolean existsByPaymentId(Long paymentId);
}
