package com.iotlaser.spms.common.domain.bo;

import com.iotlaser.spms.common.domain.SourceItem;
import com.iotlaser.spms.pro.domain.bo.ProductBo;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.QueryGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SourceItem.class, reverseConvertGenerate = false)
public class SourceBo extends BaseEntity {
    /**
     * 主键
     */
    private Long id;
    /**
     * 来源编号
     */
    private String code;
    /**
     * 来源名称
     */
    private String name;
    /**
     * 来源时间
     */
    private Date time;
    /**
     * 状态
     */
    private String status;
    /**
     * 来源类型
     */
    @NotBlank(message = "来源类型不能为空", groups = {QueryGroup.class})
    private String sourceType;
    /**
     * 产品查询条件
     */
    private ProductBo product;
    /**
     * 明细查询条件
     */
    private SourceItemBo item;
    /**
     * 批次
     */
    private List<SourceItemBo> items;
}
