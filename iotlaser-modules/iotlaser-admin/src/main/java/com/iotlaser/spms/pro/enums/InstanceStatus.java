package com.iotlaser.spms.pro.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品实例状态枚举
 * 用于管理产品实例的生命周期状态，状态流转：草稿→激活→使用中→维护中→报废/归档
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-15
 */
@Getter
@AllArgsConstructor
public enum InstanceStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "创建中"),
    ACTIVE("active", "激活", "可正常使用"),
    IN_USE("in_use", "使用中", "正在使用"),
    MAINTENANCE("maintenance", "维护中", "正在维护"),
    RETIRED("retired", "报废", "已报废"),
    ARCHIVED("archived", "归档", "已归档");

    public final static String DICT_CODE = "pro_instance_status";
    public final static String DICT_NAME = "产品实例状态";
    public final static String DICT_DESC = "管理产品实例的生命周期状态，从草稿创建到激活使用、维护报废的完整流程状态";
    @EnumValue
    private final String value;
    private final String name;
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 实例状态枚举
     */
    public static InstanceStatus getByValue(String value) {
        for (InstanceStatus instanceStatus : values()) {
            if (instanceStatus.getValue().equals(value)) {
                return instanceStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
