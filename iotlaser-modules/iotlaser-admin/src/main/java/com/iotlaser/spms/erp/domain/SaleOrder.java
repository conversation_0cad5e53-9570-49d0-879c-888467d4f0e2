package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.erp.enums.SaleOrderStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 销售订单对象 erp_sale_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_sale_order")
public class SaleOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @TableId(value = "order_id")
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 下单日期
     */
    private LocalDate orderDate;

    /**
     * 订单状态
     */
    private SaleOrderStatus orderStatus;

    /**
     * 销售员ID
     */
    private Long handlerId;

    /**
     * 销售员
     */
    private String handlerName;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审批人
     */
    private String approverName;

    /**
     * 审批通过时间
     */
    private LocalDateTime approveTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 明细
     */
    @TableField(exist = false)
    private List<SaleOrderItem> items;

    // ==================== 临时变量：汇总字段 ====================
    // TODO: 待数据库结构完善后，这些字段应该持久化到数据库

    /**
     * 总数量（临时变量）
     * TODO: 需要在数据库中添加 total_quantity DECIMAL(15,4) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalQuantity;

    /**
     * 总金额-含税（临时变量）
     * TODO: 需要在数据库中添加 total_amount DECIMAL(15,2) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalAmount;

    /**
     * 总金额-不含税（临时变量）
     * TODO: 需要在数据库中添加 total_amount_exclusive_tax DECIMAL(15,2) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalAmountExclusiveTax;

    /**
     * 总税额（临时变量）
     * TODO: 需要在数据库中添加 total_tax_amount DECIMAL(15,2) 字段
     */
    @TableField(exist = false)
    private BigDecimal totalTaxAmount;
}
