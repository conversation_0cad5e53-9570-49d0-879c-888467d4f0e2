package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinAccountBo;
import com.iotlaser.spms.erp.domain.vo.FinAccountVo;
import com.iotlaser.spms.erp.service.IFinAccountService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 账户
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finAccount")
public class FinAccountController extends BaseController {

    private final IFinAccountService finAccountService;

    /**
     * 查询账户列表
     */
    @SaCheckPermission("erp:finAccount:list")
    @GetMapping("/list")
    public TableDataInfo<FinAccountVo> list(FinAccountBo bo, PageQuery pageQuery) {
        return finAccountService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出账户列表
     */
    @SaCheckPermission("erp:finAccount:export")
    @Log(title = "账户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinAccountBo bo, HttpServletResponse response) {
        List<FinAccountVo> list = finAccountService.queryList(bo);
        ExcelUtil.exportExcel(list, "账户", FinAccountVo.class, response);
    }

    /**
     * 获取账户详细信息
     *
     * @param accountId 主键
     */
    @SaCheckPermission("erp:finAccount:query")
    @GetMapping("/{accountId}")
    public R<FinAccountVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long accountId) {
        return R.ok(finAccountService.queryById(accountId));
    }

    /**
     * 新增账户
     */
    @SaCheckPermission("erp:finAccount:add")
    @Log(title = "账户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinAccountBo bo) {
        return toAjax(finAccountService.insertByBo(bo));
    }

    /**
     * 修改账户
     */
    @SaCheckPermission("erp:finAccount:edit")
    @Log(title = "账户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinAccountBo bo) {
        return toAjax(finAccountService.updateByBo(bo));
    }

    /**
     * 删除账户
     *
     * @param accountIds 主键串
     */
    @SaCheckPermission("erp:finAccount:remove")
    @Log(title = "账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{accountIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] accountIds) {
        return toAjax(finAccountService.deleteWithValidByIds(List.of(accountIds), true));
    }
}
