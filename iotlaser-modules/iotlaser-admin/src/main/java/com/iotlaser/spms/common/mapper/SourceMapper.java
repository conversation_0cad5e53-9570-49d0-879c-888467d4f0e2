package com.iotlaser.spms.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.common.domain.Source;
import com.iotlaser.spms.common.domain.SourceInfo;
import com.iotlaser.spms.common.domain.SourceItem;
import com.iotlaser.spms.common.domain.vo.SourceVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 采购入库明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025-04-23
 */
public interface SourceMapper extends BaseMapperPlus<SourceItem, SourceVo> {

    /**
     * 查询采购入库明细表及其关联信息
     */
    SourceVo queryByIdWith(@Param("info") SourceInfo info, @Param("id") Long id);

    /**
     * 分页查询采购入库明细表及其关联信息
     */
    List<SourceVo> queryPageListWith(@Param("info") SourceInfo info, @Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<Source> wrapper);

}
