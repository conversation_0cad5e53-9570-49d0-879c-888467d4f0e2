package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinApPaymentInvoiceLink;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 付款单与发票核销关系视图对象 erp_fin_ap_payment_invoice_link
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinApPaymentInvoiceLink.class)
public class FinApPaymentInvoiceLinkVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关系ID
     */
    @ExcelProperty(value = "关系ID")
    private Long linkId;

    /**
     * 付款ID
     */
    @ExcelProperty(value = "付款ID")
    private Long paymentId;

    /**
     * 应付ID
     */
    @ExcelProperty(value = "应付ID")
    private Long invoiceId;

    /**
     * 核销金额
     */
    @ExcelProperty(value = "核销金额")
    private BigDecimal appliedAmount;

    /**
     * 核销日期
     */
    @ExcelProperty(value = "核销日期")
    private LocalDate cancellationDate;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
