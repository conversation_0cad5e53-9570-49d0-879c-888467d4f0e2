package com.iotlaser.spms.base.domain.bo;

import com.iotlaser.spms.base.domain.Location;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 位置库位业务对象 base_location
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Location.class, reverseConvertGenerate = false)
public class LocationBo extends BaseEntity {

    /**
     * 位置库位ID
     */
    @NotNull(message = "位置库位ID不能为空", groups = {EditGroup.class})
    private Long locationId;

    /**
     * 位置库位编码
     */
    @NotBlank(message = "位置库位编码不能为空", groups = {EditGroup.class})
    private String locationCode;

    /**
     * 位置库位名称
     */
    @NotBlank(message = "位置库位名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String locationName;

    /**
     * 库位类型
     */
    @NotBlank(message = "库位类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String locationType;

    /**
     * 上级节点
     */
    @NotNull(message = "上级节点不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long parentId;

    /**
     * 排列顺序
     */
    private Long orderNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

}
