package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.FinArReceivableItem;
import com.iotlaser.spms.erp.domain.bo.FinArReceivableItemBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 应收单明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
public interface IFinArReceivableItemService {

    /**
     * 查询应收单明细
     *
     * @param itemId 主键
     * @return 应收单明细
     */
    FinArReceivableItemVo queryById(Long itemId);

    /**
     * 分页查询应收单明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应收单明细分页列表
     */
    TableDataInfo<FinArReceivableItemVo> queryPageList(FinArReceivableItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的应收单明细列表
     *
     * @param bo 查询条件
     * @return 应收单明细列表
     */
    List<FinArReceivableItemVo> queryList(FinArReceivableItemBo bo);

    /**
     * 新增应收单明细
     *
     * @param bo 应收单明细
     * @return 是否新增成功
     */
    Boolean insertByBo(FinArReceivableItemBo bo);

    /**
     * 修改应收单明细
     *
     * @param bo 应收单明细
     * @return 是否修改成功
     */
    Boolean updateByBo(FinArReceivableItemBo bo);

    /**
     * 校验并批量删除应收单明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据应收单ID获取明细列表
     *
     * @param receivableId 应收单ID
     * @return 明细列表
     */
    List<FinArReceivableItem> getItemsByReceivableId(Long receivableId);
}
