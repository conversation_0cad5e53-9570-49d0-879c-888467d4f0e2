package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinStatementItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;


/**
 * 对账单明细视图对象 erp_fin_statement_item
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinStatementItem.class)
public class FinStatementItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 明细ID
     */
    @ExcelProperty(value = "明细ID")
    private Long itemId;

    /**
     * 对账ID
     */
    @ExcelProperty(value = "对账ID")
    private Long statementId;

    /**
     * 组号
     */
    @ExcelProperty(value = "组号")
    private String groupId;

    /**
     * 组内余额
     */
    @ExcelProperty(value = "组内余额")
    private Long groupBalance;

    /**
     * 来源ID
     */
    @ExcelProperty(value = "来源ID")
    private Long sourceId;

    /**
     * 来源类型
     */
    @ExcelProperty(value = "来源类型")
    private String sourceType;

    /**
     * 来源明细ID
     */
    @ExcelProperty(value = "来源明细ID")
    private Long sourceItemId;

    /**
     * 借方金额 (应收增加)
     */
    @ExcelProperty(value = "借方金额 (应收增加)")
    private Long amountDebit;

    /**
     * 贷方金额 (应收减少)
     */
    @ExcelProperty(value = "贷方金额 (应收减少)")
    private Long amountCredit;

    /**
     * 对账标记
     */
    @ExcelProperty(value = "对账标记")
    private String markFlag;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
