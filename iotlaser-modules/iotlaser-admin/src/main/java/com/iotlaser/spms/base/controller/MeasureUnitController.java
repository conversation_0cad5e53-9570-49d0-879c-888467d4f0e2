package com.iotlaser.spms.base.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.base.domain.bo.MeasureUnitBo;
import com.iotlaser.spms.base.domain.vo.MeasureUnitVo;
import com.iotlaser.spms.base.service.IMeasureUnitService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 计量单位
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/base/measureUnit")
public class MeasureUnitController extends BaseController {

    private final IMeasureUnitService baseMeasureUnitService;

    /**
     * 查询计量单位列表
     */
    @SaCheckPermission("base:measureUnit:list")
    @GetMapping("/list")
    public R<List<MeasureUnitVo>> list(MeasureUnitBo bo) {
        List<MeasureUnitVo> list = baseMeasureUnitService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 导出计量单位列表
     */
    @SaCheckPermission("base:measureUnit:export")
    @Log(title = "计量单位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MeasureUnitBo bo, HttpServletResponse response) {
        List<MeasureUnitVo> list = baseMeasureUnitService.queryList(bo);
        ExcelUtil.exportExcel(list, "计量单位", MeasureUnitVo.class, response);
    }

    /**
     * 获取计量单位详细信息
     *
     * @param unitId 主键
     */
    @SaCheckPermission("base:measureUnit:query")
    @GetMapping("/{unitId}")
    public R<MeasureUnitVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long unitId) {
        return R.ok(baseMeasureUnitService.queryById(unitId));
    }

    /**
     * 新增计量单位
     */
    @SaCheckPermission("base:measureUnit:add")
    @Log(title = "计量单位", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MeasureUnitBo bo) {
        return toAjax(baseMeasureUnitService.insertByBo(bo));
    }

    /**
     * 修改计量单位
     */
    @SaCheckPermission("base:measureUnit:edit")
    @Log(title = "计量单位", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MeasureUnitBo bo) {
        return toAjax(baseMeasureUnitService.updateByBo(bo));
    }

    /**
     * 删除计量单位
     *
     * @param unitIds 主键串
     */
    @SaCheckPermission("base:measureUnit:remove")
    @Log(title = "计量单位", businessType = BusinessType.DELETE)
    @DeleteMapping("/{unitIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] unitIds) {
        return toAjax(baseMeasureUnitService.deleteWithValidByIds(List.of(unitIds), true));
    }
}
