package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinApInvoiceBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import com.iotlaser.spms.erp.service.IFinApInvoiceService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商发票
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finApInvoice")
public class FinApInvoiceController extends BaseController {

    private final IFinApInvoiceService finApInvoiceService;

    /**
     * 查询供应商发票列表
     */
    @SaCheckPermission("erp:finApInvoice:list")
    @GetMapping("/list")
    public TableDataInfo<FinApInvoiceVo> list(FinApInvoiceBo bo, PageQuery pageQuery) {
        return finApInvoiceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出供应商发票列表
     */
    @SaCheckPermission("erp:finApInvoice:export")
    @Log(title = "供应商发票", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinApInvoiceBo bo, HttpServletResponse response) {
        List<FinApInvoiceVo> list = finApInvoiceService.queryList(bo);
        ExcelUtil.exportExcel(list, "供应商发票", FinApInvoiceVo.class, response);
    }

    /**
     * 获取供应商发票详细信息
     *
     * @param invoiceId 主键
     */
    @SaCheckPermission("erp:finApInvoice:query")
    @GetMapping("/{invoiceId}")
    public R<FinApInvoiceVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long invoiceId) {
        return R.ok(finApInvoiceService.queryById(invoiceId));
    }

    /**
     * 新增供应商发票
     */
    @SaCheckPermission("erp:finApInvoice:add")
    @Log(title = "供应商发票", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinApInvoiceBo bo) {
        return toAjax(finApInvoiceService.insertByBo(bo));
    }

    /**
     * 修改供应商发票
     */
    @SaCheckPermission("erp:finApInvoice:edit")
    @Log(title = "供应商发票", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinApInvoiceBo bo) {
        return toAjax(finApInvoiceService.updateByBo(bo));
    }

    /**
     * 删除供应商发票
     *
     * @param invoiceIds 主键串
     */
    @SaCheckPermission("erp:finApInvoice:remove")
    @Log(title = "供应商发票", businessType = BusinessType.DELETE)
    @DeleteMapping("/{invoiceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] invoiceIds) {
        return toAjax(finApInvoiceService.deleteWithValidByIds(List.of(invoiceIds), true));
    }
}
