package com.iotlaser.spms.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.mes.domain.ProductionReturn;
import com.iotlaser.spms.mes.enums.ProductionReturnStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 生产退料视图对象 mes_production_return
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductionReturn.class)
public class ProductionReturnVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退料单ID
     */
    @ExcelProperty(value = "退料单ID")
    private Long returnId;

    /**
     * 退料单编号
     */
    @ExcelProperty(value = "退料单编号")
    private String returnCode;

    /**
     * 退料单名称
     */
    @ExcelProperty(value = "退料单名称")
    private String returnName;

    /**
     * 生产订单ID
     */
    @ExcelProperty(value = "生产订单ID")
    private Long orderId;

    /**
     * 生产订单编码
     */
    @ExcelProperty(value = "生产订单编码")
    private String orderCode;

    /**
     * 生产订单名称
     */
    @ExcelProperty(value = "生产订单名称")
    private String orderName;

    /**
     * 检验单ID
     */
    @ExcelProperty(value = "检验单ID")
    private Long inspectionId;

    /**
     * 检验单编号
     */
    @ExcelProperty(value = "检验单编号")
    private String inspectionCode;

    /**
     * 检验单名称
     */
    @ExcelProperty(value = "检验单名称")
    private String inspectionName;

    /**
     * 退料时间
     */
    @ExcelProperty(value = "退料时间")
    private Date returnTime;

    /**
     * 退料状态
     */
    @ExcelProperty(value = "退料状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_production_return_status")
    private ProductionReturnStatus returnStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

    /**
     * 明细
     */
    private List<ProductionReturnItemVo> items;
}
