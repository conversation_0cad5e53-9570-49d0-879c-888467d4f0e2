package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.SaleOutboundItem;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundItemVo;
import com.iotlaser.spms.erp.mapper.SaleOutboundItemMapper;
import com.iotlaser.spms.erp.service.ISaleOutboundItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 销售出库明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleOutboundItemServiceImpl implements ISaleOutboundItemService {

    private final SaleOutboundItemMapper baseMapper;

    /**
     * 查询销售出库明细
     *
     * @param itemId 主键
     * @return 销售出库明细
     */
    @Override
    public SaleOutboundItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询销售出库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售出库明细分页列表
     */
    @Override
    public TableDataInfo<SaleOutboundItemVo> queryPageList(SaleOutboundItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleOutboundItem> lqw = buildQueryWrapper(bo);
        Page<SaleOutboundItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售出库明细列表
     *
     * @param bo 查询条件
     * @return 销售出库明细列表
     */
    @Override
    public List<SaleOutboundItemVo> queryList(SaleOutboundItemBo bo) {
        LambdaQueryWrapper<SaleOutboundItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleOutboundItem> buildQueryWrapper(SaleOutboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleOutboundItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleOutboundItem::getItemId);
        lqw.eq(bo.getOutboundId() != null, SaleOutboundItem::getOutboundId, bo.getOutboundId());
        lqw.eq(bo.getProductId() != null, SaleOutboundItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), SaleOutboundItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), SaleOutboundItem::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, SaleOutboundItem::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), SaleOutboundItem::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), SaleOutboundItem::getUnitName, bo.getUnitName());
        lqw.eq(bo.getQuantity() != null, SaleOutboundItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getFinishQuantity() != null, SaleOutboundItem::getFinishQuantity, bo.getFinishQuantity());
        lqw.eq(bo.getPrice() != null, SaleOutboundItem::getPrice, bo.getPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleOutboundItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增销售出库明细
     *
     * @param bo 销售出库明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SaleOutboundItemBo bo) {
        SaleOutboundItem add = MapstructUtils.convert(bo, SaleOutboundItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改销售出库明细
     *
     * @param bo 销售出库明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SaleOutboundItemBo bo) {
        SaleOutboundItem update = MapstructUtils.convert(bo, SaleOutboundItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleOutboundItem entity) {
        // 校验必填字段
        if (entity.getOutboundId() == null) {
            throw new ServiceException("出库单不能为空");
        }
        if (entity.getProductId() == null) {
            throw new ServiceException("产品不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("出库数量必须大于0");
        }

        // 校验同一出库单中产品不能重复
        if (entity.getOutboundId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<SaleOutboundItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleOutboundItem::getOutboundId, entity.getOutboundId());
            wrapper.eq(SaleOutboundItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(SaleOutboundItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一销售出库单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除销售出库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验明细是否可以删除
            List<SaleOutboundItem> items = baseMapper.selectByIds(ids);
            for (SaleOutboundItem item : items) {
                log.info("删除销售出库明细，产品：{}", item.getProductName());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
