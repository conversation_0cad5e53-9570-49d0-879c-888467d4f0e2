package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存记录单类型枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025-04-23
 */
@Getter
@AllArgsConstructor
public enum SourceType implements IDictEnum<String> {

    INBOUND("inbound", "仓储入库", "仓储管理入库操作"),
    OUTBOUND("outbound", "仓储出库", "仓储管理出库操作"),
    INVENTORY_CHECK("inventory_check", "库存盘点", "仓储库存盘点操作");

    public final static String DICT_CODE = "wms_source_type";
    public final static String DICT_NAME = "库存记录来源类型";
    public final static String DICT_DESC = "定义库存变动记录的来源类型，用于追溯库存变动的业务来源";

    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 来源类型枚举
     */
    public static SourceType getByValue(String value) {
        for (SourceType sourceType : values()) {
            if (sourceType.getValue().equals(value)) {
                return sourceType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
