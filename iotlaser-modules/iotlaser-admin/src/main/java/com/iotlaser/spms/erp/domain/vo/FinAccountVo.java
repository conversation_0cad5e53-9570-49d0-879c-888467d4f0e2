package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinAccount;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 账户视图对象 erp_fin_account
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinAccount.class)
public class FinAccountVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账户ID
     */
    @ExcelProperty(value = "账户ID")
    private Long accountId;

    /**
     * 账户编码
     */
    @ExcelProperty(value = "账户编码")
    private String accountCode;

    /**
     * 账户名称
     */
    @ExcelProperty(value = "账户名称")
    private String accountName;

    /**
     * 账户类型
     */
    @ExcelProperty(value = "账户类型")
    private String accountType;

    /**
     * 开户行
     */
    @ExcelProperty(value = "开户行")
    private String bankName;

    /**
     * 银行账号/支付账号
     */
    @ExcelProperty(value = "银行账号/支付账号")
    private String accountNumber;

    /**
     * 币种
     */
    @ExcelProperty(value = "币种")
    private String currency;

    /**
     * 期初余额
     */
    @ExcelProperty(value = "期初余额")
    private BigDecimal initialBalance;

    /**
     * 当前余额(由流水实时更新)
     */
    @ExcelProperty(value = "当前余额")
    private BigDecimal currentBalance;

    /**
     * 账户状态
     */
    @ExcelProperty(value = "账户状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_fin_account_status")
    private String accountStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
