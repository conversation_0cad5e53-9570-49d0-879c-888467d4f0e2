package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinExpenseInvoice;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 管理费用业务对象 erp_fin_expense_invoice
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinExpenseInvoice.class, reverseConvertGenerate = false)
public class FinExpenseInvoiceBo extends BaseEntity {

    /**
     * 应付ID
     */
    @NotNull(message = "应付ID不能为空", groups = {EditGroup.class})
    private Long invoiceId;

    /**
     * 应付编号
     */
    private String invoiceCode;

    /**
     * 应付名称
     */
    private String invoiceName;

    /**
     * 收款方类型
     */
    private String payeeType;

    /**
     * 收款方ID
     */
    private Long payeeId;

    /**
     * 收款方编码
     */
    private String payeeCode;

    /**
     * 收款方名称
     */
    private String payeeName;

    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 开票日期
     */
    private LocalDate invoiceDate;

    /**
     * 金额（不含税）
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 金额（含税）
     */
    private BigDecimal amount;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 应付状态
     */
    private String invoiceStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
