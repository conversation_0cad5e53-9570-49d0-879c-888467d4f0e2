package com.iotlaser.spms.core.dict.enums;


import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

import java.io.Serializable;


/**
 * @description: 通用字典枚举
 * @author: wsat
 * @create: 2019-07-04 11:56
 **/
public interface IDictEnum<T extends Serializable> extends IEnum<T> {

    /**
     * 数据库中存储的值
     */
    @JsonValue
    T getValue();


    /**
     * 从数据库保存的字典编码
     */
    String getDictCode();


    /**
     * 从数据库保存的字典名称
     */
    String getDictName();


    /**
     * 从数据库保存的字典描述
     */
    String getDictDesc();

}
