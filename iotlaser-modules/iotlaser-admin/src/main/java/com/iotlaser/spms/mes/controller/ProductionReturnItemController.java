package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.ProductionReturnItemBo;
import com.iotlaser.spms.mes.domain.vo.ProductionReturnItemVo;
import com.iotlaser.spms.mes.service.IProductionReturnItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产退料明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/productionReturnItem")
public class ProductionReturnItemController extends BaseController {

    private final IProductionReturnItemService productionReturnItemService;

    /**
     * 查询生产退料明细列表
     */
    @SaCheckPermission("mes:productionReturnItem:list")
    @GetMapping("/list")
    public TableDataInfo<ProductionReturnItemVo> list(ProductionReturnItemBo bo, PageQuery pageQuery) {
        return productionReturnItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产退料明细列表
     */
    @SaCheckPermission("mes:productionReturnItem:export")
    @Log(title = "生产退料明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductionReturnItemBo bo, HttpServletResponse response) {
        List<ProductionReturnItemVo> list = productionReturnItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产退料明细", ProductionReturnItemVo.class, response);
    }

    /**
     * 获取生产退料明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("mes:productionReturnItem:query")
    @GetMapping("/{itemId}")
    public R<ProductionReturnItemVo> getInfo(@NotNull(message = "主键不能为空")
                                             @PathVariable Long itemId) {
        return R.ok(productionReturnItemService.queryById(itemId));
    }

    /**
     * 新增生产退料明细
     */
    @SaCheckPermission("mes:productionReturnItem:add")
    @Log(title = "生产退料明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductionReturnItemBo bo) {
        return toAjax(productionReturnItemService.insertByBo(bo));
    }

    /**
     * 修改生产退料明细
     */
    @SaCheckPermission("mes:productionReturnItem:edit")
    @Log(title = "生产退料明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductionReturnItemBo bo) {
        return toAjax(productionReturnItemService.updateByBo(bo));
    }

    /**
     * 删除生产退料明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("mes:productionReturnItem:remove")
    @Log(title = "生产退料明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(productionReturnItemService.deleteWithValidByIds(List.of(itemIds), true));
    }

    /**
     * 获取生产退料明细表以及关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:productionReturnItem:query")
    @GetMapping("with/{id}")
    public R<ProductionReturnItemVo> queryByIdWith(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(productionReturnItemService.queryByIdWith(id));
    }

    /**
     * 查询生产退料明细表列表以及关联详细信息
     */
    @SaCheckPermission("mes:productionReturnItem:list")
    @GetMapping("with/list")
    public TableDataInfo<ProductionReturnItemVo> queryPageListWith(ProductionReturnItemBo bo, PageQuery pageQuery) {
        return productionReturnItemService.queryPageListWith(bo, pageQuery);
    }


}
