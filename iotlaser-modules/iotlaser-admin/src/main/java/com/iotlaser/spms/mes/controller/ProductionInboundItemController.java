package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.ProductionInboundItemBo;
import com.iotlaser.spms.mes.domain.vo.ProductionInboundItemVo;
import com.iotlaser.spms.mes.service.IProductionInboundItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产入库明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/productionInboundItem")
public class ProductionInboundItemController extends BaseController {

    private final IProductionInboundItemService productionInboundItemService;

    /**
     * 查询生产入库明细列表
     */
    @SaCheckPermission("mes:productionInboundItem:list")
    @GetMapping("/list")
    public TableDataInfo<ProductionInboundItemVo> list(ProductionInboundItemBo bo, PageQuery pageQuery) {
        return productionInboundItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产入库明细列表
     */
    @SaCheckPermission("mes:productionInboundItem:export")
    @Log(title = "生产入库明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductionInboundItemBo bo, HttpServletResponse response) {
        List<ProductionInboundItemVo> list = productionInboundItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产入库明细", ProductionInboundItemVo.class, response);
    }

    /**
     * 获取生产入库明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("mes:productionInboundItem:query")
    @GetMapping("/{itemId}")
    public R<ProductionInboundItemVo> getInfo(@NotNull(message = "主键不能为空")
                                              @PathVariable Long itemId) {
        return R.ok(productionInboundItemService.queryById(itemId));
    }

    /**
     * 新增生产入库明细
     */
    @SaCheckPermission("mes:productionInboundItem:add")
    @Log(title = "生产入库明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductionInboundItemBo bo) {
        return toAjax(productionInboundItemService.insertByBo(bo));
    }

    /**
     * 修改生产入库明细
     */
    @SaCheckPermission("mes:productionInboundItem:edit")
    @Log(title = "生产入库明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductionInboundItemBo bo) {
        return toAjax(productionInboundItemService.updateByBo(bo));
    }

    /**
     * 删除生产入库明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("mes:productionInboundItem:remove")
    @Log(title = "生产入库明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(productionInboundItemService.deleteWithValidByIds(List.of(itemIds), true));
    }

    /**
     * 获取生产入库明细表以及关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:productionInboundItem:query")
    @GetMapping("with/{id}")
    public R<ProductionInboundItemVo> queryByIdWith(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(productionInboundItemService.queryByIdWith(id));
    }

    /**
     * 查询生产入库明细表列表以及关联详细信息
     */
    @SaCheckPermission("mes:productionInboundItem:list")
    @GetMapping("with/list")
    public TableDataInfo<ProductionInboundItemVo> queryPageListWith(ProductionInboundItemBo bo, PageQuery pageQuery) {
        return productionInboundItemService.queryPageListWith(bo, pageQuery);
    }


}
