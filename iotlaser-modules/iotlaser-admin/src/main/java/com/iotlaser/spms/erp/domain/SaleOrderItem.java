package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售订单明细对象 erp_sale_order_item
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_sale_order_item")
public class SaleOrderItem extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 销售订单明细ID
     */
    @TableId(value = "item_id")
    private Long itemId;

    /**
     * 销售订单ID
     */
    private Long orderId;

    /**
     * 库存ID
     */
    private Long inventoryBatchId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 待完成数量
     */
    private BigDecimal quantity;

    /**
     * 已完成数量
     */
    private BigDecimal finishQuantity;

    /**
     * 已发货数量
     */
    private BigDecimal shippedQuantity;

    /**
     * 已退货数量
     */
    private BigDecimal returnedQuantity;

    /**
     * 含税价格
     */
    private BigDecimal price;

    /**
     * 单价（不含税）
     */
    private BigDecimal priceExclusiveTax;

    /**
     * 金额（含税）
     */
    private BigDecimal amount;

    /**
     * 金额（不含税）
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 已开票数量
     */
    private BigDecimal invoicedQuantity;

    /**
     * 已开票金额
     */
    private BigDecimal invoicedAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 批次
     */
    @TableField(exist = false)
    private List<SaleOrderItemBatch> batches;

}
