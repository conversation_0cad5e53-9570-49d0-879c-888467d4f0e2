package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinExpenseInvoiceBo;
import com.iotlaser.spms.erp.domain.vo.FinExpenseInvoiceVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 管理费用Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
public interface IFinExpenseInvoiceService {

    /**
     * 查询管理费用
     *
     * @param invoiceId 主键
     * @return 管理费用
     */
    FinExpenseInvoiceVo queryById(Long invoiceId);

    /**
     * 分页查询管理费用列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 管理费用分页列表
     */
    TableDataInfo<FinExpenseInvoiceVo> queryPageList(FinExpenseInvoiceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的管理费用列表
     *
     * @param bo 查询条件
     * @return 管理费用列表
     */
    List<FinExpenseInvoiceVo> queryList(FinExpenseInvoiceBo bo);

    /**
     * 新增管理费用
     *
     * @param bo 管理费用
     * @return 是否新增成功
     */
    Boolean insertByBo(FinExpenseInvoiceBo bo);

    /**
     * 修改管理费用
     *
     * @param bo 管理费用
     * @return 是否修改成功
     */
    Boolean updateByBo(FinExpenseInvoiceBo bo);

    /**
     * 校验并批量删除管理费用信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
