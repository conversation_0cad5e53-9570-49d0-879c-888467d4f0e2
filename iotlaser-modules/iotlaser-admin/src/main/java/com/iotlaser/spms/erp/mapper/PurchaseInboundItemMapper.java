package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.PurchaseInboundItem;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 采购入库明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface PurchaseInboundItemMapper extends BaseMapperPlus<PurchaseInboundItem, PurchaseInboundItemVo> {


    /**
     * 查询采购入库明细表及其关联信息
     */
    PurchaseInboundItem queryByIdWith(@Param("itemId") Long itemId);

    /**
     * 分页查询采购入库明细表及其关联信息
     */
    List<PurchaseInboundItem> queryPageListWith(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<PurchaseInboundItem> wrapper);


}
