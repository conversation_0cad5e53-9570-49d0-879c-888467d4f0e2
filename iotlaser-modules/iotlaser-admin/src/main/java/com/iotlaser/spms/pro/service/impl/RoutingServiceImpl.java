package com.iotlaser.spms.pro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.pro.domain.Routing;
import com.iotlaser.spms.pro.domain.bo.RoutingBo;
import com.iotlaser.spms.pro.domain.vo.RoutingVo;
import com.iotlaser.spms.pro.enums.RoutingStatus;
import com.iotlaser.spms.pro.mapper.RoutingMapper;
import com.iotlaser.spms.pro.service.IRoutingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.PRO_ROUTING_CODE;


/**
 * 工艺路线Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RoutingServiceImpl implements IRoutingService {

    private final RoutingMapper baseMapper;
    private final Gen gen;

    /**
     * 查询工艺路线
     *
     * @param routingId 主键
     * @return 工艺路线
     */
    @Override
    public RoutingVo queryById(Long routingId) {
        return baseMapper.selectVoById(routingId);
    }

    /**
     * 分页查询工艺路线列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工艺路线分页列表
     */
    @Override
    public TableDataInfo<RoutingVo> queryPageList(RoutingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Routing> lqw = buildQueryWrapper(bo);
        Page<RoutingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的工艺路线列表
     *
     * @param bo 查询条件
     * @return 工艺路线列表
     */
    @Override
    public List<RoutingVo> queryList(RoutingBo bo) {
        LambdaQueryWrapper<Routing> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Routing> buildQueryWrapper(RoutingBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Routing> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Routing::getRoutingId);
        lqw.eq(StringUtils.isNotBlank(bo.getRoutingCode()), Routing::getRoutingCode, bo.getRoutingCode());
        lqw.like(StringUtils.isNotBlank(bo.getRoutingName()), Routing::getRoutingName, bo.getRoutingName());
        lqw.eq(bo.getProductId() != null, Routing::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), Routing::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), Routing::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, Routing::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), Routing::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), Routing::getUnitName, bo.getUnitName());
        lqw.eq(StringUtils.isNotBlank(bo.getRoutingVersion()), Routing::getRoutingVersion, bo.getRoutingVersion());
        lqw.eq(StringUtils.isNotBlank(bo.getRoutingStatus()), Routing::getRoutingStatus, bo.getRoutingStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Routing::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增工艺路线
     *
     * @param bo 工艺路线
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(RoutingBo bo) {
        if (StringUtils.isEmpty(bo.getRoutingCode())) {
            bo.setRoutingCode(gen.code(PRO_ROUTING_CODE));
        }
        bo.setRoutingStatus(RoutingStatus.DRAFT.getValue());
        Routing add = MapstructUtils.convert(bo, Routing.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRoutingId(add.getRoutingId());
            log.info("新增工艺路线成功，工艺路线编码：{}", add.getRoutingCode());
        }
        return flag;
    }

    /**
     * 修改工艺路线
     *
     * @param bo 工艺路线
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(RoutingBo bo) {
        Routing update = MapstructUtils.convert(bo, Routing.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            log.info("修改工艺路线成功，工艺路线编码：{}", update.getRoutingCode());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Routing entity) {
        // 校验工艺路线编码唯一性
        if (StringUtils.isNotBlank(entity.getRoutingCode())) {
            LambdaQueryWrapper<Routing> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Routing::getRoutingCode, entity.getRoutingCode());
            if (entity.getRoutingId() != null) {
                wrapper.ne(Routing::getRoutingId, entity.getRoutingId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("工艺路线编码已存在：" + entity.getRoutingCode());
            }
        }

        // 校验必填字段
        if (StringUtils.isBlank(entity.getRoutingName())) {
            throw new ServiceException("工艺路线名称不能为空");
        }

        // 校验工艺路线状态（保留核心业务逻辑校验）
        if (StringUtils.isNotBlank(entity.getRoutingStatus())) {
            RoutingStatus routingStatus = RoutingStatus.getByValue(entity.getRoutingStatus());
            if (routingStatus == null) {
                throw new ServiceException("无效的工艺路线状态：" + entity.getRoutingStatus());
            }
        }

        // 启用格式校验（枚举标准化完成后启用）
        if (StringUtils.isNotBlank(entity.getRoutingName()) && entity.getRoutingName().length() > 100) {
            throw new ServiceException("工艺路线名称长度不能超过100个字符");
        }

    }

    /**
     * 校验并批量删除工艺路线信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验工艺路线状态，只有草稿状态的工艺路线才能删除
            List<Routing> routings = baseMapper.selectByIds(ids);
            for (Routing routing : routings) {
                if (!RoutingStatus.DRAFT.getValue().equals(routing.getRoutingStatus())) {
                    throw new ServiceException("工艺路线【" + routing.getRoutingCode() + "】状态为【" +
                        routing.getRoutingStatus() + "】，不允许删除");
                }
                log.info("删除工艺路线，工艺路线编码：{}", routing.getRoutingCode());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
