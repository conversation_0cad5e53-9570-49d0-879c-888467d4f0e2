package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.PurchaseReturnItemBatchBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnItemBatchVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 采购退货批次明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
public interface IPurchaseReturnItemBatchService {

    /**
     * 查询采购退货批次明细
     *
     * @param batchId 主键
     * @return 采购退货批次明细
     */
    PurchaseReturnItemBatchVo queryById(Long batchId);

    /**
     * 分页查询采购退货批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购退货批次明细分页列表
     */
    TableDataInfo<PurchaseReturnItemBatchVo> queryPageList(PurchaseReturnItemBatchBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的采购退货批次明细列表
     *
     * @param bo 查询条件
     * @return 采购退货批次明细列表
     */
    List<PurchaseReturnItemBatchVo> queryList(PurchaseReturnItemBatchBo bo);

    /**
     * 新增采购退货批次明细
     *
     * @param bo 采购退货批次明细
     * @return 是否新增成功
     */
    Boolean insertByBo(PurchaseReturnItemBatchBo bo);

    /**
     * 修改采购退货批次明细
     *
     * @param bo 采购退货批次明细
     * @return 是否修改成功
     */
    Boolean updateByBo(PurchaseReturnItemBatchBo bo);

    /**
     * 校验并批量删除采购退货批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
