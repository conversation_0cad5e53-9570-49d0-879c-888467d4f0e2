package com.iotlaser.spms.pro.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 工艺路线对象 pro_routing
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pro_routing")
public class Routing extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 路线ID
     */
    @TableId(value = "routing_id")
    private Long routingId;

    /**
     * 路线编码
     */
    private String routingCode;

    /**
     * 路线名称
     */
    private String routingName;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 路线版本
     */
    private String routingVersion;

    /**
     * 路线状态
     */
    private String routingStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
