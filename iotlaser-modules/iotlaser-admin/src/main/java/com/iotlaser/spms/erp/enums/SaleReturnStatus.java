package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 销售出库单状态
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Getter
@AllArgsConstructor
public enum SaleReturnStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "退货单已创建，但未通知仓库"),
    AWAITING_RETURN("awaiting_return", "待退回", "已通知客户，等待客户将货物退回"),
    PENDING_WAREHOUSE("pending_warehouse", "待入库", "货物已收到，等待仓库执行入库操作"),
    COMPLETED("completed", "已入库", "仓库已完成退货入库，库存已增加");

    public final static String DICT_CODE = "erp_sale_return_status";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 销售退货状态枚举
     */
    public static SaleReturnStatus getByValue(String value) {
        for (SaleReturnStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }
}
