package com.iotlaser.spms.mes.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生产入库单状态枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Getter
@AllArgsConstructor
public enum ProductionInboundStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "车间报工，待确认"),
    PENDING_WAREHOUSE("pending_warehouse", "待入库", "已通知仓库，等待仓库接收产成品"),
    COMPLETED("completed", "已入库", "仓库已完成入库，产成品库存已增加"),
    CANCELLED("cancelled", "已取消", "入库单已取消");

    public final static String DICT_CODE = "mes_production_inbound_status";
    public final static String DICT_NAME = "生产入库单状态";
    public final static String DICT_DESC = "管理生产完工产品入库单的状态流转，从创建、待入库到完成入库的完整流程";

    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 生产入库状态枚举
     */
    public static ProductionInboundStatus getByValue(String value) {
        for (ProductionInboundStatus inboundStatus : values()) {
            if (inboundStatus.getValue().equals(value)) {
                return inboundStatus;
            }
        }
        return null;
    }

    /**
     * 检查状态是否可以转换
     *
     * @param fromStatus 源状态
     * @param toStatus   目标状态
     * @return 是否可以转换
     */
    public static boolean canTransition(ProductionInboundStatus fromStatus, ProductionInboundStatus toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        switch (fromStatus) {
            case DRAFT:
                return toStatus == PENDING_WAREHOUSE || toStatus == CANCELLED;
            case PENDING_WAREHOUSE:
                return toStatus == COMPLETED || toStatus == CANCELLED;
            case COMPLETED:
            case CANCELLED:
                return false; // 终态，不能再转换
            default:
                return false;
        }
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
