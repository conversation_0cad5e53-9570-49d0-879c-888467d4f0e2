package com.iotlaser.spms.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.common.domain.SourceItem;
import com.iotlaser.spms.common.domain.vo.SourceItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 采购入库明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-04-23
 */
public interface SourceItemMapper extends BaseMapperPlus<SourceItem, SourceItemVo> {

    /**
     * 查询采购入库明细表及其关联信息
     */
    SourceItemVo queryItemByIdWith(@Param("sourceType") String sourceType, @Param("mainTable") String mainTable, @Param("mainId") String mainId, @Param("batchTable") String batchTable, @Param("isJoinBatch") Boolean isJoinBatch, @Param("itemId") Long itemId);

    /**
     * 分页查询采购入库明细表及其关联信息
     */
    List<SourceItemVo> queryItemPageListWith(@Param("sourceType") String sourceType, @Param("mainTable") String mainTable, @Param("mainId") String mainId, @Param("batchTable") String batchTable, @Param("isJoinBatch") Boolean isJoinBatch, @Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<SourceItem> wrapper);

}
