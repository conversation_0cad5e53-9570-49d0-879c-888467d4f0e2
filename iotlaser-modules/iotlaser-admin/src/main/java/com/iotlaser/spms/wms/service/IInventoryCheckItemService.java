package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.wms.domain.bo.InventoryCheckItemBo;
import com.iotlaser.spms.wms.domain.vo.InventoryCheckItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 库存盘点明细Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-15
 */
public interface IInventoryCheckItemService {

    /**
     * 查询库存盘点明细
     *
     * @param itemId 主键
     * @return 库存盘点明细
     */
    InventoryCheckItemVo queryById(Long itemId);

    /**
     * 分页查询库存盘点明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 库存盘点明细分页列表
     */
    TableDataInfo<InventoryCheckItemVo> queryPageList(InventoryCheckItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的库存盘点明细列表
     *
     * @param bo 查询条件
     * @return 库存盘点明细列表
     */
    List<InventoryCheckItemVo> queryList(InventoryCheckItemBo bo);

    /**
     * 新增库存盘点明细
     *
     * @param bo 库存盘点明细
     * @return 是否新增成功
     */
    Boolean insertByBo(InventoryCheckItemBo bo);

    /**
     * 修改库存盘点明细
     *
     * @param bo 库存盘点明细
     * @return 是否修改成功
     */
    Boolean updateByBo(InventoryCheckItemBo bo);

    /**
     * 校验并批量删除库存盘点明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据盘点单ID查询明细ID列表
     *
     * @param checkId 盘点单ID
     * @return 明细ID列表
     */
    List<Long> selectItemIdsByCheckId(Long checkId);

    /**
     * 批量插入或更新库存盘点明细
     *
     * @param items 明细BO集合
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<InventoryCheckItemBo> items);

    /**
     * 根据ID集合删除库存盘点明细
     *
     * @param ids ID集合
     * @return 是否删除成功
     */
    Boolean deleteByIds(Collection<Long> ids);

    /**
     * 根据盘点单ID查询明细列表
     * ✅ 修正：返回VO而非Entity
     *
     * @param checkId 盘点单ID
     * @return 明细VO列表
     */
    List<InventoryCheckItemVo> selectListByCheckId(Long checkId);
}
