package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.SaleOutboundItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 销售出库明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
public interface ISaleOutboundItemService {

    /**
     * 查询销售出库明细
     *
     * @param itemId 主键
     * @return 销售出库明细
     */
    SaleOutboundItemVo queryById(Long itemId);

    /**
     * 分页查询销售出库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售出库明细分页列表
     */
    TableDataInfo<SaleOutboundItemVo> queryPageList(SaleOutboundItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的销售出库明细列表
     *
     * @param bo 查询条件
     * @return 销售出库明细列表
     */
    List<SaleOutboundItemVo> queryList(SaleOutboundItemBo bo);

    /**
     * 新增销售出库明细
     *
     * @param bo 销售出库明细
     * @return 是否新增成功
     */
    Boolean insertByBo(SaleOutboundItemBo bo);

    /**
     * 修改销售出库明细
     *
     * @param bo 销售出库明细
     * @return 是否修改成功
     */
    Boolean updateByBo(SaleOutboundItemBo bo);

    /**
     * 校验并批量删除销售出库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
