package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存状态
 *
 * <AUTHOR>
 * @date 2025/5/20
 */
@Getter
@AllArgsConstructor
public enum InventoryBatchStatus implements IDictEnum<String> {
    AVAILABLE("available", "可用", "库存可用状态"),
    QUARANTINE("quarantine", "待检", "库存隔离待检状态"),
    PENDING_INSPECTION("pending_inspection", "待检验", "库存待质检状态"),
    BLOCKED("blocked", "冻结", "库存冻结状态"),
    FROZEN("frozen", "冻结", "库存冻结状态"),
    RESERVED("reserved", "预留", "库存预留状态"),
    IN_TRANSIT("in_transit", "在途", "库存在途状态"),
    WARNING("warning", "预警", "库存预警状态"),
    EXPIRED("expired", "过期", "库存过期状态");

    public final static String DICT_CODE = "wms_inventory_batch_status";
    public final static String DICT_NAME = "库存批次状态";
    public final static String DICT_DESC = "定义库存批次的状态，包括可用、待检、冻结、预留等不同的库存状态";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态值获取枚举
     *
     * @param value 状态值
     * @return 库存批次状态枚举
     */
    public static InventoryBatchStatus getByValue(String value) {
        for (InventoryBatchStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }


}
