package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.SaleOrder;
import com.iotlaser.spms.erp.enums.SaleOrderStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 销售订单业务对象 erp_sale_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SaleOrder.class, reverseConvertGenerate = false)
public class SaleOrderBo extends BaseEntity {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = {EditGroup.class})
    private Long orderId;

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空", groups = {EditGroup.class})
    private String orderCode;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 下单日期
     */
    private LocalDate orderDate;

    /**
     * 订单状态
     */
    private SaleOrderStatus orderStatus;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人
     */
    private String applicantName;

    /**
     * 销售员ID
     */
    private Long handlerId;

    /**
     * 销售员
     */
    private String handlerName;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审批人
     */
    private String approverName;

    /**
     * 审批通过时间
     */
    private LocalDateTime approveTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 明细
     */
    private List<SaleOrderItemBo> items;

    // ==================== 临时变量：汇总字段 ====================
    // TODO: 待数据库结构完善后，这些字段应该持久化到数据库

    /**
     * 总数量（临时变量）
     */
    private BigDecimal totalQuantity;

    /**
     * 总金额-含税（临时变量）
     */
    private BigDecimal totalAmount;

    /**
     * 总金额-不含税（临时变量）
     */
    private BigDecimal totalAmountExclusiveTax;

    /**
     * 总税额（临时变量）
     */
    private BigDecimal totalTaxAmount;
}
