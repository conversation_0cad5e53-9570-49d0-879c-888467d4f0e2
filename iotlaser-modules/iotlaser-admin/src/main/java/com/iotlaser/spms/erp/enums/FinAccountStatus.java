package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账户状态枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinAccountStatus implements IDictEnum<String> {

    ACTIVE("active", "正常", "账户正常使用"),
    FROZEN("frozen", "冻结", "账户被冻结，暂停使用"),
    SUSPENDED("suspended", "暂停", "账户暂停使用"),
    CLOSED("closed", "关闭", "账户已关闭"),
    PENDING_APPROVAL("pending_approval", "待审批", "账户等待审批开通"),
    UNDER_REVIEW("under_review", "审核中", "账户信息审核中"),
    RESTRICTED("restricted", "受限", "账户使用受限"),
    DORMANT("dormant", "休眠", "账户长期未使用");

    public final static String DICT_CODE = "erp_fin_account_status";
    public final static String DICT_NAME = "财务账户状态";
    public final static String DICT_DESC = "管理财务账户的使用状态，包括正常、冻结、暂停、关闭等账户管理状态";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 账户状态枚举
     */
    public static FinAccountStatus getByValue(String value) {
        for (FinAccountStatus accountStatus : values()) {
            if (accountStatus.getValue().equals(value)) {
                return accountStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否可以进行交易
     *
     * @return 是否可以进行交易
     */
    public boolean canTransact() {
        return this == ACTIVE;
    }

    /**
     * 判断是否可以查询余额
     *
     * @return 是否可以查询余额
     */
    public boolean canQueryBalance() {
        return this == ACTIVE || this == FROZEN || this == SUSPENDED || this == RESTRICTED;
    }

    /**
     * 判断是否可以修改信息
     *
     * @return 是否可以修改信息
     */
    public boolean canModify() {
        return this == ACTIVE || this == PENDING_APPROVAL || this == UNDER_REVIEW;
    }

    /**
     * 判断是否为临时状态
     *
     * @return 是否为临时状态
     */
    public boolean isTemporary() {
        return this == PENDING_APPROVAL || this == UNDER_REVIEW || this == SUSPENDED;
    }

    /**
     * 判断是否为最终状态
     *
     * @return 是否为最终状态
     */
    public boolean isFinal() {
        return this == CLOSED;
    }

    /**
     * 获取下一个可能的状态
     *
     * @return 下一个可能的状态列表
     */
    public FinAccountStatus[] getNextPossibleStates() {
        switch (this) {
            case PENDING_APPROVAL:
                return new FinAccountStatus[]{ACTIVE, UNDER_REVIEW, CLOSED};
            case UNDER_REVIEW:
                return new FinAccountStatus[]{ACTIVE, PENDING_APPROVAL, CLOSED};
            case ACTIVE:
                return new FinAccountStatus[]{FROZEN, SUSPENDED, RESTRICTED, DORMANT, CLOSED};
            case FROZEN:
                return new FinAccountStatus[]{ACTIVE, CLOSED};
            case SUSPENDED:
                return new FinAccountStatus[]{ACTIVE, FROZEN, CLOSED};
            case RESTRICTED:
                return new FinAccountStatus[]{ACTIVE, FROZEN, CLOSED};
            case DORMANT:
                return new FinAccountStatus[]{ACTIVE, CLOSED};
            case CLOSED:
            default:
                return new FinAccountStatus[]{};
        }
    }
}
