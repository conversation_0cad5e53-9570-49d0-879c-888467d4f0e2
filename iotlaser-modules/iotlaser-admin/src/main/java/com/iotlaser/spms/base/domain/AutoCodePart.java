package com.iotlaser.spms.base.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.base.enums.PartType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 编码生成规则组成对象 base_auto_code_part
 *
 * <AUTHOR> Kai
 * @date 2025/03/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("base_auto_code_part")
public class AutoCodePart extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分段ID
     */
    @TableId(value = "part_id")
    private Long partId;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 分段序号
     */
    private Long partIndex;

    /**
     * 分段类型
     */
    private PartType partType;

    /**
     * 分段编号
     */
    private String partCode;

    /**
     * 分段名称
     */
    private String partName;

    /**
     * 分段长度
     */
    private Long partLength;

    /**
     * 时间格式
     */
    private String dateFormat;

    /**
     * 输入字符
     */
    private String inputCharacter;

    /**
     * 固定字符
     */
    private String fixCharacter;

    /**
     * 流水号起始值
     */
    private Long seriaStartNo;

    /**
     * 流水号步长
     */
    private Long seriaStep;

    /**
     * 流水号当前值
     */
    private Long seriaNowNo;

    /**
     * 流水号是否循环
     */
    private String cycleFlag;

    /**
     * 循环方式
     */
    private String cycleMethod;

    /**
     * 备注
     */
    private String remark;

}
