package com.iotlaser.spms.pro.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * BOM物料库存检查Service接口
 * <p>
 * 功能：
 * 1. BOM物料库存查询
 * 2. 物料缺口分析
 * 3. 补料建议生成
 * 4. 开工前物料检查
 *
 * <AUTHOR> <PERSON>
 * @date 2025/06/16
 */
public interface IBomInventoryCheckService {

    /**
     * 检查BOM物料库存情况
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 生产数量
     * @return BOM物料库存检查结果
     */
    Map<String, Object> checkBomInventory(Long bomId, BigDecimal requiredQuantity);

    /**
     * 检查产品BOM物料库存情况
     *
     * @param productId        产品ID
     * @param requiredQuantity 生产数量
     * @return BOM物料库存检查结果
     */
    Map<String, Object> checkProductBomInventory(Long productId, BigDecimal requiredQuantity);

    /**
     * 获取BOM物料库存详情列表
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 生产数量
     * @return 物料库存详情列表
     */
    List<Map<String, Object>> getBomMaterialInventoryDetails(Long bomId, BigDecimal requiredQuantity);

    /**
     * 获取物料缺口分析
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 生产数量
     * @return 物料缺口分析结果
     */
    Map<String, Object> getMaterialShortageAnalysis(Long bomId, BigDecimal requiredQuantity);

    /**
     * 生成补料建议
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 生产数量
     * @return 补料建议列表
     */
    List<Map<String, Object>> generateMaterialReplenishmentSuggestions(Long bomId, BigDecimal requiredQuantity);

    /**
     * 检查是否可以开工
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 生产数量
     * @return 开工检查结果
     */
    Map<String, Object> checkCanStartProduction(Long bomId, BigDecimal requiredQuantity);

    /**
     * 批量检查多个BOM的物料库存
     *
     * @param bomCheckRequests BOM检查请求列表
     * @return 批量检查结果
     */
    List<Map<String, Object>> batchCheckBomInventory(List<Map<String, Object>> bomCheckRequests);

    /**
     * 获取物料库存预警信息
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 生产数量
     * @return 库存预警信息
     */
    Map<String, Object> getMaterialInventoryAlerts(Long bomId, BigDecimal requiredQuantity);
}
