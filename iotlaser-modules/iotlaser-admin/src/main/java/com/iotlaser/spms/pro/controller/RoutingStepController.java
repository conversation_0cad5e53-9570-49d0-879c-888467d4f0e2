package com.iotlaser.spms.pro.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.pro.domain.bo.RoutingStepBo;
import com.iotlaser.spms.pro.domain.vo.RoutingStepVo;
import com.iotlaser.spms.pro.service.IRoutingStepService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工艺路线工序
 *
 * <AUTHOR> Kai
 * @date 2025-06-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/pro/routingStep")
public class RoutingStepController extends BaseController {

    private final IRoutingStepService routingStepService;

    /**
     * 查询工艺路线工序列表
     */
    @SaCheckPermission("pro:routingStep:list")
    @GetMapping("/list")
    public TableDataInfo<RoutingStepVo> list(RoutingStepBo bo, PageQuery pageQuery) {
        return routingStepService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出工艺路线工序列表
     */
    @SaCheckPermission("pro:routingStep:export")
    @Log(title = "工艺路线工序", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(RoutingStepBo bo, HttpServletResponse response) {
        List<RoutingStepVo> list = routingStepService.queryList(bo);
        ExcelUtil.exportExcel(list, "工艺路线工序", RoutingStepVo.class, response);
    }

    /**
     * 获取工艺路线工序详细信息
     *
     * @param stepId 主键
     */
    @SaCheckPermission("pro:routingStep:query")
    @GetMapping("/{stepId}")
    public R<RoutingStepVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long stepId) {
        return R.ok(routingStepService.queryById(stepId));
    }

    /**
     * 新增工艺路线工序
     */
    @SaCheckPermission("pro:routingStep:add")
    @Log(title = "工艺路线工序", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody RoutingStepBo bo) {
        return toAjax(routingStepService.insertByBo(bo));
    }

    /**
     * 修改工艺路线工序
     */
    @SaCheckPermission("pro:routingStep:edit")
    @Log(title = "工艺路线工序", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody RoutingStepBo bo) {
        return toAjax(routingStepService.updateByBo(bo));
    }

    /**
     * 删除工艺路线工序
     *
     * @param stepIds 主键串
     */
    @SaCheckPermission("pro:routingStep:remove")
    @Log(title = "工艺路线工序", businessType = BusinessType.DELETE)
    @DeleteMapping("/{stepIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] stepIds) {
        return toAjax(routingStepService.deleteWithValidByIds(List.of(stepIds), true));
    }
}
