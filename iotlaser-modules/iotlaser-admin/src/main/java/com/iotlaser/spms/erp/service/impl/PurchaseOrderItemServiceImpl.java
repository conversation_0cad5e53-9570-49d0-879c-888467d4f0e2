package com.iotlaser.spms.erp.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.PurchaseOrder;
import com.iotlaser.spms.erp.domain.PurchaseOrderItem;
import com.iotlaser.spms.erp.domain.bo.PurchaseOrderItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderItemVo;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.mapper.PurchaseOrderItemMapper;
import com.iotlaser.spms.erp.mapper.PurchaseOrderMapper;
import com.iotlaser.spms.erp.service.IPurchaseOrderItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 采购订单明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseOrderItemServiceImpl implements IPurchaseOrderItemService {

    private final PurchaseOrderItemMapper baseMapper;
    private final PurchaseOrderMapper purchaseOrderMapper;

    /**
     * 查询采购订单明细
     *
     * @param itemId 主键
     * @return 采购订单明细
     */
    @Override
    public PurchaseOrderItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询采购订单明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购订单明细分页列表
     */
    @Override
    public TableDataInfo<PurchaseOrderItemVo> queryPageList(PurchaseOrderItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseOrderItem> lqw = buildQueryWrapper(bo);
        Page<PurchaseOrderItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购订单明细列表
     *
     * @param bo 查询条件
     * @return 采购订单明细列表
     */
    @Override
    public List<PurchaseOrderItemVo> queryList(PurchaseOrderItemBo bo) {
        LambdaQueryWrapper<PurchaseOrderItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseOrderItem> buildQueryWrapper(PurchaseOrderItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseOrderItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseOrderItem::getItemId);
        lqw.eq(bo.getOrderId() != null, PurchaseOrderItem::getOrderId, bo.getOrderId());
        lqw.eq(bo.getProductId() != null, PurchaseOrderItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), PurchaseOrderItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), PurchaseOrderItem::getProductName, bo.getProductName());
        // ✅ 优化：移除数量的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, PurchaseOrderItem::getQuantity, bo.getQuantity());
        // TODO: 如需要可以后续添加数量范围查询支持
        // ✅ 优化：移除价格的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getPrice() != null, PurchaseOrderItem::getPrice, bo.getPrice());
        // TODO: 如需要可以后续添加价格范围查询支持
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseOrderItem::getStatus, bo.getStatus());
        lqw.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), PurchaseOrderItem::getProductId, StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        return lqw;
    }

    /**
     * 新增采购订单明细
     *
     * @param bo 采购订单明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PurchaseOrderItemBo bo) {
        // 计算价税分离字段
        calculateTaxFields(bo);

        PurchaseOrderItem add = MapstructUtils.convert(bo, PurchaseOrderItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PurchaseOrderItem entity) {
        // 校验同一订单中产品不能重复
        if (entity.getOrderId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<PurchaseOrderItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PurchaseOrderItem::getOrderId, entity.getOrderId());
            wrapper.eq(PurchaseOrderItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(PurchaseOrderItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一采购订单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除采购订单明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验采购订单明细是否可以删除
            List<PurchaseOrderItem> items = baseMapper.selectByIds(ids);
            for (PurchaseOrderItem item : items) {
                // 1. 检查关联的采购订单状态
                PurchaseOrder order = purchaseOrderMapper.selectById(item.getOrderId());
                if (order != null && PurchaseOrderStatus.DRAFT != order.getOrderStatus()) {
                    throw new ServiceException("采购订单明细所属订单【" + order.getOrderName() + "】状态为【" +
                        order.getOrderStatus() + "】，不允许删除明细");
                }

                // 2. 检查是否已有收货记录
                if (item.getReceivedQuantity() != null && item.getReceivedQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    throw new ServiceException("采购订单明细【" + item.getProductName() + "】已有收货记录，不允许删除");
                }

                log.info("删除采购订单明细校验通过：产品【{}】", item.getProductName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除采购订单明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除采购订单明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除采购订单明细失败：" + e.getMessage());
        }
    }


    /**
     * 查询采购订单明细表及其关联信息
     *
     * @param itemId 主键
     * @return 采购订单明细表
     */
    @Override
    public PurchaseOrderItemVo queryByIdWith(Long itemId) {
        return baseMapper.queryByIdWith(itemId);
    }

    /**
     * 分页查询采购订单明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购订单明细表分页列表
     */
    @Override
    public TableDataInfo<PurchaseOrderItemVo> queryPageListWith(PurchaseOrderItemBo bo, PageQuery pageQuery) {
        QueryWrapper<PurchaseOrderItem> queryWrapper = buildQueryWrapperWith(bo);
        List<PurchaseOrderItemVo> result = baseMapper.queryPageListWith(pageQuery.build(), queryWrapper);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<PurchaseOrderItem> buildQueryWrapperWith(PurchaseOrderItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<PurchaseOrderItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getOrderId() != null, "item.order_id", bo.getOrderId());
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        // ✅ 优化：移除数量和价格的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        // 原代码：wrapper.eq(bo.getPrice() != null, "item.price", bo.getPrice());
        // TODO: 如需要可以后续添加数量和价格的范围查询支持
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        wrapper.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), "item.product_id", StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        return wrapper;
    }


    /**
     * 批量新增或更新采购订单明细
     *
     * @param items 采购订单明细列表
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<PurchaseOrderItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }
        List<PurchaseOrderItem> entities = MapstructUtils.convert(items, PurchaseOrderItem.class);
        return baseMapper.insertOrUpdateBatch(entities);
    }

    /**
     * 根据订单ID查询明细列表
     *
     * @param orderId 订单ID
     * @return 明细列表
     */
    @Override
    public List<PurchaseOrderItemVo> queryByOrderId(Long orderId) {
        LambdaQueryWrapper<PurchaseOrderItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PurchaseOrderItem::getOrderId, orderId);
        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 修改采购订单明细
     *
     * @param bo 采购订单明细
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(PurchaseOrderItemBo bo) {
        if (bo == null || bo.getItemId() == null) {
            return false;
        }

        try {
            // 重新计算价税分离字段
            calculateTaxFields(bo);

            PurchaseOrderItem item = MapstructUtils.convert(bo, PurchaseOrderItem.class);
            // 验证实体
            validEntityBeforeSave(item);

            int result = baseMapper.updateById(item);
            return result > 0;
        } catch (Exception e) {
            throw new ServiceException("更新采购订单明细失败：" + e.getMessage());
        }
    }

    /**
     * 根据订单ID查询明细列表（内部使用）
     * ✅ 修正：返回VO而非Entity
     *
     * @param orderId 订单ID
     * @return 明细VO列表
     */
    @Override
    public List<PurchaseOrderItemVo> selectListByOrderId(Long orderId) {
        LambdaQueryWrapper<PurchaseOrderItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PurchaseOrderItem::getOrderId, orderId);
        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 根据BO更新明细Entity（兼容性方法）
     * ✅ 修正：使用BO进行更新，内部转换为Entity
     *
     * @param bo 明细Bo
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBoEntity(PurchaseOrderItemBo bo) {
        if (bo == null || bo.getItemId() == null) {
            return false;
        }

        try {
            PurchaseOrderItem item = MapstructUtils.convert(bo, PurchaseOrderItem.class);
            // 验证实体
            validEntityBeforeSave(item);

            int result = baseMapper.updateById(item);
            return result > 0;
        } catch (Exception e) {
            throw new ServiceException("更新采购订单明细失败：" + e.getMessage());
        }
    }

    /**
     * 计算价税分离字段
     *
     * @param bo 业务对象
     */
    private void calculateTaxFields(PurchaseOrderItemBo bo) {
        // 校验必要字段
        if (bo.getQuantity() == null || bo.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("数量必须大于0");
        }

        if (bo.getTaxRate() == null) {
            bo.setTaxRate(BigDecimal.ZERO); // 默认税率为0
        }

        // 校验税率范围 (0-100%)
        if (bo.getTaxRate().compareTo(BigDecimal.ZERO) < 0 ||
            bo.getTaxRate().compareTo(new BigDecimal("100")) > 0) {
            throw new ServiceException("税率必须在0-100%之间");
        }

        // 根据输入的字段计算其他字段
        if (bo.getPriceExclusiveTax() != null) {
            // 基于单价（不含税）计算
            calculateFromPriceExclusiveTax(bo);
        } else if (bo.getPrice() != null) {
            // 基于单价（含税）计算
            calculateFromPrice(bo);
        } else if (bo.getAmountExclusiveTax() != null) {
            // 基于金额（不含税）计算
            calculateFromAmountExclusiveTax(bo);
        } else if (bo.getAmount() != null) {
            // 基于金额（含税）计算
            calculateFromTotalAmount(bo);
        } else {
            throw new ServiceException("必须提供单价或金额信息");
        }
    }

    /**
     * 基于单价（不含税）计算其他字段
     */
    private void calculateFromPriceExclusiveTax(PurchaseOrderItemBo bo) {
        BigDecimal priceExclusiveTax = bo.getPriceExclusiveTax();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算金额（不含税） = 单价（不含税） × 数量
        BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(quantity)
            .setScale(2, RoundingMode.HALF_UP);
        bo.setAmountExclusiveTax(amountExclusiveTax);

        // 计算税额 = 金额（不含税） × 税率
        BigDecimal taxAmount = amountExclusiveTax.multiply(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP))
            .setScale(2, RoundingMode.HALF_UP);
        bo.setTaxAmount(taxAmount);

        // 计算单价（含税） = 单价（不含税） × (1 + 税率)
        BigDecimal price = priceExclusiveTax.multiply(BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)))
            .setScale(2, RoundingMode.HALF_UP);
        bo.setPrice(price);

        // 计算金额（含税） = 金额（不含税） + 税额
        BigDecimal amount = amountExclusiveTax.add(taxAmount);
        bo.setAmount(amount);
    }

    /**
     * 基于单价（含税）计算其他字段
     */
    private void calculateFromPrice(PurchaseOrderItemBo bo) {
        BigDecimal price = bo.getPrice();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算金额（含税） = 单价（含税） × 数量
        BigDecimal amount = price.multiply(quantity)
            .setScale(2, RoundingMode.HALF_UP);
        bo.setAmount(amount);

        // 计算单价（不含税） = 单价（含税） ÷ (1 + 税率)
        BigDecimal priceExclusiveTax = price.divide(BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)), 2, RoundingMode.HALF_UP);
        bo.setPriceExclusiveTax(priceExclusiveTax);

        // 计算金额（不含税） = 单价（不含税） × 数量
        BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(quantity)
            .setScale(2, RoundingMode.HALF_UP);
        bo.setAmountExclusiveTax(amountExclusiveTax);

        // 计算税额 = 金额（含税） - 金额（不含税）
        BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        bo.setTaxAmount(taxAmount);
    }

    /**
     * 基于金额（不含税）计算其他字段
     */
    private void calculateFromAmountExclusiveTax(PurchaseOrderItemBo bo) {
        BigDecimal amountExclusiveTax = bo.getAmountExclusiveTax();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算单价（不含税） = 金额（不含税） ÷ 数量
        BigDecimal priceExclusiveTax = amountExclusiveTax.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPriceExclusiveTax(priceExclusiveTax);

        // 计算税额 = 金额（不含税） × 税率
        BigDecimal taxAmount = amountExclusiveTax.multiply(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP))
            .setScale(2, RoundingMode.HALF_UP);
        bo.setTaxAmount(taxAmount);

        // 计算金额（含税） = 金额（不含税） + 税额
        BigDecimal amount = amountExclusiveTax.add(taxAmount);
        bo.setAmount(amount);

        // 计算单价（含税） = 金额（含税） ÷ 数量
        BigDecimal price = amount.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPrice(price);
    }

    /**
     * 基于金额（含税）计算其他字段
     */
    private void calculateFromTotalAmount(PurchaseOrderItemBo bo) {
        BigDecimal amount = bo.getAmount();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算单价（含税） = 金额（含税） ÷ 数量
        BigDecimal price = amount.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPrice(price);

        // 计算金额（不含税） = 金额（含税） ÷ (1 + 税率)
        BigDecimal amountExclusiveTax = amount.divide(BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)), 2, RoundingMode.HALF_UP);
        bo.setAmountExclusiveTax(amountExclusiveTax);

        // 计算单价（不含税） = 金额（不含税） ÷ 数量
        BigDecimal priceExclusiveTax = amountExclusiveTax.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPriceExclusiveTax(priceExclusiveTax);

        // 计算税额 = 金额（含税） - 金额（不含税）
        BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        bo.setTaxAmount(taxAmount);
    }

}
