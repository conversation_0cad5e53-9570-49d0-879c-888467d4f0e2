package com.iotlaser.spms.base.domain.bo;

import com.iotlaser.spms.base.domain.AutoCodeResult;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 编码生成记录业务对象 sys_auto_code_result
 *
 * <AUTHOR>
 * @date 2025/03/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AutoCodeResult.class, reverseConvertGenerate = false)
public class AutoCodeResultBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long codeId;

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long ruleId;

    /**
     * 生成日期时间
     */
    @NotBlank(message = "生成日期时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private String genDate;

    /**
     * 最后产生的序号
     */
    private Long genIndex;

    /**
     * 最后产生的值
     */
    private String lastResult;

    /**
     * 最后产生的流水号
     */
    private Long lastSerialNo;

    /**
     * 最后传入的参数
     */
    private String lastInputChar;

    /**
     * 备注
     */
    private String remark;

}
