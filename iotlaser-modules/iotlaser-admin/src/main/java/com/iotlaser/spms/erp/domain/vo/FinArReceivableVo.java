package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinArReceivable;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 应收单视图对象 erp_fin_ar_receivable
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinArReceivable.class)
public class FinArReceivableVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 应收ID
     */
    @ExcelProperty(value = "应收ID")
    private Long receivableId;

    /**
     * 应收编号
     */
    @ExcelProperty(value = "应收编号")
    private String receivableCode;

    /**
     * 应收名称
     */
    @ExcelProperty(value = "应收名称")
    private String receivableName;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 客户编码
     */
    @ExcelProperty(value = "客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String customerName;

    /**
     * 上游来源ID
     */
    @ExcelProperty(value = "上游来源ID")
    private Long directSourceId;

    /**
     * 上游来源编号
     */
    @ExcelProperty(value = "上游来源编号")
    private String directSourceCode;

    /**
     * 上游来源名称
     */
    @ExcelProperty(value = "上游来源名称")
    private String directSourceName;

    /**
     * 上游来源类型
     */
    @ExcelProperty(value = "上游来源类型")
    private String directSourceType;

    /**
     * 来源ID
     */
    @ExcelProperty(value = "来源ID")
    private Long sourceId;

    /**
     * 来源编号
     */
    @ExcelProperty(value = "来源编号")
    private String sourceCode;

    /**
     * 来源名称
     */
    @ExcelProperty(value = "来源名称")
    private String sourceName;

    /**
     * 来源类型
     */
    @ExcelProperty(value = "来源类型")
    private String sourceType;

    /**
     * 发票号码
     */
    @ExcelProperty(value = "发票号码")
    private String invoiceNumber;

    /**
     * 开票日期
     */
    @ExcelProperty(value = "开票日期")
    private LocalDate invoiceDate;

    /**
     * 金额（不含税）
     */
    @ExcelProperty(value = "金额（不含税）")
    private BigDecimal amountExclusiveTax;

    /**
     * 总税额
     */
    @ExcelProperty(value = "总税额")
    private BigDecimal taxAmount;

    /**
     * 金额（含税）
     */
    @ExcelProperty(value = "金额（含税）")
    private BigDecimal amount;

    /**
     * 应收状态
     */
    @ExcelProperty(value = "应收状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_fin_ar_receivable_status")
    private String receivableStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
