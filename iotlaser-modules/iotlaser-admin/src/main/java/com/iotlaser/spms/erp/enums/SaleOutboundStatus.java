package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 销售出库单状态枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-20
 */
@Getter
@AllArgsConstructor
public enum SaleOutboundStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "已创建"),
    PENDING_WAREHOUSE("pending_warehouse", "待出库", "等待出库"),
    COMPLETED("completed", "已出库", "已完成出库"),
    CANCELLED("cancelled", "已取消", "已取消");

    public final static String DICT_CODE = "erp_sale_outbound_status";
    public final static String DICT_NAME = "销售出库状态";
    public final static String DICT_DESC = "管理销售出库单的流程状态，从草稿创建到出库完成的完整流程";
    @EnumValue
    private final String value;
    private final String name;
    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static SaleOutboundStatus getByValue(String value) {
        for (SaleOutboundStatus outboundStatus : values()) {
            if (outboundStatus.getValue().equals(value)) {
                return outboundStatus;
            }
        }
        return null;
    }

    /**
     * 检查状态是否可以转换
     */
    public static boolean canTransition(SaleOutboundStatus fromStatus, SaleOutboundStatus toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        switch (fromStatus) {
            case DRAFT:
                return toStatus == PENDING_WAREHOUSE || toStatus == CANCELLED;
            case PENDING_WAREHOUSE:
                return toStatus == COMPLETED || toStatus == CANCELLED;
            case COMPLETED:
            case CANCELLED:
                return false; // 终态，不能再转换
            default:
                return false;
        }
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
