package com.iotlaser.spms.base.domain.bo;

import com.iotlaser.spms.base.domain.AutoCodePart;
import com.iotlaser.spms.base.enums.PartType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 编码生成规则组成业务对象 sys_auto_code_part
 *
 * <AUTHOR> <PERSON>
 * @date 2025/03/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AutoCodePart.class, reverseConvertGenerate = false)
public class AutoCodePartBo extends BaseEntity {

    /**
     * 分段ID
     */
    @NotNull(message = "分段ID不能为空", groups = {EditGroup.class})
    private Long partId;

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long ruleId;

    /**
     * 分段序号
     */
    @NotNull(message = "分段序号不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long partIndex;

    /**
     * 分段类型
     */
    @NotBlank(message = "分段类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private PartType partType;

    /**
     * 分段编号
     */
    private String partCode;

    /**
     * 分段名称
     */
    private String partName;

    /**
     * 分段长度
     */
    @NotNull(message = "分段长度不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long partLength;

    /**
     * 时间格式
     */
    private String dateFormat;

    /**
     * 输入字符
     */
    private String inputCharacter;

    /**
     * 固定字符
     */
    private String fixCharacter;

    /**
     * 流水号起始值
     */
    private Long seriaStartNo;

    /**
     * 流水号步长
     */
    private Long seriaStep;

    /**
     * 流水号当前值
     */
    private Long seriaNowNo;

    /**
     * 流水号是否循环
     */
    private String cycleFlag;

    /**
     * 循环方式
     */
    private String cycleMethod;

    /**
     * 备注
     */
    private String remark;

}
