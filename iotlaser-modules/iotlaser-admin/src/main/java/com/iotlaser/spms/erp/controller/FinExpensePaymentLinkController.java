package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinExpensePaymentLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinExpensePaymentLinkVo;
import com.iotlaser.spms.erp.service.IFinExpensePaymentLinkService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理费用与付款单核销关系
 *
 * <AUTHOR> Kai
 * @date 2025-06-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finExpensePaymentLink")
public class FinExpensePaymentLinkController extends BaseController {

    private final IFinExpensePaymentLinkService finExpensePaymentLinkService;

    /**
     * 查询管理费用与付款单核销关系列表
     */
    @SaCheckPermission("spms/erp:finExpensePaymentLink:list")
    @GetMapping("/list")
    public TableDataInfo<FinExpensePaymentLinkVo> list(FinExpensePaymentLinkBo bo, PageQuery pageQuery) {
        return finExpensePaymentLinkService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出管理费用与付款单核销关系列表
     */
    @SaCheckPermission("spms/erp:finExpensePaymentLink:export")
    @Log(title = "管理费用与付款单核销关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinExpensePaymentLinkBo bo, HttpServletResponse response) {
        List<FinExpensePaymentLinkVo> list = finExpensePaymentLinkService.queryList(bo);
        ExcelUtil.exportExcel(list, "管理费用与付款单核销关系", FinExpensePaymentLinkVo.class, response);
    }

    /**
     * 获取管理费用与付款单核销关系详细信息
     *
     * @param linkId 主键
     */
    @SaCheckPermission("spms/erp:finExpensePaymentLink:query")
    @GetMapping("/{linkId}")
    public R<FinExpensePaymentLinkVo> getInfo(@NotNull(message = "主键不能为空")
                                              @PathVariable Long linkId) {
        return R.ok(finExpensePaymentLinkService.queryById(linkId));
    }

    /**
     * 新增管理费用与付款单核销关系
     */
    @SaCheckPermission("spms/erp:finExpensePaymentLink:add")
    @Log(title = "管理费用与付款单核销关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinExpensePaymentLinkBo bo) {
        return toAjax(finExpensePaymentLinkService.insertByBo(bo));
    }

    /**
     * 修改管理费用与付款单核销关系
     */
    @SaCheckPermission("spms/erp:finExpensePaymentLink:edit")
    @Log(title = "管理费用与付款单核销关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinExpensePaymentLinkBo bo) {
        return toAjax(finExpensePaymentLinkService.updateByBo(bo));
    }

    /**
     * 删除管理费用与付款单核销关系
     *
     * @param linkIds 主键串
     */
    @SaCheckPermission("spms/erp:finExpensePaymentLink:remove")
    @Log(title = "管理费用与付款单核销关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{linkIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] linkIds) {
        return toAjax(finExpensePaymentLinkService.deleteWithValidByIds(List.of(linkIds), true));
    }
}
