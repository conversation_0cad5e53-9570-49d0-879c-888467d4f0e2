package com.iotlaser.spms.mes.service;

import com.iotlaser.spms.mes.domain.bo.ProductionReturnItemBo;
import com.iotlaser.spms.mes.domain.vo.ProductionReturnItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 生产退料明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
public interface IProductionReturnItemService {

    /**
     * 查询生产退料明细
     *
     * @param itemId 主键
     * @return 生产退料明细
     */
    ProductionReturnItemVo queryById(Long itemId);

    /**
     * 分页查询生产退料明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产退料明细分页列表
     */
    TableDataInfo<ProductionReturnItemVo> queryPageList(ProductionReturnItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的生产退料明细列表
     *
     * @param bo 查询条件
     * @return 生产退料明细列表
     */
    List<ProductionReturnItemVo> queryList(ProductionReturnItemBo bo);

    /**
     * 新增生产退料明细
     *
     * @param bo 生产退料明细
     * @return 是否新增成功
     */
    Boolean insertByBo(ProductionReturnItemBo bo);

    /**
     * 修改生产退料明细
     *
     * @param bo 生产退料明细
     * @return 是否修改成功
     */
    Boolean updateByBo(ProductionReturnItemBo bo);

    /**
     * 校验并批量删除生产退料明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询生产退料明细表及其关联信息
     *
     * @param itemId 主键
     * @return 生产退料明细表
     */
    ProductionReturnItemVo queryByIdWith(Long itemId);

    /**
     * 分页查询生产退料明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产退料明细表分页列表
     */
    TableDataInfo<ProductionReturnItemVo> queryPageListWith(ProductionReturnItemBo bo, PageQuery pageQuery);

    /**
     * 根据退料单ID查询明细ID列表
     *
     * @param returnId 退料单ID
     * @return 明细ID列表
     */
    List<Long> selectItemIdsByReturnId(Long returnId);

    /**
     * 批量插入或更新生产退料明细
     *
     * @param items 明细BO集合
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<ProductionReturnItemBo> items);

    /**
     * 根据ID集合删除生产退料明细
     *
     * @param ids ID集合
     * @return 是否删除成功
     */
    Boolean deleteByIds(Collection<Long> ids);
}
