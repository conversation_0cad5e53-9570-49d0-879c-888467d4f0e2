package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 核销类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinApplyType implements IDictEnum<String> {

    PAYMENT_INVOICE("payment_invoice", "付款核销发票", "付款单核销应付发票"),
    RECEIPT_RECEIVABLE("receipt_receivable", "收款核销应收", "收款单核销应收账款"),
    ADVANCE_PAYMENT("advance_payment", "预付款核销", "预付款核销采购发票"),
    ADVANCE_RECEIPT("advance_receipt", "预收款核销", "预收款核销销售应收"),
    OFFSET("offset", "债权债务抵扣", "债权债务相互抵扣"),
    ADJUSTMENT("adjustment", "调整核销", "金额调整后的核销"),
    WRITE_OFF("write_off", "坏账核销", "坏账损失核销"),
    OTHER("other", "其他核销", "其他类型的核销");

    public final static String DICT_CODE = "erp_fin_apply_type";
    public final static String DICT_NAME = "核销类型";
    public final static String DICT_DESC = "管理财务核销操作的分类类型，包括付款核销、收款核销、预付预收核销等不同类型";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 核销类型枚举
     */
    public static FinApplyType getByValue(String value) {
        for (FinApplyType applyType : values()) {
            if (applyType.getValue().equals(value)) {
                return applyType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否需要特殊审批
     *
     * @return 是否需要特殊审批
     */
    public boolean requiresSpecialApproval() {
        return this == OFFSET || this == ADJUSTMENT || this == WRITE_OFF;
    }

    /**
     * 判断是否为预付/预收类型
     *
     * @return 是否为预付/预收类型
     */
    public boolean isAdvanceType() {
        return this == ADVANCE_PAYMENT || this == ADVANCE_RECEIPT;
    }

    /**
     * 判断是否影响现金流
     *
     * @return 是否影响现金流
     */
    public boolean affectsCashFlow() {
        return this == PAYMENT_INVOICE || this == RECEIPT_RECEIVABLE ||
            this == ADVANCE_PAYMENT || this == ADVANCE_RECEIPT;
    }

    /**
     * 获取相关的业务模块
     *
     * @return 相关的业务模块
     */
    public String getRelatedModule() {
        switch (this) {
            case PAYMENT_INVOICE:
            case ADVANCE_PAYMENT:
                return "AP"; // 应付模块
            case RECEIPT_RECEIVABLE:
            case ADVANCE_RECEIPT:
                return "AR"; // 应收模块
            case OFFSET:
            case ADJUSTMENT:
            case WRITE_OFF:
                return "GL"; // 总账模块
            case OTHER:
            default:
                return "COMMON"; // 通用模块
        }
    }
}
