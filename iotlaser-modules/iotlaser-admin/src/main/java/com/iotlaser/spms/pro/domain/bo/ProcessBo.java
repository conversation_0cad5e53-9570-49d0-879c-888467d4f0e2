package com.iotlaser.spms.pro.domain.bo;

import com.iotlaser.spms.pro.domain.Process;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 工序业务对象 pro_process
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Process.class, reverseConvertGenerate = false)
public class ProcessBo extends BaseEntity {

    /**
     * 工序ID
     */
    @NotNull(message = "工序ID不能为空", groups = {EditGroup.class})
    private Long processId;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 工序类别
     */
    private String processCategory;

    /**
     * 标准工时(分钟)
     */
    private Long standardDuration;

    /**
     * 标准计件单价
     */
    private Long standardPrice;

    /**
     * 是否质检
     */
    private String checkFlag;

    /**
     * 报工类型
     */
    private String reportType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
