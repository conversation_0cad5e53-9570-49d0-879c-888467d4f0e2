package com.iotlaser.spms.pro.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.pro.domain.Routing;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;


/**
 * 工艺路线视图对象 pro_routing
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Routing.class)
public class RoutingVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 路线ID
     */
    @ExcelProperty(value = "路线ID")
    private Long routingId;

    /**
     * 路线编码
     */
    @ExcelProperty(value = "路线编码")
    private String routingCode;

    /**
     * 路线名称
     */
    @ExcelProperty(value = "路线名称")
    private String routingName;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位ID")
    private Long unitId;

    /**
     * 计量单位编码
     */
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    private String unitName;

    /**
     * 路线版本
     */
    @ExcelProperty(value = "路线版本")
    private String routingVersion;

    /**
     * 路线状态
     */
    @ExcelProperty(value = "路线状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "pro_routing_status")
    private String routingStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
