package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.PurchaseReturnBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 采购退货Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/07
 */
public interface IPurchaseReturnService {

    /**
     * 查询采购退货
     *
     * @param returnId 主键
     * @return 采购退货
     */
    PurchaseReturnVo queryById(Long returnId);

    /**
     * 分页查询采购退货列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购退货分页列表
     */
    TableDataInfo<PurchaseReturnVo> queryPageList(PurchaseReturnBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的采购退货列表
     *
     * @param bo 查询条件
     * @return 采购退货列表
     */
    List<PurchaseReturnVo> queryList(PurchaseReturnBo bo);

    /**
     * 新增采购退货
     *
     * @param bo 采购退货
     * @return 创建的采购退货
     */
    Boolean insertByBo(PurchaseReturnBo bo);

    /**
     * 修改采购退货
     *
     * @param bo 采购退货
     * @return 修改后的采购退货
     */
    Boolean updateByBo(PurchaseReturnBo bo);

    /**
     * 校验并批量删除采购退货信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认采购退货单
     *
     * @param returnId 退货单ID
     * @return 是否确认成功
     */
    Boolean confirmReturn(Long returnId);

    /**
     * 批量确认采购退货单
     *
     * @param returnIds 退货单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmReturns(Collection<Long> returnIds);

    /**
     * 完成采购退货出库
     *
     * @param returnId 退货单ID
     * @return 是否完成成功
     */
    Boolean completeReturn(Long returnId);

    /**
     * 取消采购退货单
     *
     * @param returnId 退货单ID
     * @param reason   取消原因
     * @return 是否取消成功
     */
    Boolean cancelReturn(Long returnId, String reason);

    /**
     * 根据采购入库单创建退货单
     *
     * @param purchaseInboundId 采购入库单ID
     * @return 创建的退货单
     */
    PurchaseReturnVo createFromPurchaseInbound(Long purchaseInboundId);
}
