package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.SaleOrderItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售订单明细视图对象 erp_sale_order_item
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SaleOrderItem.class)
public class SaleOrderItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 明细ID
     */
    @ExcelProperty(value = "明细ID")
    private Long itemId;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 库存ID
     */
    @ExcelProperty(value = "库存ID")
    private Long inventoryBatchId;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位ID")
    private Long unitId;

    /**
     * 计量单位编码
     */
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    private String unitName;

    /**
     * 待完成数量
     */
    @ExcelProperty(value = "待完成数量")
    private BigDecimal quantity;

    /**
     * 已完成数量
     */
    @ExcelProperty(value = "已完成数量")
    private BigDecimal finishQuantity;

    /**
     * 已发货数量
     */
    @ExcelProperty(value = "已发货数量")
    private BigDecimal shippedQuantity;

    /**
     * 已退货数量
     */
    @ExcelProperty(value = "已退货数量")
    private BigDecimal returnedQuantity;

    /**
     * 含税价格
     */
    @ExcelProperty(value = "含税价格")
    private BigDecimal price;

    /**
     * 单价（不含税）
     */
    @ExcelProperty(value = "单价（不含税）")
    private BigDecimal priceExclusiveTax;

    /**
     * 金额（含税）
     */
    @ExcelProperty(value = "金额（含税）")
    private BigDecimal amount;

    /**
     * 金额（不含税）
     */
    @ExcelProperty(value = "金额（不含税）")
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    @ExcelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;

    /**
     * 已开票数量
     */
    @ExcelProperty(value = "已开票数量")
    private BigDecimal invoicedQuantity;

    /**
     * 已开票金额
     */
    @ExcelProperty(value = "已开票金额")
    private BigDecimal invoicedAmount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

    /**
     * 批次
     */
    private List<SaleOrderItemBatchVo> batches;
}
