package com.iotlaser.spms.mes.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生产领料单状态
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Getter
@AllArgsConstructor
public enum ProductionIssueStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "领料单已创建，但未通知仓库"),
    PENDING_WAREHOUSE("pending_warehouse", "待出库", "已通知仓库，等待仓库拣货"),
    COMPLETED("completed", "已领料", "仓库已完成发料，物料已到车间");
    public final static String DICT_CODE = "mes_production_issue_status";
    public final static String DICT_NAME = "生产领料状态";
    public final static String DICT_DESC = "管理生产领料单的流程状态，从草稿创建到仓库出库完成的完整流程";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 生产领料状态枚举
     */
    public static ProductionIssueStatus getByValue(String value) {
        for (ProductionIssueStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
