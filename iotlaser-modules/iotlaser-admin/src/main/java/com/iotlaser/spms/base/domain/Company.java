package com.iotlaser.spms.base.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 公司信息对象 base_company
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("base_company")
public class Company extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 公司ID
     */
    @TableId(value = "company_id")
    private Long companyId;

    /**
     * 公司类型
     */
    private String companyType;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司简称
     */
    private String shortName;

    /**
     * 公司英文名称
     */
    private String englishName;

    /**
     * 公司简介
     */
    private String intro;

    /**
     * 公司等级
     */
    private String level;

    /**
     * 公司LOGO地址
     */
    private String logo;

    /**
     * 公司地址
     */
    private String address;

    /**
     * 官网地址
     */
    private String website;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 公司电话
     */
    private String tel;

    /**
     * 联系人1
     */
    private String contact1;

    /**
     * 联系人1-电话
     */
    private String contact1Tel;

    /**
     * 联系人1-邮箱
     */
    private String contact1Email;

    /**
     * 联系人2
     */
    private String contact2;

    /**
     * 联系人2-电话
     */
    private String contact2Tel;

    /**
     * 联系人2-邮箱
     */
    private String contact2Email;

    /**
     * 是否供应商
     */
    private String supplierFlag;

    /**
     * 是否客户
     */
    private String customerFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

}
