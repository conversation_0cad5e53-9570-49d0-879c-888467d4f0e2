package com.iotlaser.spms.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.wms.domain.bo.InventoryCheckItemBo;
import com.iotlaser.spms.wms.domain.vo.InventoryCheckItemVo;
import com.iotlaser.spms.wms.service.IInventoryCheckItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 库存盘点明细
 *
 * <AUTHOR> Kai
 * @date 2025-06-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/wms/inventoryCheckItem")
public class InventoryCheckItemController extends BaseController {

    private final IInventoryCheckItemService inventoryCheckItemService;

    /**
     * 查询库存盘点明细列表
     */
    @SaCheckPermission("wms:inventoryCheckItem:list")
    @GetMapping("/list")
    public TableDataInfo<InventoryCheckItemVo> list(InventoryCheckItemBo bo, PageQuery pageQuery) {
        return inventoryCheckItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出库存盘点明细列表
     */
    @SaCheckPermission("wms:inventoryCheckItem:export")
    @Log(title = "库存盘点明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InventoryCheckItemBo bo, HttpServletResponse response) {
        List<InventoryCheckItemVo> list = inventoryCheckItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "库存盘点明细", InventoryCheckItemVo.class, response);
    }

    /**
     * 获取库存盘点明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("wms:inventoryCheckItem:query")
    @GetMapping("/{itemId}")
    public R<InventoryCheckItemVo> getInfo(@NotNull(message = "主键不能为空")
                                           @PathVariable Long itemId) {
        return R.ok(inventoryCheckItemService.queryById(itemId));
    }

    /**
     * 新增库存盘点明细
     */
    @SaCheckPermission("wms:inventoryCheckItem:add")
    @Log(title = "库存盘点明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InventoryCheckItemBo bo) {
        return toAjax(inventoryCheckItemService.insertByBo(bo));
    }

    /**
     * 修改库存盘点明细
     */
    @SaCheckPermission("wms:inventoryCheckItem:edit")
    @Log(title = "库存盘点明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InventoryCheckItemBo bo) {
        return toAjax(inventoryCheckItemService.updateByBo(bo));
    }

    /**
     * 删除库存盘点明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("wms:inventoryCheckItem:remove")
    @Log(title = "库存盘点明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(inventoryCheckItemService.deleteWithValidByIds(List.of(itemIds), true));
    }
}
