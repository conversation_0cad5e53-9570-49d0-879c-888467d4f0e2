package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.PurchaseOrderItem;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 采购订单明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface PurchaseOrderItemMapper extends BaseMapperPlus<PurchaseOrderItem, PurchaseOrderItemVo> {


    /**
     * 查询采购订单明细表及其关联信息
     */
    PurchaseOrderItemVo queryByIdWith(@Param("itemId") Long itemId);

    /**
     * 分页查询采购订单明细表及其关联信息
     */
    List<PurchaseOrderItemVo> queryPageListWith(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<PurchaseOrderItem> wrapper);


}
