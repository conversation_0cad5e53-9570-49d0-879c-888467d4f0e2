package com.iotlaser.spms.base.service;

import cn.hutool.core.lang.tree.Tree;
import com.iotlaser.spms.base.domain.bo.MeasureUnitBo;
import com.iotlaser.spms.base.domain.vo.MeasureUnitVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 计量单位Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface IMeasureUnitService {

    /**
     * 查询计量单位
     *
     * @param unitId 主键
     * @return 计量单位
     */
    MeasureUnitVo queryById(Long unitId);

    /**
     * 查询符合条件的计量单位列表
     *
     * @param bo 查询条件
     * @return 计量单位列表
     */
    List<MeasureUnitVo> queryList(MeasureUnitBo bo);

    /**
     * 新增计量单位
     *
     * @param bo 计量单位
     * @return 是否新增成功
     */
    Boolean insertByBo(MeasureUnitBo bo);

    /**
     * 修改计量单位
     *
     * @param bo 计量单位
     * @return 是否修改成功
     */
    Boolean updateByBo(MeasureUnitBo bo);

    /**
     * 校验并批量删除计量单位信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 检查计量单位编码唯一性
     * ✅ 修正：使用BO而非直接暴露Entity
     *
     * @param bo 计量单位BO
     * @return 是否唯一
     */
    boolean checkProductMaterialUnitCodeUnique(MeasureUnitBo bo);

    List<Tree<Long>> selectTreeList(MeasureUnitBo bo);

    List<Tree<Long>> buildTreeSelect(List<MeasureUnitVo> results);

    /**
     * 数量单位转换
     *
     * @param quantity 数量
     * @param fromUnit 源单位编码
     * @param toUnit   目标单位编码
     * @return 转换后的数量
     */
    BigDecimal convertQuantity(BigDecimal quantity, String fromUnit, String toUnit);

    /**
     * 根据单位编码获取单位信息
     *
     * @param unitCode 单位编码
     * @return 单位信息
     */
    MeasureUnitVo getByUnitCode(String unitCode);

    /**
     * 根据单位组获取单位列表
     *
     * @param groupCode 单位组编码
     * @return 单位列表
     */
    List<MeasureUnitVo> getUnitsByGroup(String groupCode);

    /**
     * 获取基础单位列表（没有父单位的单位）
     *
     * @return 基础单位列表
     */
    List<MeasureUnitVo> getBaseUnits();

    /**
     * 根据精度四舍五入数值
     *
     * @param value    数值
     * @param unitCode 单位编码
     * @return 四舍五入后的数值
     */
    BigDecimal roundByUnitPrecision(BigDecimal value, String unitCode);
}
