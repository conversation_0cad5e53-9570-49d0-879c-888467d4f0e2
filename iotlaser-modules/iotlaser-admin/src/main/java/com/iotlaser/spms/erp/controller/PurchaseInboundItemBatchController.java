package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundItemBatchBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemBatchVo;
import com.iotlaser.spms.erp.service.IPurchaseInboundItemBatchService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购入库批次明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/purchaseInboundItemBatch")
public class PurchaseInboundItemBatchController extends BaseController {

    private final IPurchaseInboundItemBatchService purchaseInboundItemBatchService;

    /**
     * 查询采购入库批次明细列表
     */
    @SaCheckPermission("erp:purchaseInboundItemBatch:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseInboundItemBatchVo> list(PurchaseInboundItemBatchBo bo, PageQuery pageQuery) {
        return purchaseInboundItemBatchService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出采购入库批次明细列表
     */
    @SaCheckPermission("erp:purchaseInboundItemBatch:export")
    @Log(title = "采购入库批次明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseInboundItemBatchBo bo, HttpServletResponse response) {
        List<PurchaseInboundItemBatchVo> list = purchaseInboundItemBatchService.queryList(bo);
        ExcelUtil.exportExcel(list, "采购入库批次明细", PurchaseInboundItemBatchVo.class, response);
    }

    /**
     * 获取采购入库批次明细详细信息
     *
     * @param batchId 主键
     */
    @SaCheckPermission("erp:purchaseInboundItemBatch:query")
    @GetMapping("/{batchId}")
    public R<PurchaseInboundItemBatchVo> getInfo(@NotNull(message = "主键不能为空")
                                                 @PathVariable Long batchId) {
        return R.ok(purchaseInboundItemBatchService.queryById(batchId));
    }

    /**
     * 新增采购入库批次明细
     */
    @SaCheckPermission("erp:purchaseInboundItemBatch:add")
    @Log(title = "采购入库批次明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PurchaseInboundItemBatchBo bo) {
        return toAjax(purchaseInboundItemBatchService.insertByBo(bo));
    }

    /**
     * 修改采购入库批次明细
     */
    @SaCheckPermission("erp:purchaseInboundItemBatch:edit")
    @Log(title = "采购入库批次明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PurchaseInboundItemBatchBo bo) {
        return toAjax(purchaseInboundItemBatchService.updateByBo(bo));
    }

    /**
     * 删除采购入库批次明细
     *
     * @param batchIds 主键串
     */
    @SaCheckPermission("erp:purchaseInboundItemBatch:remove")
    @Log(title = "采购入库批次明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{batchIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] batchIds) {
        return toAjax(purchaseInboundItemBatchService.deleteWithValidByIds(List.of(batchIds), true));
    }
}
