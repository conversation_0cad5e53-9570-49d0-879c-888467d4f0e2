package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 对账单明细对象 erp_fin_statement_item
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_statement_item")
public class FinStatementItem extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 明细ID
     */
    @TableId(value = "item_id")
    private Long itemId;

    /**
     * 对账ID
     */
    private Long statementId;

    /**
     * 组号
     */
    private String groupId;

    /**
     * 组内余额
     */
    private BigDecimal groupBalance;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 来源明细ID
     */
    private Long sourceItemId;

    /**
     * 借方金额 (应收增加)
     */
    private BigDecimal amountDebit;

    /**
     * 贷方金额 (应收减少)
     */
    private BigDecimal amountCredit;

    /**
     * 对账标记
     */
    private String markFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
