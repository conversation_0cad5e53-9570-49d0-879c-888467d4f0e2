package com.iotlaser.spms.base.strategy;

import com.iotlaser.spms.base.domain.vo.AutoCodePartVo;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 当前时间处理类
 *
 * <AUTHOR>
 * @date 2025/5/14
 */
@Component
@Order(1)
public class PartTypeNowDateHandler implements PartTypeTemplate {

    /**
     * 根据指定的日期格式返回当前时间的字符串表示
     * 此方法用于处理基础自动代码生成的一部分， Specifically, it formats the current date and time
     * according to the provided date format
     *
     * @param vo 包含日期格式信息的请求对象
     *           该对象用于指定希望获取的日期和时间的格式
     * @return 当前时间的字符串表示，格式化为baseAutoCodePartVo.getDateFormat()所指定的格式
     */
    @Override
    public String partHandle(AutoCodePartVo vo) {
        String formatDate = vo.getDateFormat();
        return DateTimeFormatter.ofPattern(formatDate).format(LocalDateTime.now());
    }
}
