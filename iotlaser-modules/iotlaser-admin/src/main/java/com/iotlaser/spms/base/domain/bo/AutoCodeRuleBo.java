package com.iotlaser.spms.base.domain.bo;

import com.iotlaser.spms.base.domain.AutoCodeRule;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 编码生成规则业务对象 sys_auto_code_rule
 *
 * <AUTHOR>
 * @date 2025/03/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AutoCodeRule.class, reverseConvertGenerate = false)
public class AutoCodeRuleBo extends BaseEntity {

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空", groups = {EditGroup.class})
    private Long ruleId;

    /**
     * 规则编码
     */
    @NotBlank(message = "规则编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String ruleCode;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String ruleName;

    /**
     * 描述
     */
    private String ruleDesc;

    /**
     * 最大长度
     */
    private Long maxLength;

    /**
     * 是否补齐
     */
    @NotBlank(message = "是否补齐不能为空", groups = {AddGroup.class, EditGroup.class})
    private String isPadded;

    /**
     * 补齐字符
     */
    private String paddedChar;

    /**
     * 补齐方式
     */
    private String paddedMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 （0正常 1停用）
     */
    private String status;

}
