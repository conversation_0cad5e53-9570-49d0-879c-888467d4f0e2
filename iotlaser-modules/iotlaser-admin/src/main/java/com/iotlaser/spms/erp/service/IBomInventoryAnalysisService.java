package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.vo.BomInventoryAnalysisVo;
import com.iotlaser.spms.erp.domain.vo.MaterialShortageVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseRequirementVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * BOM清单库存分析服务接口
 *
 * <AUTHOR> Assistant
 * @date 2025-06-24
 */
public interface IBomInventoryAnalysisService {

    /**
     * 根据BOM清单分析库存状况
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 需求数量
     * @return BOM库存分析结果
     */
    BomInventoryAnalysisVo analyzeBomInventory(Long bomId, BigDecimal requiredQuantity);

    /**
     * 批量分析多个BOM的库存状况
     *
     * @param bomRequirements BOM需求清单 (BOM ID -> 需求数量)
     * @return 批量分析结果
     */
    List<BomInventoryAnalysisVo> batchAnalyzeBomInventory(Map<Long, BigDecimal> bomRequirements);

    /**
     * 查询原材料当前库存余量
     *
     * @param materialId 原材料ID
     * @param locationId 库位ID（可选）
     * @return 库存余量信息
     */
    BigDecimal queryMaterialInventoryBalance(Long materialId, Long locationId);

    /**
     * 批量查询原材料库存余量
     *
     * @param materialIds 原材料ID列表
     * @param locationId  库位ID（可选）
     * @return 库存余量映射 (材料ID -> 库存余量)
     */
    Map<Long, BigDecimal> batchQueryMaterialInventoryBalance(List<Long> materialIds, Long locationId);

    /**
     * 计算采购需求
     *
     * @param bomId             BOM清单ID
     * @param requiredQuantity  需求数量
     * @param considerInTransit 是否考虑在途订单
     * @return 采购需求分析结果
     */
    PurchaseRequirementVo calculatePurchaseRequirement(Long bomId, BigDecimal requiredQuantity, Boolean considerInTransit);

    /**
     * 生成缺料分析报告
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 需求数量
     * @return 缺料分析结果
     */
    List<MaterialShortageVo> generateMaterialShortageReport(Long bomId, BigDecimal requiredQuantity);

    /**
     * 检查库存预警
     *
     * @param materialId 原材料ID
     * @return 是否触发预警
     */
    Boolean checkInventoryWarning(Long materialId);

    /**
     * 批量检查库存预警
     *
     * @param materialIds 原材料ID列表
     * @return 预警材料列表
     */
    List<Long> batchCheckInventoryWarning(List<Long> materialIds);

    /**
     * 获取材料的安全库存量
     *
     * @param materialId 原材料ID
     * @return 安全库存量
     */
    BigDecimal getMaterialSafetyStock(Long materialId);

    /**
     * 获取材料的最小采购量
     *
     * @param materialId 原材料ID
     * @return 最小采购量
     */
    BigDecimal getMaterialMinPurchaseQuantity(Long materialId);

    /**
     * 计算考虑损耗率后的实际需求量
     *
     * @param materialId          原材料ID
     * @param theoreticalQuantity 理论需求量
     * @return 实际需求量
     */
    BigDecimal calculateActualRequirement(Long materialId, BigDecimal theoreticalQuantity);

    /**
     * 多层级BOM展开计算
     *
     * @param bomId            顶层BOM ID
     * @param requiredQuantity 需求数量
     * @param maxLevel         最大展开层级（防止循环引用）
     * @return 展开后的原材料需求清单 (材料ID -> 总需求量)
     */
    Map<Long, BigDecimal> expandBomMultiLevel(Long bomId, BigDecimal requiredQuantity, Integer maxLevel);

    /**
     * 查询在途采购订单数量
     *
     * @param materialId 原材料ID
     * @return 在途数量
     */
    BigDecimal queryInTransitQuantity(Long materialId);

    /**
     * 生成采购建议清单
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 需求数量
     * @param priorityLevel    优先级等级
     * @return 采购建议清单
     */
    List<PurchaseRequirementVo> generatePurchaseSuggestions(Long bomId, BigDecimal requiredQuantity, String priorityLevel);
}
