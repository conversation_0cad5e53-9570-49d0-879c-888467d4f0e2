package com.iotlaser.spms.pro.service;

import com.iotlaser.spms.pro.domain.bo.RoutingStepBo;
import com.iotlaser.spms.pro.domain.vo.RoutingStepVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 工艺路线工序Service接口
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
public interface IRoutingStepService {

    /**
     * 查询工艺路线工序
     *
     * @param stepId 主键
     * @return 工艺路线工序
     */
    RoutingStepVo queryById(Long stepId);

    /**
     * 分页查询工艺路线工序列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工艺路线工序分页列表
     */
    TableDataInfo<RoutingStepVo> queryPageList(RoutingStepBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的工艺路线工序列表
     *
     * @param bo 查询条件
     * @return 工艺路线工序列表
     */
    List<RoutingStepVo> queryList(RoutingStepBo bo);

    /**
     * 新增工艺路线工序
     *
     * @param bo 工艺路线工序
     * @return 是否新增成功
     */
    Boolean insertByBo(RoutingStepBo bo);

    /**
     * 修改工艺路线工序
     *
     * @param bo 工艺路线工序
     * @return 是否修改成功
     */
    Boolean updateByBo(RoutingStepBo bo);

    /**
     * 校验并批量删除工艺路线工序信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
