package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinAccountBo;
import com.iotlaser.spms.erp.domain.vo.FinAccountVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 账户Service接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
public interface IFinAccountService {

    /**
     * 查询账户
     *
     * @param accountId 主键
     * @return 账户
     */
    FinAccountVo queryById(Long accountId);

    /**
     * 分页查询账户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 账户分页列表
     */
    TableDataInfo<FinAccountVo> queryPageList(FinAccountBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的账户列表
     *
     * @param bo 查询条件
     * @return 账户列表
     */
    List<FinAccountVo> queryList(FinAccountBo bo);

    /**
     * 新增账户
     *
     * @param bo 账户
     * @return 是否新增成功
     */
    Boolean insertByBo(FinAccountBo bo);

    /**
     * 修改账户
     *
     * @param bo 账户
     * @return 是否修改成功
     */
    Boolean updateByBo(FinAccountBo bo);

    /**
     * 校验并批量删除账户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 更新账户余额
     *
     * @param accountId 账户ID
     * @param amount    变动金额 (正数为增加，负数为减少)
     * @return 是否更新成功
     */
    Boolean updateAccountBalance(Long accountId, BigDecimal amount);

    /**
     * 获取账户当前余额
     *
     * @param accountId 账户ID
     * @return 当前余额
     */
    BigDecimal getAccountBalance(Long accountId);
}
