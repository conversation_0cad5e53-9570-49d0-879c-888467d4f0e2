package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinApInvoiceItemBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceItemVo;
import com.iotlaser.spms.erp.service.IFinApInvoiceItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商发票明细
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finApInvoiceItem")
public class FinApInvoiceItemController extends BaseController {

    private final IFinApInvoiceItemService finApInvoiceItemService;

    /**
     * 查询供应商发票明细列表
     */
    @SaCheckPermission("erp:finApInvoiceItem:list")
    @GetMapping("/list")
    public TableDataInfo<FinApInvoiceItemVo> list(FinApInvoiceItemBo bo, PageQuery pageQuery) {
        return finApInvoiceItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出供应商发票明细列表
     */
    @SaCheckPermission("erp:finApInvoiceItem:export")
    @Log(title = "供应商发票明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinApInvoiceItemBo bo, HttpServletResponse response) {
        List<FinApInvoiceItemVo> list = finApInvoiceItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "供应商发票明细", FinApInvoiceItemVo.class, response);
    }

    /**
     * 获取供应商发票明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("erp:finApInvoiceItem:query")
    @GetMapping("/{itemId}")
    public R<FinApInvoiceItemVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long itemId) {
        return R.ok(finApInvoiceItemService.queryById(itemId));
    }

    /**
     * 新增供应商发票明细
     */
    @SaCheckPermission("erp:finApInvoiceItem:add")
    @Log(title = "供应商发票明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinApInvoiceItemBo bo) {
        return toAjax(finApInvoiceItemService.insertByBo(bo));
    }

    /**
     * 修改供应商发票明细
     */
    @SaCheckPermission("erp:finApInvoiceItem:edit")
    @Log(title = "供应商发票明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinApInvoiceItemBo bo) {
        return toAjax(finApInvoiceItemService.updateByBo(bo));
    }

    /**
     * 删除供应商发票明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("erp:finApInvoiceItem:remove")
    @Log(title = "供应商发票明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(finApInvoiceItemService.deleteWithValidByIds(List.of(itemIds), true));
    }
}
