package com.iotlaser.spms.common.service;

import com.iotlaser.spms.common.domain.bo.SourceBo;
import com.iotlaser.spms.common.domain.bo.SourceItemBo;
import com.iotlaser.spms.common.domain.vo.SourceItemVo;
import com.iotlaser.spms.common.domain.vo.SourceVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

public interface ISourceService {

    /**
     * 查询产品明细表及其关联信息
     *
     * @param sourceType 查询分类
     * @param id         主键
     * @return 产品入库明细表
     */
    SourceVo queryByIdWith(String sourceType, Long id);

    /**
     * 分页查询产品明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品入库明细表分页列表
     */
    TableDataInfo<SourceVo> queryPageListWith(SourceBo bo, PageQuery pageQuery);


    /**
     * 查询产品明细表及其关联信息
     *
     * @param sourceType 查询分类
     * @param itemId     主键
     * @return 产品入库明细表
     */
    SourceItemVo queryItemByIdWith(String sourceType, Long itemId);

    /**
     * 分页查询产品明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品入库明细表分页列表
     */
    TableDataInfo<SourceItemVo> queryItemPageListWith(SourceItemBo bo, PageQuery pageQuery);
}
