package com.iotlaser.spms.erp.utils;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 金额计算工具类
 * 统一ERP系统中的金额计算逻辑，确保计算一致性
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Slf4j
public class AmountCalculationUtils {

    /**
     * 默认金额精度（小数位数）
     */
    public static final int DEFAULT_AMOUNT_SCALE = 2;

    /**
     * 默认税率精度（小数位数）
     */
    public static final int DEFAULT_TAX_RATE_SCALE = 4;

    /**
     * 默认舍入模式
     */
    public static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 默认精度阈值
     */
    private static final BigDecimal PRECISION_THRESHOLD = new BigDecimal("0.01");

    /**
     * 计算不含税金额
     * 公式：不含税金额 = 含税金额 ÷ (1 + 税率%)
     *
     * @param amountIncludingTax 含税金额
     * @param taxRate            税率（百分比，如13表示13%）
     * @return 不含税金额
     */
    public static BigDecimal calculateAmountExcludingTax(BigDecimal amountIncludingTax, BigDecimal taxRate) {
        if (amountIncludingTax == null) {
            return BigDecimal.ZERO;
        }
        if (taxRate == null || taxRate.compareTo(BigDecimal.ZERO) == 0) {
            return amountIncludingTax;
        }

        try {
            // 计算税率系数：1 + 税率/100
            BigDecimal taxRateDecimal = taxRate.divide(new BigDecimal("100"), DEFAULT_TAX_RATE_SCALE, DEFAULT_ROUNDING_MODE);
            BigDecimal divisor = BigDecimal.ONE.add(taxRateDecimal);

            // 计算不含税金额
            return amountIncludingTax.divide(divisor, DEFAULT_AMOUNT_SCALE, DEFAULT_ROUNDING_MODE);
        } catch (Exception e) {
            log.error("计算不含税金额失败 - 含税金额: {}, 税率: {}, 错误: {}", amountIncludingTax, taxRate, e.getMessage());
            throw new ServiceException("计算不含税金额失败：" + e.getMessage());
        }
    }

    /**
     * 计算含税金额
     * 公式：含税金额 = 不含税金额 × (1 + 税率%)
     *
     * @param amountExcludingTax 不含税金额
     * @param taxRate            税率（百分比）
     * @return 含税金额
     */
    public static BigDecimal calculateAmountIncludingTax(BigDecimal amountExcludingTax, BigDecimal taxRate) {
        if (amountExcludingTax == null) {
            return BigDecimal.ZERO;
        }
        if (taxRate == null || taxRate.compareTo(BigDecimal.ZERO) == 0) {
            return amountExcludingTax;
        }

        try {
            // 计算税率系数：1 + 税率/100
            BigDecimal taxRateDecimal = taxRate.divide(new BigDecimal("100"), DEFAULT_TAX_RATE_SCALE, DEFAULT_ROUNDING_MODE);
            BigDecimal multiplier = BigDecimal.ONE.add(taxRateDecimal);

            // 计算含税金额
            return amountExcludingTax.multiply(multiplier).setScale(DEFAULT_AMOUNT_SCALE, DEFAULT_ROUNDING_MODE);
        } catch (Exception e) {
            log.error("计算含税金额失败 - 不含税金额: {}, 税率: {}, 错误: {}", amountExcludingTax, taxRate, e.getMessage());
            throw new ServiceException("计算含税金额失败：" + e.getMessage());
        }
    }

    /**
     * 计算税额
     * 公式：税额 = 含税金额 - 不含税金额
     *
     * @param amountIncludingTax 含税金额
     * @param amountExcludingTax 不含税金额
     * @return 税额
     */
    public static BigDecimal calculateTaxAmount(BigDecimal amountIncludingTax, BigDecimal amountExcludingTax) {
        if (amountIncludingTax == null) {
            amountIncludingTax = BigDecimal.ZERO;
        }
        if (amountExcludingTax == null) {
            amountExcludingTax = BigDecimal.ZERO;
        }

        return amountIncludingTax.subtract(amountExcludingTax).setScale(DEFAULT_AMOUNT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 根据不含税金额和税率计算税额
     * 公式：税额 = 不含税金额 × 税率%
     *
     * @param amountExcludingTax 不含税金额
     * @param taxRate            税率（百分比）
     * @return 税额
     */
    public static BigDecimal calculateTaxAmountByRate(BigDecimal amountExcludingTax, BigDecimal taxRate) {
        if (amountExcludingTax == null || taxRate == null) {
            return BigDecimal.ZERO;
        }

        try {
            BigDecimal taxRateDecimal = taxRate.divide(new BigDecimal("100"), DEFAULT_TAX_RATE_SCALE, DEFAULT_ROUNDING_MODE);
            return amountExcludingTax.multiply(taxRateDecimal).setScale(DEFAULT_AMOUNT_SCALE, DEFAULT_ROUNDING_MODE);
        } catch (Exception e) {
            log.error("根据税率计算税额失败 - 不含税金额: {}, 税率: {}, 错误: {}", amountExcludingTax, taxRate, e.getMessage());
            throw new ServiceException("计算税额失败：" + e.getMessage());
        }
    }

    /**
     * 计算行金额（含税）
     * 公式：行金额 = 数量 × 含税单价
     *
     * @param quantity 数量
     * @param price    含税单价
     * @return 行金额（含税）
     */
    public static BigDecimal calculateLineAmount(BigDecimal quantity, BigDecimal price) {
        if (quantity == null || price == null) {
            return BigDecimal.ZERO;
        }

        return quantity.multiply(price).setScale(DEFAULT_AMOUNT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 计算行金额（不含税）
     * 公式：行金额 = 数量 × 不含税单价
     *
     * @param quantity          数量
     * @param priceExcludingTax 不含税单价
     * @return 行金额（不含税）
     */
    public static BigDecimal calculateLineAmountExcludingTax(BigDecimal quantity, BigDecimal priceExcludingTax) {
        if (quantity == null || priceExcludingTax == null) {
            return BigDecimal.ZERO;
        }

        return quantity.multiply(priceExcludingTax).setScale(DEFAULT_AMOUNT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 获取精度阈值
     *
     * @return 精度阈值
     */
    public static BigDecimal getPrecisionThreshold() {
        return PRECISION_THRESHOLD;
    }

    /**
     * 安全比较两个BigDecimal值
     * 处理null值情况
     *
     * @param value1 第一个值
     * @param value2 第二个值
     * @return 比较结果：-1表示value1<value2，0表示相等，1表示value1>value2
     */
    public static int safeCompare(BigDecimal value1, BigDecimal value2) {
        if (value1 == null && value2 == null) {
            return 0;
        }
        if (value1 == null) {
            return -1;
        }
        if (value2 == null) {
            return 1;
        }
        return value1.compareTo(value2);
    }

    /**
     * 检查两个金额是否在精度范围内相等
     *
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 是否相等
     */
    public static boolean isAmountEqual(BigDecimal amount1, BigDecimal amount2) {
        if (amount1 == null && amount2 == null) {
            return true;
        }
        if (amount1 == null || amount2 == null) {
            return false;
        }
        BigDecimal difference = amount1.subtract(amount2).abs();
        return safeCompare(difference, PRECISION_THRESHOLD) <= 0;
    }

    /**
     * 验证金额计算的一致性
     * 验证：含税金额 = 不含税金额 + 税额
     *
     * @param amountIncludingTax 含税金额
     * @param amountExcludingTax 不含税金额
     * @param taxAmount          税额
     * @return 是否一致
     */
    public static boolean validateAmountConsistency(BigDecimal amountIncludingTax,
                                                    BigDecimal amountExcludingTax,
                                                    BigDecimal taxAmount) {
        if (amountIncludingTax == null || amountExcludingTax == null || taxAmount == null) {
            return false;
        }

        BigDecimal calculatedTotal = amountExcludingTax.add(taxAmount);
        BigDecimal difference = amountIncludingTax.subtract(calculatedTotal).abs();

        // 允许0.01的误差
        return difference.compareTo(new BigDecimal("0.01")) <= 0;
    }

    /**
     * 计算金额差异百分比
     *
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 差异百分比（绝对值）
     */
    public static BigDecimal calculateAmountDifferencePercentage(BigDecimal amount1, BigDecimal amount2) {
        if (amount1 == null || amount2 == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal maxAmount = amount1.max(amount2);
        if (maxAmount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal difference = amount1.subtract(amount2).abs();
        return difference.divide(maxAmount, 4, DEFAULT_ROUNDING_MODE).multiply(new BigDecimal("100"));
    }

    /**
     * 检查金额差异是否在容差范围内
     *
     * @param amount1   金额1
     * @param amount2   金额2
     * @param tolerance 容差百分比（如5表示5%）
     * @return 是否在容差范围内
     */
    public static boolean isWithinTolerance(BigDecimal amount1, BigDecimal amount2, BigDecimal tolerance) {
        BigDecimal differencePercentage = calculateAmountDifferencePercentage(amount1, amount2);
        return differencePercentage.compareTo(tolerance) <= 0;
    }

    /**
     * 格式化金额显示
     *
     * @param amount 金额
     * @return 格式化后的金额字符串
     */
    public static String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0.00";
        }
        return amount.setScale(DEFAULT_AMOUNT_SCALE, DEFAULT_ROUNDING_MODE).toString();
    }

    /**
     * 安全的BigDecimal加法
     *
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 相加结果
     */
    public static BigDecimal safeAdd(BigDecimal amount1, BigDecimal amount2) {
        if (amount1 == null) amount1 = BigDecimal.ZERO;
        if (amount2 == null) amount2 = BigDecimal.ZERO;
        return amount1.add(amount2);
    }

    /**
     * 安全的BigDecimal减法
     *
     * @param amount1 被减数
     * @param amount2 减数
     * @return 相减结果
     */
    public static BigDecimal safeSubtract(BigDecimal amount1, BigDecimal amount2) {
        if (amount1 == null) amount1 = BigDecimal.ZERO;
        if (amount2 == null) amount2 = BigDecimal.ZERO;
        return amount1.subtract(amount2);
    }

}
