package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.enums.GenCodeType;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.domain.bo.SaleOrderItemBo;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundBo;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundItemBo;
import com.iotlaser.spms.erp.domain.vo.*;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import com.iotlaser.spms.erp.mapper.SaleOutboundMapper;
import com.iotlaser.spms.erp.service.*;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.domain.vo.InventoryBatchVo;
import com.iotlaser.spms.wms.service.IInventoryBatchService;
import com.iotlaser.spms.wms.service.IInventoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 销售出库Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-05-10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleOutboundServiceImpl implements ISaleOutboundService {

    private final SaleOutboundMapper baseMapper;
    private final ISaleOutboundItemService itemService;
    private final ISaleOrderItemService saleOrderItemService;
    private final IInventoryService inventoryService;
    private final IFinArReceivableService finArReceivableService;
    private final IFinArReceiptReceivableLinkService finArReceiptReceivableLinkService;
    private final IFinAccountLedgerService finAccountLedgerService;
    private final IInventoryBatchService inventoryBatchService;
    private final ICompanyService companyService;
    private final IProductService productService;
    private final Gen gen;
    @Lazy
    @Autowired
    private ISaleOrderService saleOrderService;

    /**
     * 查询销售出库
     *
     * @param outboundId 主键
     * @return 销售出库
     */
    @Override
    public SaleOutboundVo queryById(Long outboundId) {
        return baseMapper.selectVoById(outboundId);
    }

    /**
     * 分页查询销售出库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售出库分页列表
     */
    @Override
    public TableDataInfo<SaleOutboundVo> queryPageList(SaleOutboundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleOutbound> lqw = buildQueryWrapper(bo);
        Page<SaleOutboundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售出库列表
     *
     * @param bo 查询条件
     * @return 销售出库列表
     */
    @Override
    public List<SaleOutboundVo> queryList(SaleOutboundBo bo) {
        LambdaQueryWrapper<SaleOutbound> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据销售订单ID查询销售出库单列表
     *
     * @param orderId 销售订单ID
     * @return 销售出库单列表
     */
    @Override
    public List<SaleOutboundVo> queryByOrderId(Long orderId) {
        if (orderId == null) {
            throw new ServiceException("销售订单ID不能为空");
        }

        LambdaQueryWrapper<SaleOutbound> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SaleOutbound::getOrderId, orderId);
        wrapper.eq(SaleOutbound::getStatus, "1"); // 只查询有效记录
        wrapper.orderByDesc(SaleOutbound::getCreateTime);

        try {
            List<SaleOutboundVo> result = baseMapper.selectVoList(wrapper);
            log.info("根据销售订单ID查询出库单完成 - 订单ID: {}, 结果数量: {}", orderId, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据销售订单ID查询出库单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("查询销售出库单失败：" + e.getMessage());
        }
    }

    private LambdaQueryWrapper<SaleOutbound> buildQueryWrapper(SaleOutboundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleOutbound> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleOutbound::getOutboundId);
        lqw.eq(StringUtils.isNotBlank(bo.getOutboundCode()), SaleOutbound::getOutboundCode, bo.getOutboundCode());
        lqw.like(StringUtils.isNotBlank(bo.getOutboundName()), SaleOutbound::getOutboundName, bo.getOutboundName());
        lqw.eq(bo.getOrderId() != null, SaleOutbound::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderCode()), SaleOutbound::getOrderCode, bo.getOrderCode());
        lqw.like(StringUtils.isNotBlank(bo.getOrderName()), SaleOutbound::getOrderName, bo.getOrderName());
        lqw.eq(bo.getCustomerId() != null, SaleOutbound::getCustomerId, bo.getCustomerId());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerCode()), SaleOutbound::getCustomerCode, bo.getCustomerCode());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), SaleOutbound::getCustomerName, bo.getCustomerName());
        lqw.eq(bo.getOutboundDate() != null, SaleOutbound::getOutboundDate, bo.getOutboundDate());
        lqw.eq(bo.getOutboundStatus() != null, SaleOutbound::getOutboundStatus, bo.getOutboundStatus().getValue());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleOutbound::getStatus, bo.getStatus());
        lqw.between(params.get("beginOrderDate") != null && params.get("endOrderDate") != null,
            SaleOutbound::getOutboundDate, params.get("beginOrderDate"), params.get("endOrderDate"));
        return lqw;
    }

    /**
     * 新增销售出库
     *
     * @param bo 销售出库
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SaleOutboundBo bo) {
        try {
            // 1. 生成出库单编号
            if (StringUtils.isBlank(bo.getOutboundCode())) {
                bo.setOutboundCode(gen.code(GenCodeType.ERP_SALE_OUTBOUND_CODE));
            }

            // 2. 设置初始状态
            if (bo.getOutboundStatus() == null) {
                bo.setOutboundStatus(SaleOutboundStatus.DRAFT);
            }

            // 3. 设置出库时间
            if (bo.getOutboundDate() == null) {
                bo.setOutboundDate(LocalDate.now());
            }

            // 4. 填充冗余字段
            fillRedundantFields(bo);

            // 5. 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 6. 转换为实体并校验
            SaleOutbound add = MapstructUtils.convert(bo, SaleOutbound.class);
            validEntityBeforeSave(add);

            // 7. 插入主表
            boolean flag = baseMapper.insert(add) > 0;
            if (!flag) {
                throw new ServiceException("新增销售出库失败");
            }

            bo.setOutboundId(add.getOutboundId());

            // 8. 处理明细数据
            if (bo.getItems() != null && !bo.getItems().isEmpty()) {
                // 填充明细的冗余字段
                fillItemsData(bo.getItems(), add.getOutboundId());

                // 计算明细金额（价税分离）
                calculateItemAmounts(bo.getItems());

                // 插入明细
                // TODO: 添加insertOrUpdateBatch方法
                // itemService.insertOrUpdateBatch(bo.getItems());

                // 计算并更新主表汇总金额
                updateTotalAmounts(add.getOutboundId());
            }

            log.info("新增销售出库成功：{}", add.getOutboundName());
            return true;
        } catch (Exception e) {
            log.error("新增销售出库失败：{}", e.getMessage(), e);
            throw new ServiceException("新增销售出库失败：" + e.getMessage());
        }
    }

    /**
     * 修改销售出库
     *
     * @param bo 销售出库
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SaleOutboundBo bo) {
        try {
            // 1. 填充冗余字段
            fillRedundantFields(bo);

            // 2. 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 3. 转换为实体并校验
            SaleOutbound update = MapstructUtils.convert(bo, SaleOutbound.class);
            validEntityBeforeSave(update);

            // 4. 更新主表
            boolean result = baseMapper.updateById(update) > 0;
            if (!result) {
                throw new ServiceException("修改销售出库失败");
            }

            // 5. 处理明细数据
            if (bo.getItems() != null) {
                // 填充明细的冗余字段
                fillItemsData(bo.getItems(), bo.getOutboundId());

                // 计算明细金额（价税分离）
                calculateItemAmounts(bo.getItems());

                // 更新明细
                // TODO: 添加insertOrUpdateBatch方法
                // itemService.insertOrUpdateBatch(bo.getItems());

                // 重新计算并更新主表汇总金额
                updateTotalAmounts(bo.getOutboundId());
            }

            log.info("修改销售出库成功：{}", update.getOutboundName());
            return true;
        } catch (Exception e) {
            log.error("修改销售出库失败：{}", e.getMessage(), e);
            throw new ServiceException("修改销售出库失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleOutbound entity) {
        // 校验必填字段
        if (StringUtils.isBlank(entity.getOutboundCode())) {
            throw new ServiceException("出库单编码不能为空");
        }
        if (StringUtils.isBlank(entity.getOutboundName())) {
            throw new ServiceException("出库单名称不能为空");
        }
        if (entity.getOrderId() == null) {
            throw new ServiceException("关联销售订单不能为空");
        }
        if (entity.getCustomerId() == null) {
            throw new ServiceException("客户不能为空");
        }

        // 校验出库单编码唯一性
        if (StringUtils.isNotBlank(entity.getOutboundCode())) {
            LambdaQueryWrapper<SaleOutbound> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleOutbound::getOutboundCode, entity.getOutboundCode());
            if (entity.getOutboundId() != null) {
                wrapper.ne(SaleOutbound::getOutboundId, entity.getOutboundId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("出库单编码已存在：" + entity.getOutboundCode());
            }
        }
    }

    /**
     * 校验并批量删除销售出库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验出库单状态，只有草稿状态的出库单才能删除
            List<SaleOutbound> outbounds = baseMapper.selectByIds(ids);
            for (SaleOutbound outbound : outbounds) {
                if (SaleOutboundStatus.DRAFT != outbound.getOutboundStatus()) {
                    throw new ServiceException("出库单[" + outbound.getOutboundCode() + "]不是草稿状态，无法删除");
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 确认销售出库单
     *
     * @param outboundId 出库单ID
     * @return 是否确认成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmOutbound(Long outboundId) {
        SaleOutbound outbound = baseMapper.selectById(outboundId);
        if (outbound == null) {
            throw new ServiceException("销售出库单不存在");
        }

        // 校验出库单状态
        if (SaleOutboundStatus.DRAFT != outbound.getOutboundStatus()) {
            throw new ServiceException("只有草稿状态的出库单才能确认");
        }

        SaleOutboundItemBo queryBo = new SaleOutboundItemBo();
        queryBo.setOutboundId(outboundId);
        List<SaleOutboundItemVo> items = itemService.queryList(queryBo);
        if (items.isEmpty()) {
            throw new ServiceException("出库明细不能为空");
        }

        // 更新出库单状态
        outbound.setOutboundStatus(SaleOutboundStatus.PENDING_WAREHOUSE);
        return baseMapper.updateById(outbound) > 0;
    }

    /**
     * 批量确认销售出库单
     *
     * @param outboundIds 出库单ID集合
     * @return 是否确认成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchConfirmOutbounds(Collection<Long> outboundIds) {
        for (Long outboundId : outboundIds) {
            confirmOutbound(outboundId);
        }
        return true;
    }

    /**
     * 取消销售出库单
     *
     * @param outboundId 出库单ID
     * @param reason     取消原因
     * @return 是否取消成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOutbound(Long outboundId, String reason) {
        SaleOutbound outbound = baseMapper.selectById(outboundId);
        if (outbound == null) {
            throw new ServiceException("销售出库单不存在");
        }

        // 校验出库单状态，只有草稿和待出库状态的出库单才能取消
        if (!SaleOutboundStatus.DRAFT.getValue().equals(outbound.getOutboundStatus())
            && !SaleOutboundStatus.PENDING_WAREHOUSE.getValue().equals(outbound.getOutboundStatus())) {
            throw new ServiceException("只有草稿或待出库状态的出库单才能取消");
        }

        try {
            // 更新出库单状态和取消原因
            if (StringUtils.isNotBlank(reason)) {
                String currentRemark = StringUtils.isBlank(outbound.getRemark()) ? "" : outbound.getRemark();
                outbound.setRemark(currentRemark + " [取消原因：" + reason + "]");
            }

            // 设置为取消状态
            outbound.setOutboundStatus(SaleOutboundStatus.CANCELLED);

            boolean result = baseMapper.updateById(outbound) > 0;
            if (result) {
                log.info("销售出库单【{}】取消成功，原因：{}", outbound.getOutboundCode(), reason);
            }
            return result;
        } catch (Exception e) {
            log.error("销售出库单【{}】取消失败：{}", outbound.getOutboundCode(), e.getMessage(), e);
            throw new ServiceException("取消出库单失败：" + e.getMessage());
        }
    }

    /**
     * 完成销售出库
     *
     * @param outboundId 出库单ID
     * @return 是否完成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeOutbound(Long outboundId) {
        SaleOutbound outbound = baseMapper.selectById(outboundId);
        if (outbound == null) {
            throw new ServiceException("销售出库单不存在");
        }

        // 校验出库单状态
        if (!SaleOutboundStatus.PENDING_WAREHOUSE.getValue().equals(outbound.getOutboundStatus())) {
            throw new ServiceException("只有待出库状态的出库单才能完成");
        }

        try {
            // 检查库存可用性
            checkInventoryAvailability(outboundId);

            // 更新出库单状态
            outbound.setOutboundStatus(SaleOutboundStatus.COMPLETED);
            outbound.setOutboundDate(LocalDate.now());

            boolean result = baseMapper.updateById(outbound) > 0;
            if (result) {
                log.info("销售出库单【{}】完成出库", outbound.getOutboundCode());
                // 处理库存扣减逻辑
                processInventoryDeduction(outbound);
            }
            return result;
        } catch (Exception e) {
            log.error("销售出库单【{}】完成失败：{}", outbound.getOutboundCode(), e.getMessage(), e);
            throw new ServiceException("完成出库失败：" + e.getMessage());
        }
    }

    /**
     * 根据销售订单创建出库单
     *
     * @param saleOrderId 销售订单ID
     * @return 创建的出库单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaleOutboundVo createFromSaleOrder(Long saleOrderId) {
        SaleOrderVo saleOrder = saleOrderService.queryById(saleOrderId);
        if (saleOrder == null) {
            throw new ServiceException("销售订单不存在");
        }

        SaleOrderItemBo itemQueryBo = new SaleOrderItemBo();
        itemQueryBo.setOrderId(saleOrderId);
        List<SaleOrderItemVo> orderItems = saleOrderItemService.queryList(itemQueryBo);
        if (orderItems.isEmpty()) {
            throw new ServiceException("销售订单明细不存在");
        }

        // 创建出库单
        SaleOutboundBo outboundBo = new SaleOutboundBo();
        outboundBo.setOutboundName("销售出库-" + saleOrder.getOrderName());
        outboundBo.setOrderId(saleOrder.getOrderId());
        outboundBo.setOrderCode(saleOrder.getOrderCode());
        outboundBo.setOrderName(saleOrder.getOrderName());
        outboundBo.setCustomerId(saleOrder.getCustomerId());
        outboundBo.setCustomerCode(saleOrder.getCustomerCode());
        outboundBo.setCustomerName(saleOrder.getCustomerName());
        outboundBo.setOutboundDate(LocalDate.now());
        outboundBo.setRemark("由销售订单[" + saleOrder.getOrderCode() + "]自动创建");

        List<SaleOutboundItemBo> outboundItems = new ArrayList<>();
        for (SaleOrderItemVo orderItem : orderItems) {
            SaleOutboundItemBo itemBo = new SaleOutboundItemBo();
            itemBo.setProductId(orderItem.getProductId());
            itemBo.setProductCode(orderItem.getProductCode());
            itemBo.setProductName(orderItem.getProductName());
            itemBo.setUnitId(orderItem.getUnitId());
            itemBo.setUnitCode(orderItem.getUnitCode());
            itemBo.setUnitName(orderItem.getUnitName());
            itemBo.setQuantity(orderItem.getQuantity());
            itemBo.setPrice(orderItem.getPrice());
            itemBo.setRemark("来源：销售订单明细");
            outboundItems.add(itemBo);
        }
        outboundBo.setItems(outboundItems);

        // 保存出库单
        insertByBo(outboundBo);

        return queryById(outboundBo.getOutboundId());
    }

    /**
     * 检查是否有关联的出库单
     *
     * @param saleOrderId 销售订单ID
     * @return 是否存在
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean existsByOrderId(Long saleOrderId) {
        LambdaQueryWrapper<SaleOutbound> outboundWrapper = Wrappers.lambdaQuery();
        outboundWrapper.eq(SaleOutbound::getOrderId, saleOrderId);
        return baseMapper.exists(outboundWrapper);
    }

    /**
     * 处理库存扣减逻辑
     *
     * @param outbound 出库单
     */
    private void processInventoryDeduction(SaleOutbound outbound) {
        try {
            // 获取出库明细
            SaleOutboundItemBo queryBo = new SaleOutboundItemBo();
            queryBo.setOutboundId(outbound.getOutboundId());
            List<SaleOutboundItemVo> items = itemService.queryList(queryBo);

            for (SaleOutboundItemVo item : items) {
                // 调用WMS模块的库存扣减服务
                boolean deductResult = inventoryService.adjustInventory(
                    item.getProductId(),
                    item.getLocationId(),
                    item.getQuantity().negate(), // 负数表示扣减
                    "销售出库扣减 - 出库单：" + outbound.getOutboundCode(),
                    outbound.getHandlerId(),
                    outbound.getHandlerName()
                );

                if (!deductResult) {
                    throw new ServiceException("产品【" + item.getProductName() + "】库存扣减失败");
                }

                log.info("销售出库库存扣减成功：产品【{}】数量【{}】库位【{}】",
                    item.getProductName(), item.getQuantity(), item.getLocationCode());

                // 处理批次库存扣减（FIFO原则）
                if (item.getProductId() != null) {
                    processBatchInventoryDeduction(item, outbound);
                }
            }

            log.info("销售出库单【{}】库存扣减处理完成", outbound.getOutboundCode());
        } catch (Exception e) {
            log.error("销售出库单【{}】库存扣减失败：{}", outbound.getOutboundCode(), e.getMessage(), e);
            throw new ServiceException("库存扣减失败：" + e.getMessage());
        }
    }

    /**
     * 处理批次库存扣减（FIFO原则）
     *
     * @param item     出库明细
     * @param outbound 出库单
     */
    private void processBatchInventoryDeduction(SaleOutboundItemVo item, SaleOutbound outbound) {
        try {
            // 获取该产品在该库位的所有可用批次（按先进先出排序）
            List<InventoryBatchVo> availableBatches = inventoryBatchService.getExpiringBatches(
                365, item.getProductId(), item.getLocationId());

            BigDecimal remainingQty = item.getQuantity();

            for (InventoryBatchVo batch : availableBatches) {
                if (remainingQty.compareTo(BigDecimal.ZERO) <= 0) {
                    break; // 已扣减完毕
                }

                // 检查批次可用数量
                // TODO: InventoryBatch实体中没有allocatedQuantity字段，需要重新设计库存分配逻辑
                // 暂时使用全部数量作为可用数量，待实体完善后修正
                BigDecimal batchAvailableQty = batch.getQuantity();
                // 原逻辑（待实体完善后启用）：
                // BigDecimal batchAvailableQty = batch.getQuantity().subtract(
                //     batch.getAllocatedQuantity() != null ? batch.getAllocatedQuantity() : BigDecimal.ZERO);

                if (batchAvailableQty.compareTo(BigDecimal.ZERO) <= 0) {
                    continue; // 该批次无可用库存
                }

                // 计算本批次扣减数量
                BigDecimal deductQty = remainingQty.min(batchAvailableQty);

                // 扣减批次库存
                // TODO: 实现批次库存扣减逻辑
                // inventoryBatchService.deductBatchQuantity(batch.getBatchId(), deductQty);

                remainingQty = remainingQty.subtract(deductQty);

                log.info("批次库存扣减：批次【{}】扣减数量【{}】剩余需扣减【{}】",
                    batch.getInternalBatchNumber(), deductQty, remainingQty);
            }

            if (remainingQty.compareTo(BigDecimal.ZERO) > 0) {
                log.warn("产品【{}】批次库存不足，未扣减数量：{}", item.getProductName(), remainingQty);
            }
        } catch (Exception e) {
            log.error("批次库存扣减失败：{}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 检查库存可用性
     * 重要功能 - 库存不足检查，防止超卖
     *
     * @param outboundId 出库单ID
     */
    private void checkInventoryAvailability(Long outboundId) {
        try {
            // 获取出库明细
            SaleOutboundItemBo queryBo = new SaleOutboundItemBo();
            queryBo.setOutboundId(outboundId);
            List<SaleOutboundItemVo> items = itemService.queryList(queryBo);

            List<String> insufficientItems = new ArrayList<>();

            for (SaleOutboundItemVo item : items) {
                // 1. 查询产品在指定库位的可用库存
                BigDecimal availableQty = inventoryService.getAvailableQuantity(
                    item.getProductId(), item.getLocationId());

                // 2. 检查库存数量是否足够
                if (availableQty.compareTo(item.getQuantity()) < 0) {
                    String insufficientInfo = String.format("产品【%s】库存不足，需要数量：%s，可用数量：%s",
                        item.getProductName(), item.getQuantity(), availableQty);
                    insufficientItems.add(insufficientInfo);
                    log.warn(insufficientInfo);
                } else {
                    log.info("产品【{}】库存检查通过，需要数量：{}，可用数量：{}",
                        item.getProductName(), item.getQuantity(), availableQty);
                }

                // 3. 检查批次库存可用性
                checkBatchInventoryAvailability(item);
            }

            // 如果有库存不足的产品，抛出异常
            if (!insufficientItems.isEmpty()) {
                String errorMessage = "库存不足，无法完成出库：\n" + String.join("\n", insufficientItems);
                throw new ServiceException(errorMessage);
            }

            log.info("出库单【{}】库存检查通过", outboundId);
        } catch (Exception e) {
            log.error("出库单【{}】库存检查失败：{}", outboundId, e.getMessage(), e);
            throw new ServiceException("库存检查失败：" + e.getMessage());
        }
    }

    /**
     * 检查批次库存可用性
     *
     * @param item 出库明细
     */
    private void checkBatchInventoryAvailability(SaleOutboundItemVo item) {
        try {
            // 获取该产品在该库位的所有可用批次
            List<InventoryBatchVo> availableBatches = inventoryBatchService.getExpiringBatches(
                365, item.getProductId(), item.getLocationId());

            // 计算批次总可用数量
            BigDecimal totalBatchAvailableQty = BigDecimal.ZERO;
            int expiredBatchCount = 0;
            int warningBatchCount = 0;

            for (InventoryBatchVo batch : availableBatches) {
                // 检查批次状态
                if ("EXPIRED".equals(batch.getInventoryStatus())) {
                    expiredBatchCount++;
                    continue;
                }
                if ("WARNING".equals(batch.getInventoryStatus())) {
                    warningBatchCount++;
                }

                // 计算批次可用数量
                // TODO: InventoryBatch实体中没有allocatedQuantity字段，需要重新设计库存分配逻辑
                // 暂时使用全部数量作为可用数量，待实体完善后修正
                BigDecimal batchAvailableQty = batch.getQuantity();
                // 原逻辑（待实体完善后启用）：
                // BigDecimal batchAvailableQty = batch.getQuantity().subtract(
                //     batch.getAllocatedQuantity() != null ? batch.getAllocatedQuantity() : BigDecimal.ZERO);

                if (batchAvailableQty.compareTo(BigDecimal.ZERO) > 0) {
                    totalBatchAvailableQty = totalBatchAvailableQty.add(batchAvailableQty);
                }
            }

            // 检查批次库存是否足够
            if (totalBatchAvailableQty.compareTo(item.getQuantity()) < 0) {
                log.warn("产品【{}】批次库存不足，需要数量：{}，批次可用数量：{}",
                    item.getProductName(), item.getQuantity(), totalBatchAvailableQty);
            }

            // 记录批次状态信息
            if (expiredBatchCount > 0) {
                log.warn("产品【{}】存在{}个过期批次", item.getProductName(), expiredBatchCount);
            }
            if (warningBatchCount > 0) {
                log.warn("产品【{}】存在{}个即将过期批次", item.getProductName(), warningBatchCount);
            }

        } catch (Exception e) {
            log.error("批次库存检查失败：{}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 从销售出库单生成应收账款
     *
     * @param outboundId     出库单ID
     * @param receivableType 应收类型
     * @param dueDate        到期日期
     * @param paymentTerms   付款条件
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 应收账款ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long generateArReceivableFromOutbound(Long outboundId, String receivableType,
                                                 LocalDate dueDate, String paymentTerms,
                                                 Long operatorId, String operatorName) {
        try {
            // 1. 获取出库单信息
            SaleOutbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("销售出库单不存在");
            }

            // 校验出库单状态
            if (!SaleOutboundStatus.COMPLETED.getValue().equals(outbound.getOutboundStatus())) {
                throw new ServiceException("只有已完成的出库单才能生成应收账款");
            }

            // 检查是否已经生成过应收账款
            if (finArReceivableService.existsByOutboundId(outboundId)) {
                throw new ServiceException("该出库单已生成应收账款，不能重复生成");
            }

            // 2. 获取出库明细
            // TODO: queryByOutboundId方法可能不存在，使用queryList方法替代
            SaleOutboundItemBo queryBo = new SaleOutboundItemBo();
            queryBo.setOutboundId(outboundId);
            List<SaleOutboundItemVo> outboundItems = itemService.queryList(queryBo);
            if (outboundItems.isEmpty()) {
                throw new ServiceException("出库单没有明细，无法生成应收账款");
            }

            // 3. 调用财务服务生成应收账款
            Long receivableId = finArReceivableService.generateFromSaleOutbound(outboundId,
                receivableType, dueDate, paymentTerms, operatorId, operatorName);

            // 4. 更新出库单状态，标记已生成应收
            // 注意：这里需要确保SaleOutboundStatus枚举中有RECEIVABLE_GENERATED状态
            // 暂时使用备注字段记录
            String currentRemark = StringUtils.isBlank(outbound.getRemark()) ? "" : outbound.getRemark();
            outbound.setRemark(currentRemark + " [已生成应收账款ID:" + receivableId + "]");
            baseMapper.updateById(outbound);

            log.info("从销售出库单生成应收账款成功 - 出库单: {}, 应收账款ID: {}, 操作人: {}",
                outbound.getOutboundCode(), receivableId, operatorName);

            return receivableId;
        } catch (Exception e) {
            log.error("从销售出库单生成应收账款失败 - 出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);
            throw new ServiceException("生成应收账款失败：" + e.getMessage());
        }
    }

    /**
     * 批量从出库单生成应收账款
     *
     * @param outboundIds    出库单ID列表
     * @param receivableType 应收类型
     * @param dueDate        到期日期
     * @param paymentTerms   付款条件
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 批量生成结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchGenerateArReceivables(List<Long> outboundIds, String receivableType,
                                                          LocalDate dueDate, String paymentTerms,
                                                          Long operatorId, String operatorName) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (Long outboundId : outboundIds) {
                try {
                    Long receivableId = generateArReceivableFromOutbound(outboundId, receivableType,
                        dueDate, paymentTerms, operatorId, operatorName);

                    successList.add(Map.of(
                        "outboundId", outboundId,
                        "receivableId", receivableId,
                        "status", "SUCCESS"
                    ));
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "outboundId", outboundId,
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", outboundIds.size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", operatorId);
            result.put("operatorName", operatorName);
            result.put("operationTime", LocalDateTime.now());

            log.info("批量生成应收账款完成 - 总数: {}, 成功: {}, 失败: {}, 操作人: {}",
                outboundIds.size(), successList.size(), failureList.size(), operatorName);

            return result;
        } catch (Exception e) {
            log.error("批量生成应收账款失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量生成应收账款失败：" + e.getMessage());
        }
    }

    /**
     * 出库完成后自动生成应收单
     *
     * @param outboundId   出库单ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否生成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateReceivableAfterOutboundComplete(Long outboundId, Long operatorId, String operatorName) {
        try {
            if (outboundId == null) {
                throw new ServiceException("出库单ID不能为空");
            }

            // 1. 校验出库单状态为已完成
            SaleOutbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("出库单不存在");
            }

            if (!SaleOutboundStatus.COMPLETED.getValue().equals(outbound.getOutboundStatus())) {
                throw new ServiceException("只有已完成的出库单才能生成应收单");
            }

            // 2. 检查是否已生成应收单
            if (finArReceivableService.existsByOutboundId(outboundId)) {
                throw new ServiceException("该出库单已生成应收单，不能重复生成");
            }

            log.info("开始从出库单生成应收单 - 出库单ID: {}, 出库单编号: {}, 操作人: {}",
                outboundId, outbound.getOutboundCode(), operatorName);

            // 3. 从出库单信息生成应收单主表
            Long receivableId = finArReceivableService.generateFromSaleOutbound(
                outboundId,
                outbound.getOrderId(),
                outbound.getCustomerId(),
                operatorId,
                operatorName
            );

            if (receivableId == null) {
                throw new ServiceException("应收单生成失败");
            }

            // 4. 从出库单明细生成应收单明细
            SaleOutboundItemBo queryBo = new SaleOutboundItemBo();
            queryBo.setOutboundId(outboundId);
            List<SaleOutboundItemVo> outboundItems = itemService.queryList(queryBo);

            if (!outboundItems.isEmpty()) {
                List<Long> outboundItemIds = outboundItems.stream()
                    .map(SaleOutboundItemVo::getItemId)
                    .toList();

                Boolean itemsResult = finArReceivableService.generateReceivableItemsFromOutboundItems(
                    receivableId, outboundItemIds, operatorId, operatorName);

                if (!itemsResult) {
                    throw new ServiceException("应收单明细生成失败");
                }
            }

            log.info("从出库单生成应收单成功 - 出库单: {}, 应收单ID: {}, 操作人: {}",
                outbound.getOutboundCode(), receivableId, operatorName);

            return true;
        } catch (Exception e) {
            log.error("从出库单生成应收单失败 - 出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);
            throw new ServiceException("生成应收单失败：" + e.getMessage());
        }
    }

    /**
     * 销售业务完整流程：从出库完成到收款入账
     *
     * @param outboundId    出库单ID
     * @param receiptAmount 收款金额
     * @param accountId     账户ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 完整的业务结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> completeSaleBusinessFlow(Long outboundId, BigDecimal receiptAmount,
                                                        Long accountId, Long operatorId, String operatorName) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始执行销售业务完整流程 - 出库单ID: {}, 收款金额: {}, 操作人: {}",
                outboundId, receiptAmount, operatorName);

            // 1. 出库完成后生成应收单
            Boolean receivableResult = generateReceivableAfterOutboundComplete(outboundId, operatorId, operatorName);
            if (!receivableResult) {
                throw new ServiceException("生成应收单失败");
            }

            // 获取生成的应收单ID
            FinArReceivableVo receivable = finArReceivableService.queryByOutboundId(outboundId);
            if (receivable == null) {
                throw new ServiceException("未找到生成的应收单");
            }
            Long receivableId = receivable.getReceivableId();

            // 2. 从应收单生成收款单
            Long receiptId = finArReceivableService.generateReceiptOrderFromReceivable(
                receivableId, receiptAmount, accountId, operatorId, operatorName);

            if (receiptId == null) {
                throw new ServiceException("生成收款单失败");
            }

            // 3. 进行收款单与应收单核销
            Boolean applyResult = finArReceiptReceivableLinkService.applyReceiptToReceivable(
                receiptId, receivableId, receiptAmount, "销售业务完整流程自动核销");

            if (!applyResult) {
                throw new ServiceException("收款单与应收单核销失败");
            }

            // 4. 生成账户收入流水
            Boolean ledgerResult = finAccountLedgerService.generateIncomeFromReceiptOrder(
                receiptId, accountId, "销售业务完整流程收入");

            if (!ledgerResult) {
                throw new ServiceException("生成账户收入流水失败");
            }

            // 5. 返回完整的业务结果
            result.put("success", true);
            result.put("outboundId", outboundId);
            result.put("receivableId", receivableId);
            result.put("receiptId", receiptId);
            result.put("accountId", accountId);
            result.put("receiptAmount", receiptAmount);
            result.put("message", "销售业务完整流程执行成功");

            log.info("销售业务完整流程执行成功 - 出库单: {}, 应收单: {}, 收款单: {}, 收款金额: {}",
                outboundId, receivableId, receiptId, receiptAmount);

            return result;
        } catch (Exception e) {
            log.error("销售业务完整流程执行失败 - 出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);

            result.put("success", false);
            result.put("outboundId", outboundId);
            result.put("error", e.getMessage());
            result.put("message", "销售业务完整流程执行失败");

            throw new ServiceException("销售业务完整流程执行失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(SaleOutboundBo bo) {
        // 填充销售订单信息
        if (bo.getOrderId() != null) {
            SaleOrderVo saleOrder = saleOrderService.queryById(bo.getOrderId());
            if (saleOrder != null) {
                bo.setOrderCode(saleOrder.getOrderCode());
                bo.setOrderName(saleOrder.getOrderName());
                bo.setCustomerId(saleOrder.getCustomerId());
                bo.setCustomerCode(saleOrder.getCustomerCode());
                bo.setCustomerName(saleOrder.getCustomerName());
            }
        }

        // 填充客户信息
        if (bo.getCustomerId() != null && StringUtils.isEmpty(bo.getCustomerName())) {
            CompanyVo customer = companyService.queryById(bo.getCustomerId());
            if (customer != null) {
                bo.setCustomerCode(customer.getCompanyCode());
                bo.setCustomerName(customer.getCompanyName());
            }
        }
    }

    /**
     * 填充责任人信息
     */
    private void fillResponsiblePersonInfo(SaleOutboundBo bo) {
        Long currentUserId = LoginHelper.getUserId();
        String currentUserName = LoginHelper.getUsername();

        // 如果是新增，设置申请人
        if (bo.getOutboundId() == null) {
            bo.setApplicantId(currentUserId);
            bo.setApplicantName(currentUserName);
        }

        // 设置经办人（每次更新都更新）
        bo.setHandlerId(currentUserId);
        bo.setHandlerName(currentUserName);
    }

    /**
     * 填充明细数据
     */
    private void fillItemsData(List<SaleOutboundItemBo> items, Long outboundId) {
        for (SaleOutboundItemBo item : items) {
            item.setOutboundId(outboundId);

            // 填充产品信息
            if (item.getProductId() != null) {
                ProductVo product = productService.queryById(item.getProductId());
                if (product != null) {
                    item.setProductCode(product.getProductCode());
                    item.setProductName(product.getProductName());
                    // TODO: SaleOutboundItemBo缺少productSpec字段，暂时注释
                    // TODO: 在SaleOutboundItemBo中添加setProductSpecs()方法
                    // item.setProductSpecs(product.getProductSpecs());
                    item.setUnitId(product.getUnitId());
                    item.setUnitName(product.getUnitName());

                    // 如果没有设置价格，使用产品的销售价格
                    if (item.getPrice() == null && product.getSalePrice() != null) {
                        item.setPrice(new BigDecimal(product.getSalePrice().toString()));
                    }
                }
            }
        }
    }

    /**
     * 计算明细金额（价税分离）
     */
    private void calculateItemAmounts(List<SaleOutboundItemBo> items) {
        for (SaleOutboundItemBo item : items) {
            if (item.getQuantity() != null && item.getPrice() != null) {
                BigDecimal quantity = item.getQuantity();
                BigDecimal price = item.getPrice(); // 含税价
                BigDecimal taxRate = item.getTaxRate() != null ? item.getTaxRate() : BigDecimal.ZERO;

                // 计算含税金额
                BigDecimal amount = price.multiply(quantity).setScale(2, RoundingMode.HALF_UP);
                // ✅ 优化：虽然BO类缺少amount字段，但可以通过Entity层更新
                // 将计算结果存储到临时变量，在保存时通过Entity设置
                // TODO: 建议为SaleOutboundItemBo添加amount字段以完善数据传递

                // 计算不含税价格和金额
                if (taxRate.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal divisor = BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
                    BigDecimal priceExclusiveTax = price.divide(divisor, 4, RoundingMode.HALF_UP);
                    BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(quantity).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal taxAmount = amount.subtract(amountExclusiveTax);

                    item.setPriceExclusiveTax(priceExclusiveTax);
                    item.setAmountExclusiveTax(amountExclusiveTax);
                    item.setTaxAmount(taxAmount);
                } else {
                    // 无税情况
                    item.setPriceExclusiveTax(price);
                    item.setAmountExclusiveTax(amount);
                    item.setTaxAmount(BigDecimal.ZERO);
                }
            }
        }
    }

    /**
     * 更新主表汇总金额
     */
    private void updateTotalAmounts(Long outboundId) {
        // 计算明细总金额
        SaleOutboundItemBo queryBo = new SaleOutboundItemBo();
        queryBo.setOutboundId(outboundId);
        List<SaleOutboundItemVo> items = itemService.queryList(queryBo);

        BigDecimal totalAmount = items.stream()
            .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalAmountExclusiveTax = items.stream()
            .map(item -> item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalTaxAmount = items.stream()
            .map(item -> item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 更新主表（使用临时变量字段）
        SaleOutbound update = new SaleOutbound();
        update.setOutboundId(outboundId);
        // ✅ 恢复：SaleOutbound实体确实有这些临时变量字段
        update.setTotalAmount(totalAmount);
        // TODO: SaleOutbound实体缺少totalAmountExclusiveTax和totalTaxAmount字段
        // update.setTotalAmountExclusiveTax(totalAmountExclusiveTax);
        // update.setTotalTaxAmount(totalTaxAmount);

        // 执行主表更新
        int result = baseMapper.updateById(update);
        if (result > 0) {
            log.info("更新销售出库单汇总金额成功 - 出库单ID: {}, 总金额: {}", outboundId, totalAmount);
        }

        baseMapper.updateById(update);

        log.debug("更新销售出库汇总金额 - 出库单ID: {}, 含税总额: {}, 不含税总额: {}, 税额: {}",
            outboundId, totalAmount, totalAmountExclusiveTax, totalTaxAmount);
    }

}
