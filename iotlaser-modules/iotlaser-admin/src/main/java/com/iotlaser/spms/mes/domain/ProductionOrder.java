package com.iotlaser.spms.mes.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.mes.enums.ProductionOrderStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 生产订单对象 mes_production_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mes_production_order")
public class ProductionOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 生产订单ID
     */
    @TableId(value = "order_id")
    private Long orderId;

    /**
     * 生产订单编码
     */
    private String orderCode;

    /**
     * 生产订单名称
     */
    private String orderName;

    /**
     * 生产订单类型
     */
    private String orderType;

    /**
     * 销售订单ID
     */
    private Long saleOrderId;

    /**
     * 销售订单编码
     */
    private String saleOrderCode;

    /**
     * 销售订单名称
     */
    private String saleOrderName;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * BOMID
     */
    private String bomId;

    /**
     * BOM编码
     */
    private String bomCode;

    /**
     * BOM名称
     */
    private String bomName;

    /**
     * 生产数量
     */
    private BigDecimal quantity;

    /**
     * 已完成数量
     */
    private BigDecimal finishQuantity;

    /**
     * 计划开始日期
     */
    private LocalDate plannedStartDate;

    /**
     * 计划结束日期
     */
    private LocalDate plannedEndDate;

    /**
     * 计划员ID
     */
    private Long plannerId;

    /**
     * 计划员姓名
     */
    private String plannerName;

    /**
     * 车间主管ID
     */
    private Long supervisorId;

    /**
     * 车间主管姓名
     */
    private String supervisorName;

    /**
     * 工单下达时间
     */
    private LocalDateTime releaseTime;

    /**
     * 实际开始时间
     */
    private Date actualStartTime;

    /**
     * 实际完成时间
     */
    private Date actualEndTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 订单状态
     */
    private ProductionOrderStatus orderStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

}
