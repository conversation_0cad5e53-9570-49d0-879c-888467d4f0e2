package com.iotlaser.spms.base.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 编码生成记录对象 base_auto_code_result
 *
 * <AUTHOR> <PERSON>
 * @date 2025/03/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("base_auto_code_result")
public class AutoCodeResult extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "code_id")
    private Long codeId;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 生成日期时间
     */
    private String genDate;

    /**
     * 最后产生的序号
     */
    private Long genIndex;

    /**
     * 最后产生的值
     */
    private String lastResult;

    /**
     * 最后产生的流水号
     */
    private Long lastSerialNo;

    /**
     * 最后传入的参数
     */
    private String lastInputChar;

    /**
     * 备注
     */
    private String remark;

}
