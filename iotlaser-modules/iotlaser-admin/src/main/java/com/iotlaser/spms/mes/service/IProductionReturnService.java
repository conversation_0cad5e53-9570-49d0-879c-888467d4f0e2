package com.iotlaser.spms.mes.service;

import com.iotlaser.spms.mes.domain.bo.ProductionReturnBo;
import com.iotlaser.spms.mes.domain.vo.ProductionReturnVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 生产退料Service接口
 *
 * <AUTHOR>
 * @date 2025/05/07
 */
public interface IProductionReturnService {

    /**
     * 查询生产退料
     *
     * @param returnId 主键
     * @return 生产退料
     */
    ProductionReturnVo queryById(Long returnId);

    /**
     * 分页查询生产退料列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产退料分页列表
     */
    TableDataInfo<ProductionReturnVo> queryPageList(ProductionReturnBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的生产退料列表
     *
     * @param bo 查询条件
     * @return 生产退料列表
     */
    List<ProductionReturnVo> queryList(ProductionReturnBo bo);

    /**
     * 新增生产退料
     *
     * @param bo 生产退料
     * @return 是否新增成功
     */
    Boolean insertByBo(ProductionReturnBo bo);

    /**
     * 修改生产退料
     *
     * @param bo 生产退料
     * @return 是否修改成功
     */
    Boolean updateByBo(ProductionReturnBo bo);

    /**
     * 校验并批量删除生产退料信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认生产退料单
     *
     * @param returnId 退料单ID
     * @return 是否确认成功
     */
    Boolean confirmReturn(Long returnId);

    /**
     * 批量确认生产退料单
     *
     * @param returnIds 退料单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmReturns(Collection<Long> returnIds);

    /**
     * 完成生产退料入库
     *
     * @param returnId 退料单ID
     * @return 是否完成成功
     */
    Boolean completeReturn(Long returnId);

    /**
     * 取消生产退料单
     *
     * @param returnId 退料单ID
     * @param reason   取消原因
     * @return 是否取消成功
     */
    Boolean cancelReturn(Long returnId, String reason);

    /**
     * 根据生产领料单创建退料单
     *
     * @param productionIssueId 生产领料单ID
     * @return 创建的退料单
     */
    ProductionReturnVo createFromProductionIssue(Long productionIssueId);
}
