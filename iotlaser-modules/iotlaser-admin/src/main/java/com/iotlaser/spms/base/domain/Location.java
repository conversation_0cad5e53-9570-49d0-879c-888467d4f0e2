package com.iotlaser.spms.base.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 位置库位对象 base_location
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("base_location")
public class Location extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 位置库位ID
     */
    @TableId(value = "location_id")
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 库位类型
     */
    private String locationType;

    /**
     * 上级节点
     */
    private Long parentId;

    /**
     * 排列顺序
     */
    private Long orderNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

}
