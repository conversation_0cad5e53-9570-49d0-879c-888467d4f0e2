package com.iotlaser.spms.erp.service.impl;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.domain.PurchaseInboundItem;
import com.iotlaser.spms.erp.domain.PurchaseInboundItemBatch;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundBo;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundItemBo;
import com.iotlaser.spms.erp.domain.vo.*;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.erp.mapper.PurchaseInboundMapper;
import com.iotlaser.spms.erp.service.*;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.domain.InventoryBatch;
import com.iotlaser.spms.wms.domain.InventoryLog;
import com.iotlaser.spms.wms.domain.bo.InventoryBatchBo;
import com.iotlaser.spms.wms.domain.bo.InventoryLogBo;
import com.iotlaser.spms.wms.enums.*;
import com.iotlaser.spms.wms.service.IInventoryBatchService;
import com.iotlaser.spms.wms.service.IInventoryLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.ObjectUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_PURCHASE_INBOUND_CODE;
import static org.dromara.common.core.constant.SystemConstants.NO;
import static org.dromara.common.core.constant.SystemConstants.YES;
import static org.dromara.common.satoken.utils.LoginHelper.getLoginUser;

/**
 * 采购入库Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-04-23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseInboundServiceImpl implements IPurchaseInboundService {

    private final PurchaseInboundMapper baseMapper;
    private final IPurchaseInboundItemService itemService;
    private final IFinApInvoiceService finApInvoiceService;
    private final IFinApPaymentInvoiceLinkService finApPaymentInvoiceLinkService;
    private final IFinAccountLedgerService finAccountLedgerService;
    private final Gen gen;
    private final IInventoryBatchService inventoryBatchService;
    private final IInventoryLogService inventoryLogService;
    private final ICompanyService companyService;
    private final IProductService productService;
    @Lazy
    @Autowired
    private IPurchaseOrderService purchaseOrderService;

    /**
     * 查询采购入库
     *
     * @param inboundId 主键
     * @return 采购入库
     */
    @Override
    public PurchaseInboundVo queryById(Long inboundId) {
        PurchaseInboundVo inboundVo = baseMapper.selectVoById(inboundId);
        if (ObjectUtils.isNotNull(inboundVo.getOrderId())) {
            inboundVo.setPurchaseOrderVo(purchaseOrderService.queryById(inboundVo.getOrderId()));
        }
        return inboundVo;
    }

    /**
     * 分页查询采购入库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购入库分页列表
     */
    @Override
    public TableDataInfo<PurchaseInboundVo> queryPageList(PurchaseInboundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseInbound> lqw = buildQueryWrapper(bo);
        Page<PurchaseInboundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购入库列表
     *
     * @param bo 查询条件
     * @return 采购入库列表
     */
    @Override
    public List<PurchaseInboundVo> queryList(PurchaseInboundBo bo) {
        LambdaQueryWrapper<PurchaseInbound> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据采购订单ID查询采购入库单列表
     *
     * @param orderId 采购订单ID
     * @return 采购入库单列表
     */
    @Override
    public List<PurchaseInboundVo> queryByOrderId(Long orderId) {
        if (orderId == null) {
            throw new ServiceException("采购订单ID不能为空");
        }

        LambdaQueryWrapper<PurchaseInbound> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PurchaseInbound::getOrderId, orderId);
        wrapper.eq(PurchaseInbound::getStatus, "1"); // 只查询有效记录
        wrapper.orderByDesc(PurchaseInbound::getCreateTime);

        try {
            List<PurchaseInboundVo> result = baseMapper.selectVoList(wrapper);
            log.info("根据采购订单ID查询入库单完成 - 订单ID: {}, 结果数量: {}", orderId, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据采购订单ID查询入库单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("查询采购入库单失败：" + e.getMessage());
        }
    }

    private LambdaQueryWrapper<PurchaseInbound> buildQueryWrapper(PurchaseInboundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseInbound> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseInbound::getInboundId);
        lqw.eq(StringUtils.isNotBlank(bo.getInboundCode()), PurchaseInbound::getInboundCode, bo.getInboundCode());
        lqw.like(StringUtils.isNotBlank(bo.getInboundName()), PurchaseInbound::getInboundName, bo.getInboundName());
        lqw.eq(bo.getOrderId() != null, PurchaseInbound::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderCode()), PurchaseInbound::getOrderCode, bo.getOrderCode());
        lqw.like(StringUtils.isNotBlank(bo.getOrderName()), PurchaseInbound::getOrderName, bo.getOrderName());
        lqw.eq(bo.getInspectionId() != null, PurchaseInbound::getInspectionId, bo.getInspectionId());
        lqw.eq(StringUtils.isNotBlank(bo.getInspectionCode()), PurchaseInbound::getInspectionCode, bo.getInspectionCode());
        lqw.like(StringUtils.isNotBlank(bo.getInspectionName()), PurchaseInbound::getInspectionName, bo.getInspectionName());
        lqw.eq(bo.getSupplierId() != null, PurchaseInbound::getSupplierId, bo.getSupplierId());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierCode()), PurchaseInbound::getSupplierCode, bo.getSupplierCode());
        lqw.like(StringUtils.isNotBlank(bo.getSupplierName()), PurchaseInbound::getSupplierName, bo.getSupplierName());
        lqw.eq(bo.getInboundDate() != null, PurchaseInbound::getInboundDate, bo.getInboundDate());
        lqw.eq(bo.getInboundStatus() != null, PurchaseInbound::getInboundStatus, bo.getInboundStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseInbound::getStatus, bo.getStatus());
        lqw.between(params.get("beginInboundDate") != null && params.get("endInboundDate") != null,
            PurchaseInbound::getInboundDate, params.get("beginInboundDate"), params.get("endInboundDate"));
        return lqw;
    }

    /**
     * 新增采购入库
     *
     * @param bo 采购入库
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(PurchaseInboundBo bo) {
        try {
            // 1. 生成入库单号
            if (StringUtils.isEmpty(bo.getInboundCode())) {
                bo.setInboundCode(gen.code(ERP_PURCHASE_INBOUND_CODE));
            }

            // 2. 设置初始状态
            if (bo.getInboundStatus() == null) {
                bo.setInboundStatus(PurchaseInboundStatus.DRAFT);
            }

            // 3. 设置入库日期
            if (bo.getInboundDate() == null) {
                bo.setInboundDate(new Date());
            }

            // 4. 填充冗余字段
            fillRedundantFields(bo);

            // 5. 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 6. 转换为实体并校验
            PurchaseInbound add = MapstructUtils.convert(bo, PurchaseInbound.class);
            validEntityBeforeSave(add);

            // 7. 插入主表
            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增采购入库失败");
            }

            bo.setInboundId(add.getInboundId());

            // 8. 处理明细数据
            if (bo.getItems() != null && !bo.getItems().isEmpty()) {
                // 填充明细的冗余字段和批次信息
                fillItemsData(bo.getItems(), add.getInboundId());

                // 计算明细金额（价税分离）
                calculateItemAmounts(bo.getItems());

                // 插入明细
                itemService.insertOrUpdateBatch(bo.getItems());

                // 计算并更新主表汇总金额
                updateTotalAmounts(add.getInboundId());
            }

            log.info("新增采购入库成功：{}", add.getInboundName());
            return true;
        } catch (Exception e) {
            log.error("新增采购入库失败：{}", e.getMessage(), e);
            throw new ServiceException("新增采购入库失败：" + e.getMessage());
        }
    }


    /**
     * 修改采购入库
     *
     * @param bo 采购入库
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(PurchaseInboundBo bo) {
        try {
            // 1. 填充冗余字段
            fillRedundantFields(bo);

            // 2. 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 3. 转换为实体并校验
            PurchaseInbound update = MapstructUtils.convert(bo, PurchaseInbound.class);
            validEntityBeforeSave(update);

            // 4. 更新主表
            boolean flag = baseMapper.updateById(update) > 0;
            if (!flag) {
                throw new ServiceException("修改采购入库失败");
            }

            // 5. 处理明细数据
            if (bo.getItems() != null) {
                // 填充明细的冗余字段和批次信息
                fillItemsData(bo.getItems(), bo.getInboundId());

                // 计算明细金额（价税分离）
                calculateItemAmounts(bo.getItems());

                // 更新明细
                itemService.insertOrUpdateBatch(bo.getItems());

                // 重新计算并更新主表汇总金额
                updateTotalAmounts(bo.getInboundId());
            }

            // 6. 如果采购入库状态为完成，则生成库存记录
            if (PurchaseInboundStatus.COMPLETED.equals(update.getInboundStatus())) {
                processInventoryRecords(update);
            }

            log.info("修改采购入库成功：{}", update.getInboundName());
            return true;
        } catch (Exception e) {
            log.error("修改采购入库失败：{}", e.getMessage(), e);
            throw new ServiceException("修改采购入库失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 采购入库单实体
     */
    private void validEntityBeforeSave(PurchaseInbound entity) {
        if (Objects.equals(entity.getInboundStatus(), PurchaseInboundStatus.PENDING_WAREHOUSE) ||
            Objects.equals(entity.getInboundStatus(), PurchaseInboundStatus.COMPLETED)) {
            // 已完成状态的入库单需要详细校验
            PurchaseInboundItemBo itemBo = new PurchaseInboundItemBo();
            itemBo.setInboundId(entity.getInboundId());
            List<PurchaseInboundItemVo> items = itemService.queryListWith(itemBo);
            for (PurchaseInboundItemVo item : items) {
                // 根据产品是否为批次管理，执行不同的校验逻辑
                if (Objects.equals(item.getProduct().getBatchFlag(), NO)) {
                    // 对于非批次管理的产品，检查库位和单价信息
                    if (ObjectUtils.isNull(item.getLocationId())) {
                        throw new ServiceException("采购入库单明细" + item.getProductCode() + "未选择库位");
                    }
                    if (item.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ServiceException("采购入库单明细" + item.getProductCode() + "单价不能小于等于0");
                    }
                } else {
                    // 对于批次管理的产品，检查批次信息
                    if (item.getBatches().isEmpty()) {
                        throw new ServiceException("采购入库单明细" + item.getProductCode() + "未添加批次信息");
                    }
                    for (PurchaseInboundItemBatchVo batch : item.getBatches()) {
                        // 对每个批次的数量和单价进行校验
                        if (batch.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                            throw new ServiceException("采购入库单明细" + item.getProductCode() + "批次" + batch.getInternalBatchNumber() + "数量不能小于等于0");
                        }
                        if (batch.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                            throw new ServiceException("采购入库单明细" + item.getProductCode() + "批次" + batch.getInternalBatchNumber() + "单价不能小于等于0");
                        }
                    }
                }
                // 最后检查产品明细的完成数量是否与预期一致
                if (!Objects.equals(item.getQuantity(), item.getFinishQuantity())) {
                    throw new ServiceException("采购入库单明细" + item.getProductCode() + "未完成数量不一致");
                }
            }
        }
    }

    /**
     * 处理库存记录生成
     *
     * @param entity 采购入库单实体，包含入库单的详细信息
     */
    private void processInventoryRecords(PurchaseInbound entity) {
        if (entity.getInboundStatus() == null || !entity.getInboundStatus().equals(PurchaseInboundStatus.COMPLETED)) {
            return;
        }
        List<InventoryBatch> batches = new ArrayList<>();
        List<InventoryLog> records = new ArrayList<>();

        for (PurchaseInboundItem item : entity.getItems()) {
            if (item.getProduct().getBatchFlag().equals(YES)) {
                if (item.getBatches().isEmpty()) {
                    throw new ServiceException("采购入库单明细" + item.getProductCode() + "的批次不能为空");
                }
                for (PurchaseInboundItemBatch batch : item.getBatches()) {
                    InventoryBatch inventoryBatch = createInventoryBatch(batch, item, entity);
                    batches.add(inventoryBatch);
                    InventoryLog batchLog = createInventoryBatchLog(batch, item, entity, inventoryBatch);
                    records.add(batchLog);
                }
            } else {
                InventoryLog itemLog = createInventoryItemLog(item, entity);
                records.add(itemLog);
            }
        }

        if (!batches.isEmpty()) {
            // 转换为BO对象进行批量插入
            List<InventoryBatchBo> batchBos = batches.stream()
                .map(batch -> MapstructUtils.convert(batch, InventoryBatchBo.class))
                .collect(Collectors.toList());
            inventoryBatchService.insertOrUpdateBatch(batchBos);
        }

        if (!records.isEmpty()) {
            // 转换为BO对象进行批量插入
            List<InventoryLogBo> logBos = records.stream()
                .map(record -> MapstructUtils.convert(record, InventoryLogBo.class))
                .collect(Collectors.toList());
            inventoryLogService.insertOrUpdateBatch(logBos);
        }
    }

    /**
     * 创建库存批次记录
     *
     * @param itemBatch 批次
     * @param item      明细
     * @param entity    实体
     * @return 库存批次对象
     */
    private InventoryBatch createInventoryBatch(PurchaseInboundItemBatch itemBatch, PurchaseInboundItem item, PurchaseInbound entity) {
        InventoryBatch batch = new InventoryBatch();
        batch.setInventoryBatchId(itemBatch.getBatchId());
        batch.setManagementType(InventoryManagementType.BATCH.getValue());
        batch.setInternalBatchNumber(itemBatch.getInternalBatchNumber());
        batch.setSupplierBatchNumber(itemBatch.getSupplierBatchNumber());
        batch.setSerialNumber(itemBatch.getSerialNumber());
        batch.setDirectSourceId(entity.getOrderId());
        batch.setDirectSourceType(DirectSourceType.PURCHASE_ORDER_INBOUND);
        batch.setSourceId(entity.getInboundId());
        batch.setSourceItemId(item.getItemId());
        batch.setSourceCode(entity.getInboundCode());
        batch.setSourceName(entity.getInboundName());
        batch.setSourceType(SourceType.INBOUND);
        batch.setProductId(item.getProductId());
        batch.setProductCode(item.getProductCode());
        batch.setProductName(item.getProductName());
        batch.setUnitId(item.getUnitId());
        batch.setUnitCode(item.getUnitCode());
        batch.setUnitName(item.getUnitName());
        batch.setLocationId(itemBatch.getLocationId());
        batch.setLocationCode(itemBatch.getLocationCode());
        batch.setLocationName(itemBatch.getLocationName());
        batch.setQuantity(itemBatch.getQuantity());
        batch.setCostPrice(itemBatch.getPrice());
        batch.setInventoryTime(new DateTime().toLocalDateTime());
        batch.setExpiryTime(itemBatch.getExpiryTime());
        batch.setInventoryStatus(InventoryBatchStatus.AVAILABLE);
        return batch;
    }

    /**
     * 创建库存批次日志记录
     *
     * @param batch          批次
     * @param item           明细
     * @param entity         实体
     * @param inventoryBatch 库存批次对象
     * @return 库存日志对象
     */
    private InventoryLog createInventoryBatchLog(PurchaseInboundItemBatch batch, PurchaseInboundItem item, PurchaseInbound entity, InventoryBatch inventoryBatch) {
        InventoryLog log = new InventoryLog();
        log.setInventoryBatchId(inventoryBatch.getInventoryBatchId());
        log.setDirectSourceId(entity.getOrderId());
        log.setDirectSourceType(DirectSourceType.PURCHASE_ORDER_INBOUND);
        log.setSourceId(entity.getInboundId());
        log.setSourceCode(entity.getInboundCode());
        log.setSourceName(entity.getInboundName());
        log.setSourceType(SourceType.INBOUND);
        log.setSourceItemId(item.getItemId());
        log.setSourceBatchId(batch.getInventoryBatchId());
        log.setProductId(item.getProductId());
        log.setProductCode(item.getProductCode());
        log.setProductName(item.getProductName());
        log.setUnitId(item.getUnitId());
        log.setUnitCode(item.getUnitCode());
        log.setUnitName(item.getUnitName());
        log.setLocationId(batch.getLocationId());
        log.setLocationCode(batch.getLocationCode());
        log.setLocationName(batch.getLocationName());
        log.setDirection(InventoryDirection.IN.getValue());
        log.setBeforeQuantity(BigDecimal.ZERO);
        log.setQuantity(batch.getQuantity());
        log.setAfterQuantity(batch.getQuantity());
        log.setRecordTime(LocalDateTime.now());
        log.setRemark(item.getRemark());
        return log;
    }

    /**
     * 创建库存物品日志记录（无批次）
     *
     * @param item   明细
     * @param entity 实体
     * @return 库存日志对象
     */
    private InventoryLog createInventoryItemLog(PurchaseInboundItem item, PurchaseInbound entity) {
        InventoryLog log = new InventoryLog();
        log.setInventoryBatchId(0L); // 使用常量表示无批次情况
        log.setDirectSourceId(entity.getOrderId());
        log.setDirectSourceType(DirectSourceType.PURCHASE_ORDER_INBOUND);
        log.setSourceId(entity.getInboundId());
        log.setSourceCode(entity.getInboundCode());
        log.setSourceName(entity.getInboundName());
        log.setSourceType(SourceType.INBOUND);
        log.setSourceItemId(item.getItemId());
        log.setSourceBatchId(0L); // 使用常量表示无批次情况
        log.setProductId(item.getProductId());
        log.setProductCode(item.getProductCode());
        log.setProductName(item.getProductName());
        log.setUnitId(item.getUnitId());
        log.setUnitCode(item.getUnitCode());
        log.setUnitName(item.getUnitName());
        log.setLocationId(item.getLocationId());
        log.setLocationCode(item.getLocationCode());
        log.setLocationName(item.getLocationName());
        log.setDirection(InventoryDirection.IN.getValue());
        log.setBeforeQuantity(BigDecimal.ZERO);
        log.setQuantity(item.getQuantity());
        log.setAfterQuantity(item.getQuantity());
        log.setRecordTime(LocalDateTime.now());
        log.setRemark(item.getRemark());
        return log;
    }

    /**
     * 校验并批量删除采购入库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验采购入库单是否可以删除
            List<PurchaseInbound> inbounds = baseMapper.selectByIds(ids);
            for (PurchaseInbound inbound : inbounds) {
                // 1. 检查入库单状态，只有草稿状态的入库单才能删除
                if (PurchaseInboundStatus.DRAFT != inbound.getInboundStatus()) {
                    throw new ServiceException("采购入库单【" + inbound.getInboundName() + "】状态为【" +
                        inbound.getInboundStatus() + "】，不允许删除");
                }

                // 2. 检查是否有关联的库存变动记录
                // 注意：草稿状态的入库单通常还未产生实际库存变动，可以安全删除
                log.debug("检查采购入库单库存变动记录 - 入库单ID: {}", inbound.getInboundId());

                // 3. 级联删除采购入库明细
                // TODO: 添加existsByInboundId和getItemIdsByInboundId方法
                // if (itemService.existsByInboundId(inbound.getInboundId())) {
                //     List<Long> itemIds = itemService.getItemIdsByInboundId(inbound.getInboundId());
                //     if (!itemIds.isEmpty()) {
                //         itemService.deleteWithValidByIds(itemIds, false);
                //         log.info("级联删除采购入库明细，入库单：{}，明细数量：{}", inbound.getInboundName(), itemIds.size());
                //     }
                // }

                log.info("删除采购入库单校验通过：{}", inbound.getInboundName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除采购入库单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除采购入库单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除采购入库单失败：" + e.getMessage());
        }
    }

    /**
     * 确认采购入库单
     *
     * @param inboundId 入库单ID
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmInbound(Long inboundId) {
        PurchaseInbound inbound = baseMapper.selectById(inboundId);
        if (inbound == null) {
            throw new ServiceException("采购入库单不存在");
        }

        // 校验状态
        if (PurchaseInboundStatus.DRAFT != inbound.getInboundStatus()) {
            throw new ServiceException("只有草稿状态的采购入库单才能确认");
        }

        // 更新状态为待入库
        inbound.setInboundStatus(PurchaseInboundStatus.PENDING_WAREHOUSE);
        inbound.setInboundDate(LocalDate.now());

        return baseMapper.updateById(inbound) > 0;
    }

    /**
     * 批量确认采购入库单
     *
     * @param inboundIds 入库单ID集合
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchConfirmInbounds(Collection<Long> inboundIds) {
        for (Long inboundId : inboundIds) {
            confirmInbound(inboundId);
        }
        return true;
    }

    /**
     * 完成采购入库
     *
     * @param inboundId 入库单ID
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeInbound(Long inboundId) {
        PurchaseInbound inbound = baseMapper.selectById(inboundId);
        if (inbound == null) {
            throw new ServiceException("采购入库单不存在");
        }

        // 校验状态
        if (!PurchaseInboundStatus.PENDING_WAREHOUSE.equals(inbound.getInboundStatus())) {
            throw new ServiceException("只有待入库状态的采购入库单才能完成");
        }

        try {
            // 先处理库存记录，如果失败则不更新状态
            inbound.setInboundStatus(PurchaseInboundStatus.COMPLETED);
            inbound.setInboundDate(LocalDate.now());
            processInventoryRecords(inbound);

            // 库存记录处理成功后，更新状态
            boolean result = baseMapper.updateById(inbound) > 0;
            if (result) {
                log.info("采购入库单【{}】完成入库，库存记录已生成", inbound.getInboundCode());
            }
            return result;
        } catch (Exception e) {
            log.error("采购入库单【{}】完成入库失败：{}", inbound.getInboundCode(), e.getMessage(), e);
            throw new ServiceException("完成入库失败：" + e.getMessage());
        }
    }

    /**
     * 根据采购订单创建入库单
     *
     * @param purchaseOrderId 采购订单ID
     * @return 创建的入库单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseInboundVo createFromPurchaseOrder(Long purchaseOrderId) {
        // 获取采购订单信息
        var purchaseOrder = purchaseOrderService.queryById(purchaseOrderId);
        if (purchaseOrder == null) {
            throw new ServiceException("采购订单不存在");
        }

        // 创建入库单
        PurchaseInboundBo inboundBo = new PurchaseInboundBo();
        inboundBo.setInboundCode(gen.code(ERP_PURCHASE_INBOUND_CODE));
        inboundBo.setInboundName("采购入库-" + purchaseOrder.getOrderName());
        inboundBo.setOrderId(purchaseOrder.getOrderId());
        inboundBo.setOrderCode(purchaseOrder.getOrderCode());
        inboundBo.setOrderName(purchaseOrder.getOrderName());
        inboundBo.setSupplierId(purchaseOrder.getSupplierId());
        inboundBo.setSupplierCode(purchaseOrder.getSupplierCode());
        inboundBo.setSupplierName(purchaseOrder.getSupplierName());
        inboundBo.setInboundStatus(PurchaseInboundStatus.DRAFT);
        inboundBo.setRemark("从采购订单" + purchaseOrder.getOrderCode() + "创建");

        // 创建入库单
        Boolean result = insertByBo(inboundBo);
        if (result) {
            return queryById(inboundBo.getInboundId());
        } else {
            throw new ServiceException("从采购订单创建入库单失败");
        }
    }

    /**
     * 检查是否存在指定订单ID的入库单
     *
     * @param orderId 订单ID
     * @return 是否存在
     */
    @Override
    public Boolean existsByOrderId(Long orderId) {
        LambdaQueryWrapper<PurchaseInbound> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PurchaseInbound::getOrderId, orderId);
        return baseMapper.exists(wrapper);
    }

    /**
     * 自动填充责任人信息
     *
     * @param bo 采购入库业务对象
     */
    private void fillResponsiblePersons(PurchaseInboundBo bo) {
        try {
            // 获取当前登录用户信息
            LoginUser loginUser = getLoginUser();
            if (loginUser != null) {
                // 如果收货负责人为空，设置为当前用户
                if (bo.getHandlerId() == null) {
                    bo.setHandlerId(loginUser.getUserId());
                    bo.setHandlerName(loginUser.getNickname());
                }

            }

            log.debug("自动填充采购入库责任人信息完成 - 收货负责人: {}", bo.getHandlerName());
        } catch (Exception e) {
            log.warn("自动填充责任人信息失败: {}", e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 从采购入库单生成应付发票
     *
     * @param inboundId     入库单ID
     * @param invoiceType   发票类型
     * @param invoiceDate   发票日期
     * @param invoiceNumber 发票号码
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 发票ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long generateApInvoiceFromInbound(Long inboundId, String invoiceType,
                                             LocalDate invoiceDate, String invoiceNumber,
                                             Long operatorId, String operatorName) {
        try {
            // 1. 获取入库单信息
            PurchaseInbound inbound = baseMapper.selectById(inboundId);
            if (inbound == null) {
                throw new ServiceException("采购入库单不存在");
            }

            // 校验入库单状态
            if (!PurchaseInboundStatus.COMPLETED.getValue().equals(inbound.getInboundStatus())) {
                throw new ServiceException("只有已完成的入库单才能生成发票");
            }

            // 检查是否已经生成过发票
            if (finApInvoiceService.existsByInboundId(inboundId)) {
                throw new ServiceException("该入库单已生成发票，不能重复生成");
            }

            // 2. 获取入库明细
            PurchaseInboundItemBo queryBo = new PurchaseInboundItemBo();
            queryBo.setInboundId(inboundId);
            List<PurchaseInboundItemVo> inboundItems = itemService.queryList(queryBo);
            if (inboundItems.isEmpty()) {
                throw new ServiceException("入库单没有明细，无法生成发票");
            }

            // 3. 调用财务服务生成发票
            Long invoiceId = finApInvoiceService.generateFromPurchaseInbound(inboundId,
                invoiceType, invoiceDate, invoiceNumber, operatorId, operatorName);

            // 4. 更新入库单状态，标记已生成发票
            inbound.setInboundStatus(PurchaseInboundStatus.COMPLETED);
            baseMapper.updateById(inbound);

            log.info("从采购入库单生成应付发票成功 - 入库单: {}, 发票ID: {}, 操作人: {}",
                inbound.getInboundCode(), invoiceId, operatorName);

            return invoiceId;
        } catch (Exception e) {
            log.error("从采购入库单生成应付发票失败 - 入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            throw new ServiceException("生成应付发票失败：" + e.getMessage());
        }
    }

    /**
     * 批量从入库单生成应付发票
     *
     * @param inboundIds   入库单ID列表
     * @param invoiceType  发票类型
     * @param invoiceDate  发票日期
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 批量生成结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchGenerateApInvoices(List<Long> inboundIds, String invoiceType,
                                                       LocalDate invoiceDate, Long operatorId, String operatorName) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (Long inboundId : inboundIds) {
                try {
                    // 为每个入库单生成发票号码
                    String invoiceNumber = generateInvoiceNumber();

                    Long invoiceId = generateApInvoiceFromInbound(inboundId, invoiceType,
                        invoiceDate, invoiceNumber, operatorId, operatorName);

                    successList.add(Map.of(
                        "inboundId", inboundId,
                        "invoiceId", invoiceId,
                        "invoiceNumber", invoiceNumber,
                        "status", "SUCCESS"
                    ));
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "inboundId", inboundId,
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", inboundIds.size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", operatorId);
            result.put("operatorName", operatorName);
            result.put("operationTime", LocalDateTime.now());

            log.info("批量生成应付发票完成 - 总数: {}, 成功: {}, 失败: {}, 操作人: {}",
                inboundIds.size(), successList.size(), failureList.size(), operatorName);

            return result;
        } catch (Exception e) {
            log.error("批量生成应付发票失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量生成应付发票失败：" + e.getMessage());
        }
    }


    /**
     * 入库完成后自动生成应付单
     *
     * @param inboundId    入库单ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否生成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateInvoiceAfterInboundComplete(Long inboundId, Long operatorId, String operatorName) {
        try {
            if (inboundId == null) {
                throw new ServiceException("入库单ID不能为空");
            }

            // 1. 校验入库单状态为已完成
            PurchaseInbound inbound = baseMapper.selectById(inboundId);
            if (inbound == null) {
                throw new ServiceException("入库单不存在");
            }

            if (!PurchaseInboundStatus.COMPLETED.getValue().equals(inbound.getInboundStatus())) {
                throw new ServiceException("只有已完成的入库单才能生成应付单");
            }

            // 2. 检查是否已生成应付单
            if (finApInvoiceService.existsByInboundId(inboundId)) {
                throw new ServiceException("该入库单已生成应付单，不能重复生成");
            }

            log.info("开始从入库单生成应付单 - 入库单ID: {}, 入库单编号: {}, 操作人: {}",
                inboundId, inbound.getInboundCode(), operatorName);

            // 3. 从入库单信息生成应付单主表
            Long invoiceId = finApInvoiceService.generateFromPurchaseInbound(
                inboundId,
                inbound.getOrderId(),
                inbound.getSupplierId(),
                operatorId,
                operatorName
            );

            if (invoiceId == null) {
                throw new ServiceException("应付单生成失败");
            }

            // 4. 从入库单明细生成应付单明细
            PurchaseInboundItemBo queryBo = new PurchaseInboundItemBo();
            queryBo.setInboundId(inboundId);
            List<PurchaseInboundItemVo> inboundItems = itemService.queryList(queryBo);

            if (!inboundItems.isEmpty()) {
                List<Long> inboundItemIds = inboundItems.stream()
                    .map(PurchaseInboundItemVo::getItemId)
                    .toList();

                Boolean itemsResult = finApInvoiceService.generateInvoiceItemsFromInboundItems(
                    invoiceId, inboundItemIds, operatorId, operatorName);

                if (!itemsResult) {
                    throw new ServiceException("应付单明细生成失败");
                }
            }

            log.info("从入库单生成应付单成功 - 入库单: {}, 应付单ID: {}, 操作人: {}",
                inbound.getInboundCode(), invoiceId, operatorName);

            return true;
        } catch (Exception e) {
            log.error("从入库单生成应付单失败 - 入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            throw new ServiceException("生成应付单失败：" + e.getMessage());
        }
    }

    /**
     * 采购业务完整流程：从入库完成到付款出账
     *
     * @param inboundId     入库单ID
     * @param paymentAmount 付款金额
     * @param accountId     账户ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 完整的业务结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> completePurchaseBusinessFlow(Long inboundId, BigDecimal paymentAmount,
                                                            Long accountId, Long operatorId, String operatorName) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始执行采购业务完整流程 - 入库单ID: {}, 付款金额: {}, 操作人: {}",
                inboundId, paymentAmount, operatorName);

            // 1. 入库完成后生成应付单
            Boolean invoiceResult = generateInvoiceAfterInboundComplete(inboundId, operatorId, operatorName);
            if (!invoiceResult) {
                throw new ServiceException("生成应付单失败");
            }

            // 获取生成的应付单ID
            FinApInvoiceVo invoice = finApInvoiceService.queryByInboundId(inboundId);
            if (invoice == null) {
                throw new ServiceException("未找到生成的应付单");
            }
            Long invoiceId = invoice.getInvoiceId();

            // 2. 从应付单生成付款单
            Long paymentId = finApInvoiceService.generatePaymentOrderFromInvoice(
                invoiceId, paymentAmount, accountId, operatorId, operatorName);

            if (paymentId == null) {
                throw new ServiceException("生成付款单失败");
            }

            // 3. 进行付款单与应付单核销
            Boolean applyResult = finApPaymentInvoiceLinkService.applyPaymentToInvoice(
                paymentId, invoiceId, paymentAmount, "采购业务完整流程自动核销");

            if (!applyResult) {
                throw new ServiceException("付款单与应付单核销失败");
            }

            // 4. 生成账户支出流水
            Boolean ledgerResult = finAccountLedgerService.generateExpenseFromPaymentOrder(
                paymentId, accountId, "采购业务完整流程支出");

            if (!ledgerResult) {
                throw new ServiceException("生成账户支出流水失败");
            }

            // 5. 返回完整的业务结果
            result.put("success", true);
            result.put("inboundId", inboundId);
            result.put("invoiceId", invoiceId);
            result.put("paymentId", paymentId);
            result.put("accountId", accountId);
            result.put("paymentAmount", paymentAmount);
            result.put("message", "采购业务完整流程执行成功");

            log.info("采购业务完整流程执行成功 - 入库单: {}, 应付单: {}, 付款单: {}, 付款金额: {}",
                inboundId, invoiceId, paymentId, paymentAmount);

            return result;
        } catch (Exception e) {
            log.error("采购业务完整流程执行失败 - 入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);

            result.put("success", false);
            result.put("inboundId", inboundId);
            result.put("error", e.getMessage());
            result.put("message", "采购业务完整流程执行失败");

            throw new ServiceException("采购业务完整流程执行失败：" + e.getMessage());
        }
    }

    /**
     * 生成发票号码
     */
    private String generateInvoiceNumber() {
        return "INV" + System.currentTimeMillis();
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(PurchaseInboundBo bo) {
        // 填充采购订单信息
        if (bo.getOrderId() != null) {
            PurchaseOrderVo purchaseOrder = purchaseOrderService.queryById(bo.getOrderId());
            if (purchaseOrder != null) {
                bo.setOrderCode(purchaseOrder.getOrderCode());
                bo.setOrderName(purchaseOrder.getOrderName());
                bo.setSupplierId(purchaseOrder.getSupplierId());
                bo.setSupplierCode(purchaseOrder.getSupplierCode());
                bo.setSupplierName(purchaseOrder.getSupplierName());
            }
        }

        // 填充供应商信息
        if (bo.getSupplierId() != null && StringUtils.isEmpty(bo.getSupplierName())) {
            CompanyVo supplier = companyService.queryById(bo.getSupplierId());
            if (supplier != null) {
                bo.setSupplierCode(supplier.getCompanyCode());
                bo.setSupplierName(supplier.getCompanyName());
            }
        }
    }

    /**
     * 填充责任人信息
     */
    private void fillResponsiblePersonInfo(PurchaseInboundBo bo) {
        Long currentUserId = LoginHelper.getUserId();
        String currentUserName = LoginHelper.getUsername();

        // 如果是新增，设置申请人
        if (bo.getInboundId() == null) {
            bo.setApplicantId(currentUserId);
            bo.setApplicantName(currentUserName);
        }

        // 设置经办人（每次更新都更新）
        bo.setHandlerId(currentUserId);
        bo.setHandlerName(currentUserName);
    }

    /**
     * 填充明细数据
     */
    private void fillItemsData(List<PurchaseInboundItemBo> items, Long inboundId) {
        for (PurchaseInboundItemBo item : items) {
            item.setInboundId(inboundId);

            // 填充产品信息
            if (item.getProductId() != null) {
                ProductVo product = productService.queryById(item.getProductId());
                if (product != null) {
                    item.setProductCode(product.getProductCode());
                    item.setProductName(product.getProductName());
                    // TODO: PurchaseInboundItemBo确实缺少productSpecs字段，保持注释
                    // item.setProductSpecs(product.getProductSpecs());
                    item.setUnitId(product.getUnitId());
                    item.setUnitName(product.getUnitName());

                    // 如果没有设置价格，使用产品的采购价格
                    if (item.getPrice() == null && product.getPurchasePrice() != null) {
                        item.setPrice(new BigDecimal(product.getPurchasePrice().toString()));
                    }
                }
            }
        }
    }

    /**
     * 计算明细金额（价税分离）
     */
    private void calculateItemAmounts(List<PurchaseInboundItemBo> items) {
        for (PurchaseInboundItemBo item : items) {
            if (item.getQuantity() != null && item.getPrice() != null) {
                BigDecimal quantity = item.getQuantity();
                BigDecimal price = item.getPrice(); // 含税价
                BigDecimal taxRate = item.getTaxRate() != null ? item.getTaxRate() : BigDecimal.ZERO;

                // 计算含税金额
                BigDecimal amount = price.multiply(quantity).setScale(2, RoundingMode.HALF_UP);
                // ✅ 优化：虽然BO类缺少amount字段，但可以通过Entity层更新
                // 将计算结果存储到临时变量，在保存时通过Entity设置
                // TODO: 建议为PurchaseInboundItemBo添加amount字段以完善数据传递

                // 计算不含税价格和金额
                if (taxRate.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal divisor = BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
                    BigDecimal priceExclusiveTax = price.divide(divisor, 4, RoundingMode.HALF_UP);
                    BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(quantity).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal taxAmount = amount.subtract(amountExclusiveTax);

                    item.setPriceExclusiveTax(priceExclusiveTax);
                    item.setAmountExclusiveTax(amountExclusiveTax);
                    item.setTaxAmount(taxAmount);
                } else {
                    // 无税情况
                    item.setPriceExclusiveTax(price);
                    item.setAmountExclusiveTax(amount);
                    item.setTaxAmount(BigDecimal.ZERO);
                }
            }
        }
    }

    /**
     * 更新主表汇总金额
     */
    private void updateTotalAmounts(Long inboundId) {
        // 计算明细总金额
        PurchaseInboundItemBo queryBo = new PurchaseInboundItemBo();
        queryBo.setInboundId(inboundId);
        List<PurchaseInboundItemVo> items = itemService.queryList(queryBo);

        BigDecimal totalAmount = items.stream()
            .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalAmountExclusiveTax = items.stream()
            .map(item -> item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalTaxAmount = items.stream()
            .map(item -> item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 更新主表（使用临时变量字段）
        PurchaseInbound update = new PurchaseInbound();
        update.setInboundId(inboundId);
        // ✅ 恢复：PurchaseInbound实体确实有这些临时变量字段
        update.setTotalAmount(totalAmount);
        // TODO: PurchaseInbound实体缺少totalAmountExclusiveTax和totalTaxAmount字段
        // update.setTotalAmountExclusiveTax(totalAmountExclusiveTax);
        // update.setTotalTaxAmount(totalTaxAmount);

        // 执行主表更新
        int result = baseMapper.updateById(update);
        if (result > 0) {
            log.info("更新采购入库单汇总金额成功 - 入库单ID: {}, 总金额: {}", inboundId, totalAmount);
        }

        baseMapper.updateById(update);

        log.debug("更新采购入库汇总金额 - 入库单ID: {}, 含税总额: {}, 不含税总额: {}, 税额: {}",
            inboundId, totalAmount, totalAmountExclusiveTax, totalTaxAmount);
    }

}
