package com.iotlaser.spms.mes.domain.bo;

import com.iotlaser.spms.mes.domain.ProductionReturn;
import com.iotlaser.spms.mes.enums.ProductionReturnStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 生产退料业务对象 mes_production_return
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductionReturn.class, reverseConvertGenerate = false)
public class ProductionReturnBo extends BaseEntity {

    /**
     * 退料单ID
     */
    @NotNull(message = "退料单ID不能为空", groups = {EditGroup.class})
    private Long returnId;

    /**
     * 退料单编号
     */
    @NotBlank(message = "退料单编号不能为空", groups = {EditGroup.class})
    private String returnCode;

    /**
     * 退料单名称
     */
    private String returnName;

    /**
     * 生产订单ID
     */
    private Long orderId;

    /**
     * 生产订单编码
     */
    private String orderCode;

    /**
     * 生产订单名称
     */
    private String orderName;

    /**
     * 检验单ID
     */
    private Long inspectionId;

    /**
     * 检验单编号
     */
    private String inspectionCode;

    /**
     * 检验单名称
     */
    private String inspectionName;

    /**
     * 退料时间
     */
    @NotNull(message = "退料时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date returnTime;

    /**
     * 退料状态
     */
    private ProductionReturnStatus returnStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 明细
     */
    private List<ProductionReturnItemBo> items;
}
