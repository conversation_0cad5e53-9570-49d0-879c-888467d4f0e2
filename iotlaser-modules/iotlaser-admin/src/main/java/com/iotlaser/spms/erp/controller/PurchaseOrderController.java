package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.PurchaseOrderBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import com.iotlaser.spms.erp.service.IPurchaseOrderService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购订单
 *
 * <AUTHOR> Kai
 * @date 2025-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/purchaseOrder")
public class PurchaseOrderController extends BaseController {

    private final IPurchaseOrderService purchaseOrderService;

    /**
     * 查询采购订单列表
     */
    @SaCheckPermission("erp:purchaseOrder:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseOrderVo> list(PurchaseOrderBo bo, PageQuery pageQuery) {
        return purchaseOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出采购订单列表
     */
    @SaCheckPermission("erp:purchaseOrder:export")
    @Log(title = "采购订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseOrderBo bo, HttpServletResponse response) {
        List<PurchaseOrderVo> list = purchaseOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "采购订单", PurchaseOrderVo.class, response);
    }

    /**
     * 获取采购订单详细信息
     *
     * @param orderId 主键
     */
    @SaCheckPermission("erp:purchaseOrder:query")
    @GetMapping("/{orderId}")
    public R<PurchaseOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                      @PathVariable Long orderId) {
        return R.ok(purchaseOrderService.queryById(orderId));
    }

    /**
     * 新增采购订单
     */
    @SaCheckPermission("erp:purchaseOrder:add")
    @Log(title = "采购订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PurchaseOrderBo bo) {
        return toAjax(purchaseOrderService.insertByBo(bo));
    }

    /**
     * 修改采购订单
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "采购订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PurchaseOrderBo bo) {
        return toAjax(purchaseOrderService.updateByBo(bo));
    }

    /**
     * 删除采购订单
     *
     * @param orderIds 主键串
     */
    @SaCheckPermission("erp:purchaseOrder:remove")
    @Log(title = "采购订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] orderIds) {
        return toAjax(purchaseOrderService.deleteWithValidByIds(List.of(orderIds), true));
    }

    /**
     * 确认采购订单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "采购订单确认", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{orderId}")
    public R<Void> confirm(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(purchaseOrderService.confirmOrder(orderId));
    }

    /**
     * 批量确认采购订单
     *
     * @param orderIds 订单ID数组
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "采购订单批量确认", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "订单ID不能为空") @RequestBody Long[] orderIds) {
        return toAjax(purchaseOrderService.batchConfirmOrders(List.of(orderIds)));
    }

    /**
     * 取消采购订单
     *
     * @param orderId 订单ID
     * @param reason  取消原因
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "采购订单取消", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{orderId}")
    public R<Void> cancel(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId,
                          @RequestParam(required = false) String reason) {
        return toAjax(purchaseOrderService.cancelOrder(orderId, reason));
    }

    /**
     * 关闭采购订单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "采购订单关闭", businessType = BusinessType.UPDATE)
    @PostMapping("/close/{orderId}")
    public R<Void> close(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(purchaseOrderService.closeOrder(orderId));
    }
}
