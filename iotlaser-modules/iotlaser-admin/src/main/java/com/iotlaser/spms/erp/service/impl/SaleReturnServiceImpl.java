package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.SaleReturn;
import com.iotlaser.spms.erp.domain.bo.SaleReturnBo;
import com.iotlaser.spms.erp.domain.bo.SaleReturnItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import com.iotlaser.spms.erp.enums.SaleReturnStatus;
import com.iotlaser.spms.erp.mapper.SaleReturnMapper;
import com.iotlaser.spms.erp.service.ISaleOutboundService;
import com.iotlaser.spms.erp.service.ISaleReturnItemService;
import com.iotlaser.spms.erp.service.ISaleReturnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_SALE_RETURN_CODE;

/**
 * 销售退货Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleReturnServiceImpl implements ISaleReturnService {

    private final SaleReturnMapper baseMapper;
    private final Gen gen;
    private final ISaleOutboundService saleOutboundService;
    private final ISaleReturnItemService itemService;

    /**
     * 查询销售退货
     *
     * @param returnId 主键
     * @return 销售退货
     */
    @Override
    public SaleReturnVo queryById(Long returnId) {
        return baseMapper.selectVoById(returnId);
    }

    /**
     * 分页查询销售退货列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售退货分页列表
     */
    @Override
    public TableDataInfo<SaleReturnVo> queryPageList(SaleReturnBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleReturn> lqw = buildQueryWrapper(bo);
        Page<SaleReturnVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售退货列表
     *
     * @param bo 查询条件
     * @return 销售退货列表
     */
    @Override
    public List<SaleReturnVo> queryList(SaleReturnBo bo) {
        LambdaQueryWrapper<SaleReturn> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleReturn> buildQueryWrapper(SaleReturnBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleReturn> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleReturn::getReturnId);
        lqw.eq(StringUtils.isNotBlank(bo.getReturnCode()), SaleReturn::getReturnCode, bo.getReturnCode());
        lqw.like(StringUtils.isNotBlank(bo.getReturnName()), SaleReturn::getReturnName, bo.getReturnName());
        lqw.eq(bo.getCustomerId() != null, SaleReturn::getCustomerId, bo.getCustomerId());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerCode()), SaleReturn::getCustomerCode, bo.getCustomerCode());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), SaleReturn::getCustomerName, bo.getCustomerName());
        lqw.eq(bo.getReturnTime() != null, SaleReturn::getReturnTime, bo.getReturnTime());
        if (bo.getReturnStatus() != null) {
            lqw.eq(SaleReturn::getReturnStatus, bo.getReturnStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleReturn::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增销售退货
     *
     * @param bo 销售退货
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(SaleReturnBo bo) {
        try {
            if (StringUtils.isEmpty(bo.getReturnCode())) {
                bo.setReturnCode(gen.code(ERP_SALE_RETURN_CODE));
            }
            bo.setReturnStatus(SaleReturnStatus.DRAFT);
            SaleReturn add = MapstructUtils.convert(bo, SaleReturn.class);
            validEntityBeforeSave(add);
            // 插入数据库
            int result = baseMapper.insert(add);
            if (result > 0) {
                bo.setReturnId(add.getReturnId());
                if (bo.getItems() != null && !bo.getItems().isEmpty()) {
                    // 设置退货明细的退货单ID
                    bo.getItems().forEach(item -> {
                        item.setReturnId(add.getReturnId());
                    });
                    itemService.insertOrUpdateBatch(bo.getItems());
                }
                return true;
            }
            return false;
        } catch (Exception e) {
            throw new ServiceException("新增销售退货失败：" + e.getMessage());
        }
    }

    /**
     * 修改销售退货
     *
     * @param bo 销售退货
     * @return 修改后的销售退货
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(SaleReturnBo bo) {
        try {
            // 将传入的BO对象转换为实体类对象
            SaleReturn update = MapstructUtils.convert(bo, SaleReturn.class);
            // 在保存前验证实体是否有效
            validEntityBeforeSave(update);
            // 更新销售退货记录，判断更新是否成功
            int result = baseMapper.updateById(update);
            if (result > 0) {
                if (bo.getItems() != null && !bo.getItems().isEmpty()) {
                    List<Long> itemIds = itemService.selectItemIdsByReturnId(bo.getReturnId());
                    // 遍历销售退货的每个物品，分别进行插入或更新操作
                    bo.getItems().forEach(item -> {
                        item.setReturnId(bo.getReturnId());
                        itemIds.remove(item.getItemId());
                    });

                    itemService.insertOrUpdateBatch(bo.getItems());
                    itemService.deleteByIds(itemIds);
                }
                return true;
            }
            return false;
        } catch (Exception e) {
            throw new ServiceException("修改销售退货失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleReturn entity) {
        // 校验退货单编号唯一性
        if (StringUtils.isNotBlank(entity.getReturnCode())) {
            LambdaQueryWrapper<SaleReturn> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleReturn::getReturnCode, entity.getReturnCode());
            if (entity.getReturnId() != null) {
                wrapper.ne(SaleReturn::getReturnId, entity.getReturnId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("退货单编号已存在：" + entity.getReturnCode());
            }
        }
    }

    /**
     * 校验并批量删除销售退货信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验销售退货单是否可以删除
            List<SaleReturn> saleReturns = baseMapper.selectByIds(ids);
            for (SaleReturn saleReturn : saleReturns) {
                // 1. 检查退货单状态，只有草稿状态的退货单才能删除
                if (saleReturn.getReturnStatus() != SaleReturnStatus.DRAFT) {
                    throw new ServiceException("销售退货单【" + saleReturn.getReturnName() + "】状态为【" +
                        saleReturn.getReturnStatus() + "】，不允许删除");
                }

                // 2. 检查是否有关联的库存变动记录
                // 注意：草稿状态的退货单通常还未产生实际库存变动，可以安全删除
                log.debug("检查销售退货单库存变动记录 - 退货单ID: {}", saleReturn.getReturnId());

                // 3. 级联删除销售退货明细
                List<Long> itemIds = itemService.selectItemIdsByReturnId(saleReturn.getReturnId());
                if (!itemIds.isEmpty()) {
                    itemService.deleteWithValidByIds(itemIds, false);
                    log.info("级联删除销售退货明细，退货单：{}，明细数量：{}", saleReturn.getReturnName(), itemIds.size());
                }

                log.info("删除销售退货单校验通过：{}", saleReturn.getReturnName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除销售退货单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除销售退货单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除销售退货单失败：" + e.getMessage());
        }
    }

    /**
     * 确认销售退货单
     *
     * @param returnId 退货单ID
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmReturn(Long returnId) {
        SaleReturn saleReturn = baseMapper.selectById(returnId);
        if (saleReturn == null) {
            throw new ServiceException("退货单不存在");
        }

        // 校验状态
        if (saleReturn.getReturnStatus() != SaleReturnStatus.DRAFT) {
            throw new ServiceException("退货单【" + saleReturn.getReturnCode() + "】状态为【" +
                saleReturn.getReturnStatus() + "】，不允许确认");
        }

        // 更新状态为待退回
        saleReturn.setReturnStatus(SaleReturnStatus.AWAITING_RETURN);
        boolean result = baseMapper.updateById(saleReturn) > 0;

        if (result) {
            log.info("销售退货单【{}】确认成功", saleReturn.getReturnCode());
        }

        return result;
    }

    /**
     * 批量确认销售退货单
     *
     * @param returnIds 退货单ID集合
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchConfirmReturns(Collection<Long> returnIds) {
        for (Long returnId : returnIds) {
            confirmReturn(returnId);
        }
        return true;
    }

    /**
     * 收货确认（客户已退回货物）
     *
     * @param returnId 退货单ID
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean receiveReturn(Long returnId) {
        SaleReturn saleReturn = baseMapper.selectById(returnId);
        if (saleReturn == null) {
            throw new ServiceException("退货单不存在");
        }

        // 校验状态
        if (saleReturn.getReturnStatus() != SaleReturnStatus.AWAITING_RETURN) {
            throw new ServiceException("退货单【" + saleReturn.getReturnCode() + "】状态为【" +
                saleReturn.getReturnStatus() + "】，不允许收货确认");
        }

        // 更新状态为待入库
        saleReturn.setReturnStatus(SaleReturnStatus.PENDING_WAREHOUSE);
        boolean result = baseMapper.updateById(saleReturn) > 0;

        if (result) {
            log.info("销售退货单【{}】收货确认成功", saleReturn.getReturnCode());
        }

        return result;
    }

    /**
     * 完成销售退货入库
     *
     * @param returnId 退货单ID
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeReturn(Long returnId) {
        SaleReturn saleReturn = baseMapper.selectById(returnId);
        if (saleReturn == null) {
            throw new ServiceException("退货单不存在");
        }

        // 校验状态
        if (saleReturn.getReturnStatus() != SaleReturnStatus.PENDING_WAREHOUSE) {
            throw new ServiceException("退货单【" + saleReturn.getReturnCode() + "】状态为【" +
                saleReturn.getReturnStatus() + "】，不允许完成入库");
        }

        // 更新状态为已入库
        saleReturn.setReturnStatus(SaleReturnStatus.COMPLETED);
        boolean result = baseMapper.updateById(saleReturn) > 0;

        if (result) {
            log.info("销售退货单【{}】入库完成", saleReturn.getReturnCode());
            // 处理库存增加逻辑
            processInventoryIncrease(saleReturn);
        }

        return result;
    }

    /**
     * 取消销售退货单
     *
     * @param returnId 退货单ID
     * @param reason   取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelReturn(Long returnId, String reason) {
        SaleReturn saleReturn = baseMapper.selectById(returnId);
        if (saleReturn == null) {
            throw new ServiceException("退货单不存在");
        }

        // 校验状态，只有草稿和待退回状态可以取消
        if (saleReturn.getReturnStatus() != SaleReturnStatus.DRAFT &&
            saleReturn.getReturnStatus() != SaleReturnStatus.AWAITING_RETURN) {
            throw new ServiceException("退货单【" + saleReturn.getReturnCode() + "】状态为【" +
                saleReturn.getReturnStatus() + "】，不允许取消");
        }

        // 更新状态为草稿，并记录取消原因
        saleReturn.setReturnStatus(SaleReturnStatus.DRAFT);
        if (StringUtils.isNotBlank(reason)) {
            saleReturn.setRemark(StringUtils.isBlank(saleReturn.getRemark()) ?
                "取消原因：" + reason : saleReturn.getRemark() + "；取消原因：" + reason);
        }
        boolean result = baseMapper.updateById(saleReturn) > 0;

        if (result) {
            log.info("销售退货单【{}】取消成功，原因：{}", saleReturn.getReturnCode(), reason);
        }

        return result;
    }

    /**
     * 根据销售出库单创建退货单
     *
     * @param saleOutboundId 销售出库单ID
     * @return 创建的退货单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaleReturnVo createFromSaleOutbound(Long saleOutboundId) {
        // 获取销售出库单信息
        SaleOutboundVo outbound = saleOutboundService.queryById(saleOutboundId);
        if (outbound == null) {
            throw new ServiceException("销售出库单不存在");
        }

        // 创建退货单
        SaleReturn saleReturn = new SaleReturn();
        saleReturn.setReturnCode(gen.code(ERP_SALE_RETURN_CODE));
        saleReturn.setReturnName("基于出库单【" + outbound.getOutboundCode() + "】的退货单");
        saleReturn.setCustomerId(outbound.getCustomerId());
        saleReturn.setCustomerCode(outbound.getCustomerCode());
        saleReturn.setCustomerName(outbound.getCustomerName());
        saleReturn.setOutboundId(outbound.getOutboundId());
        saleReturn.setOutboundCode(outbound.getOutboundCode());
        saleReturn.setOutboundName(outbound.getOutboundName());
        saleReturn.setReturnTime(new Date());
        saleReturn.setReturnStatus(SaleReturnStatus.DRAFT);
        saleReturn.setRemark("基于销售出库单【" + outbound.getOutboundCode() + "】创建");

        // 插入退货单
        boolean flag = baseMapper.insert(saleReturn) > 0;
        if (!flag) {
            throw new ServiceException("创建退货单失败");
        }

        // 创建退货明细
        if (outbound.getItems() != null && !outbound.getItems().isEmpty()) {
            List<SaleReturnItemBo> returnItemBos = outbound.getItems().stream().map(outboundItem -> {
                SaleReturnItemBo returnItemBo = new SaleReturnItemBo();
                returnItemBo.setReturnId(saleReturn.getReturnId());
                returnItemBo.setProductId(outboundItem.getProductId());
                returnItemBo.setProductCode(outboundItem.getProductCode());
                returnItemBo.setProductName(outboundItem.getProductName());
                returnItemBo.setUnitId(outboundItem.getUnitId());
                returnItemBo.setUnitCode(outboundItem.getUnitCode());
                returnItemBo.setUnitName(outboundItem.getUnitName());
                returnItemBo.setQuantity(outboundItem.getQuantity());
                returnItemBo.setPrice(outboundItem.getPrice());
                returnItemBo.setRemark("基于出库明细创建");
                return returnItemBo;
            }).collect(Collectors.toList());

            itemService.insertOrUpdateBatch(returnItemBos);
        }

        log.info("基于销售出库单【{}】创建退货单【{}】成功", outbound.getOutboundCode(), saleReturn.getReturnCode());

        return queryById(saleReturn.getReturnId());
    }

    /**
     * 处理库存增加逻辑
     *
     * @param saleReturn 销售退货单
     */
    private void processInventoryIncrease(SaleReturn saleReturn) {
        try {
            // 获取退货明细
            // 注意：这里需要根据实际的退货明细Service进行调用
            // List<SaleReturnItem> items = itemService.getByReturnId(saleReturn.getReturnId());

            // 由于当前可能没有退货明细Service，这里记录日志
            log.info("销售退货库存增加：退货单【{}】", saleReturn.getReturnCode());

            // TODO: 实际项目中需要：
            // 1. 获取销售退货明细
            // 2. 遍历明细，调用库存服务增加库存
            // 3. 记录库存变动日志
            //
            // for (SaleReturnItem item : items) {
            //     inventoryService.increaseInventory(item.getProductId(), item.getLocationId(), item.getQuantity());
            //     log.info("销售退货库存增加：产品【{}】数量【{}】", item.getProductName(), item.getQuantity());
            // }

            log.info("销售退货单【{}】库存增加处理完成", saleReturn.getReturnCode());
        } catch (Exception e) {
            log.error("销售退货单【{}】库存增加失败：{}", saleReturn.getReturnCode(), e.getMessage(), e);
            throw new ServiceException("库存增加失败：" + e.getMessage());
        }
    }
}
