package com.iotlaser.spms.pro.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工序类别枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-15
 */
@Getter
@AllArgsConstructor
public enum ProcessCategory implements IDictEnum<String> {

    MACHINING("machining", "机加工", "机械加工工序"),
    ASSEMBLY("assembly", "装配", "产品装配工序"),
    WELDING("welding", "焊接", "焊接工序"),
    PAINTING("painting", "喷涂", "表面喷涂工序"),
    HEAT_TREATMENT("heat_treatment", "热处理", "热处理工序"),
    INSPECTION("inspection", "检验", "质量检验工序"),
    PACKAGING("packaging", "包装", "产品包装工序"),
    TESTING("testing", "测试", "产品测试工序"),
    OTHER("other", "其他", "其他类别工序");

    public final static String DICT_CODE = "pro_process_category";
    public final static String DICT_NAME = "工序类别";
    public final static String DICT_DESC = "定义生产工艺中各种工序的分类，包括机加工、装配、焊接、喷涂等不同类型的工序";
    /**
     * 类别值
     */
    @EnumValue
    private final String value;
    /**
     * 类别名称
     */
    private final String name;
    /**
     * 类别描述
     */
    private final String desc;

    /**
     * 根据代码获取枚举
     *
     * @param code 类别代码
     * @return 工序类别枚举
     */
    public static ProcessCategory getByCode(String code) {
        for (ProcessCategory category : values()) {
            if (category.getValue().equals(code)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 类别值
     * @return 工序类别枚举
     */
    public static ProcessCategory getByValue(String value) {
        for (ProcessCategory category : values()) {
            if (category.getValue().equals(value)) {
                return category;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
