package com.iotlaser.spms.mes.mapper;

import com.iotlaser.spms.mes.domain.ProductionInboundItemBatch;
import com.iotlaser.spms.mes.domain.vo.ProductionInboundItemBatchVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 生产入库批次明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025/05/23
 */
public interface ProductionInboundItemBatchMapper extends BaseMapperPlus<ProductionInboundItemBatch, ProductionInboundItemBatchVo> {

}
