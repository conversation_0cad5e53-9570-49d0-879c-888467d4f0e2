package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 收款单与应收单核销关系对象 erp_fin_ar_receipt_receivable_link
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_ar_receipt_receivable_link")
public class FinArReceiptReceivableLink extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关系ID
     */
    @TableId(value = "link_id")
    private Long linkId;

    /**
     * 收款ID
     */
    private Long receiptId;

    /**
     * 应收ID
     */
    private Long receivableId;

    /**
     * 核销金额
     */
    private BigDecimal appliedAmount;

    /**
     * 核销日期
     */
    private LocalDate cancellationDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    // ==================== 经办人信息字段（临时变量） ====================
    // TODO: 待数据库结构完善后，这些字段应该持久化到数据库
    // 用于记录核销操作的经办人信息，提升审计追踪能力

    /**
     * 经办人ID（临时变量）
     * TODO: 需要在数据库中添加 handler_id BIGINT COMMENT '经办人ID' 字段
     * 用途：记录核销操作的实际经办人，用于责任追踪和审计
     * 关联：sys_user表的user_id字段
     */
    @TableField(exist = false)
    private Long handlerId;

    /**
     * 经办人姓名（临时变量）
     * TODO: 需要在数据库中添加 handler_name VARCHAR(100) COMMENT '经办人姓名' 字段
     * 用途：冗余存储经办人姓名，提升查询性能和历史数据稳定性
     * 说明：避免因人员信息变更导致历史记录显示异常
     */
    @TableField(exist = false)
    private String handlerName;

    /**
     * 经办时间（临时变量）
     * TODO: 需要在数据库中添加 handle_time DATETIME COMMENT '经办时间' 字段
     * 用途：记录核销操作的实际经办时间，用于业务分析和审计
     * 说明：与create_time区分，handle_time为业务经办时间，create_time为系统创建时间
     */
    @TableField(exist = false)
    private LocalDateTime handleTime;

    /**
     * 经办部门ID（临时变量）
     * TODO: 需要在数据库中添加 handler_dept_id BIGINT COMMENT '经办部门ID' 字段
     * 用途：记录经办人所属部门，用于部门级别的业务分析
     * 关联：sys_dept表的dept_id字段
     */
    @TableField(exist = false)
    private Long handlerDeptId;

    /**
     * 经办部门名称（临时变量）
     * TODO: 需要在数据库中添加 handler_dept_name VARCHAR(100) COMMENT '经办部门名称' 字段
     * 用途：冗余存储部门名称，提升查询性能
     */
    @TableField(exist = false)
    private String handlerDeptName;

    // ==================== 核销审批信息字段（临时变量） ====================
    // TODO: 待数据库结构完善后，这些字段应该持久化到数据库
    // 用于记录核销操作的审批信息，提升业务管控能力

    /**
     * 审批人ID（临时变量）
     * TODO: 需要在数据库中添加 approver_id BIGINT COMMENT '审批人ID' 字段
     * 用途：记录核销操作的审批人，用于审批流程管控
     * 说明：大额核销可能需要审批，小额核销可以直接经办
     */
    @TableField(exist = false)
    private Long approverId;

    /**
     * 审批人姓名（临时变量）
     * TODO: 需要在数据库中添加 approver_name VARCHAR(100) COMMENT '审批人姓名' 字段
     * 用途：冗余存储审批人姓名，提升查询性能
     */
    @TableField(exist = false)
    private String approverName;

    /**
     * 审批时间（临时变量）
     * TODO: 需要在数据库中添加 approve_time DATETIME COMMENT '审批时间' 字段
     * 用途：记录审批操作的时间
     */
    @TableField(exist = false)
    private LocalDateTime approveTime;

    /**
     * 审批意见（临时变量）
     * TODO: 需要在数据库中添加 approve_remark VARCHAR(500) COMMENT '审批意见' 字段
     * 用途：记录审批人的审批意见
     */
    @TableField(exist = false)
    private String approveRemark;


}
