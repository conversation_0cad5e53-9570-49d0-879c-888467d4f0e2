package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.PurchaseInboundItemBatch;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundItemBatchBo;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemBatchVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemVo;
import com.iotlaser.spms.erp.mapper.PurchaseInboundItemBatchMapper;
import com.iotlaser.spms.erp.service.IPurchaseInboundItemBatchService;
import com.iotlaser.spms.erp.service.IPurchaseInboundItemService;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import com.iotlaser.spms.wms.service.IInventoryBatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.PRO_BATCH_CODE;

/**
 * 采购入库批次明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseInboundItemBatchServiceImpl implements IPurchaseInboundItemBatchService {

    private final PurchaseInboundItemBatchMapper baseMapper;
    @Lazy
    @Autowired
    private IPurchaseInboundItemService itemService;
    @Lazy
    @Autowired
    private IPurchaseInboundService purchaseInboundService;
    @Lazy
    @Autowired
    private IInventoryBatchService inventoryBatchService;
    private final Gen gen;

    /**
     * 查询采购入库批次明细
     *
     * @param batchId 主键
     * @return 采购入库批次明细
     */
    @Override
    public PurchaseInboundItemBatchVo queryById(Long batchId) {
        return baseMapper.selectVoById(batchId);
    }

    /**
     * 分页查询采购入库批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购入库批次明细分页列表
     */
    @Override
    public TableDataInfo<PurchaseInboundItemBatchVo> queryPageList(PurchaseInboundItemBatchBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseInboundItemBatch> lqw = buildQueryWrapper(bo);
        Page<PurchaseInboundItemBatchVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购入库批次明细列表
     *
     * @param bo 查询条件
     * @return 采购入库批次明细列表
     */
    @Override
    public List<PurchaseInboundItemBatchVo> queryList(PurchaseInboundItemBatchBo bo) {
        LambdaQueryWrapper<PurchaseInboundItemBatch> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseInboundItemBatch> buildQueryWrapper(PurchaseInboundItemBatchBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseInboundItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseInboundItemBatch::getBatchId);
        lqw.eq(bo.getItemId() != null, PurchaseInboundItemBatch::getItemId, bo.getItemId());
        lqw.eq(bo.getInboundId() != null, PurchaseInboundItemBatch::getInboundId, bo.getInboundId());
        lqw.eq(bo.getInventoryBatchId() != null, PurchaseInboundItemBatch::getInventoryBatchId, bo.getInventoryBatchId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), PurchaseInboundItemBatch::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), PurchaseInboundItemBatch::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), PurchaseInboundItemBatch::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getProductId() != null, PurchaseInboundItemBatch::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), PurchaseInboundItemBatch::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), PurchaseInboundItemBatch::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, PurchaseInboundItemBatch::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), PurchaseInboundItemBatch::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), PurchaseInboundItemBatch::getUnitName, bo.getUnitName());

        // ✅ 优化：移除数量和价格的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, PurchaseInboundItemBatch::getQuantity, bo.getQuantity());
        // 原代码：lqw.eq(bo.getPrice() != null, PurchaseInboundItemBatch::getPrice, bo.getPrice());
        // TODO: 如需要可以后续添加数量和价格的范围查询支持

        lqw.eq(bo.getLocationId() != null, PurchaseInboundItemBatch::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), PurchaseInboundItemBatch::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), PurchaseInboundItemBatch::getLocationName, bo.getLocationName());

        // ✅ 优化：将日期字段改为范围查询，更符合实际业务需求
        // 生产时间范围查询
        lqw.between(params.get("beginProductionTime") != null && params.get("endProductionTime") != null,
            PurchaseInboundItemBatch::getProductionTime, params.get("beginProductionTime"), params.get("endProductionTime"));
        // 失效时间范围查询
        lqw.between(params.get("beginExpiryTime") != null && params.get("endExpiryTime") != null,
            PurchaseInboundItemBatch::getExpiryTime, params.get("beginExpiryTime"), params.get("endExpiryTime"));

        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseInboundItemBatch::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增采购入库批次明细
     *
     * @param bo 采购入库批次明细
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(PurchaseInboundItemBatchBo bo) {
        if (StringUtils.isEmpty(bo.getInternalBatchNumber())) {
            bo.setInternalBatchNumber(gen.code(PRO_BATCH_CODE));
        }
        PurchaseInboundItemBatch add = MapstructUtils.convert(bo, PurchaseInboundItemBatch.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        insertEntityAfterSave(add);
        return flag;
    }

    /**
     * 修改采购入库批次明细
     *
     * @param bo 采购入库批次明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PurchaseInboundItemBatchBo bo) {
        PurchaseInboundItemBatch update = MapstructUtils.convert(bo, PurchaseInboundItemBatch.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        insertEntityAfterSave(update);
        return flag;
    }

    /**
     * 保存前的数据校验
     * 此方法旨在确保采购入库单的批次信息在保存前是有效的，特别是批次数量和价格的校验
     *
     * @param entity 购买入库单批次实体，包含需要保存的批次信息
     */
    private void validEntityBeforeSave(PurchaseInboundItemBatch entity) {
        // 查询当前商品ID下，除当前批次外的已存在批次数量总和
        BigDecimal sum = baseMapper.selectQuantitySumByItemId(entity.getItemId(), entity.getBatchId());

        // 获取与商品ID关联的采购入库单明细信息
        PurchaseInboundItemVo itemVo = itemService.queryByIdWith(entity.getItemId());

        // 检查新增批次数量与已有批次数量之和是否超出采购入库单明细数量
        if (sum.add(entity.getQuantity()).compareTo(itemVo.getQuantity()) > 0) {
            // 如果超出，抛出异常提示用户
            throw new ServiceException("批次数量超出采购入库单明细数量");
        }
    }

    /**
     * 保存前的数据校验
     * 此方法旨在确保采购入库单的批次信息在保存前是有效的，特别是批次数量和价格的校验
     *
     * @param entity 购买入库单批次实体，包含需要保存的批次信息
     */
    private void insertEntityAfterSave(PurchaseInboundItemBatch entity) {
        // 查询当前商品所有批次的数量总和
        BigDecimal sum = baseMapper.selectQuantitySumByItemId(entity.getItemId(), entity.getBatchId());
        // 获取商品详细信息
        PurchaseInboundItemVo itemVo = itemService.queryByIdWith(entity.getItemId());
        // 初始化价格总和
        BigDecimal price = BigDecimal.ZERO;
        // 遍历商品的所有批次，计算价格总和
        if (itemVo.getBatches() != null) {
            for (PurchaseInboundItemBatchVo batch : itemVo.getBatches()) {
                if (batch != null && batch.getPrice() != null && batch.getQuantity() != null) {
                    price = price.add(batch.getPrice().multiply(batch.getQuantity()));
                }
            }
        }
        // 创建一个新的商品实体，用于更新商品的完成数量和价格
        PurchaseInboundItemBo itemBo = new PurchaseInboundItemBo();
        itemBo.setItemId(itemVo.getItemId());
        itemBo.setFinishQuantity(sum.add(entity.getQuantity()));
        itemBo.setPrice(price);
        // 更新商品信息
        itemService.updateByBo(itemBo);
    }


    /**
     * 校验并批量删除采购入库批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验批次明细是否可以删除
            List<PurchaseInboundItemBatch> batches = baseMapper.selectByIds(ids);
            for (PurchaseInboundItemBatch batch : batches) {
                // 1. 检查主表状态，只有草稿状态的入库批次才能删除
                // TODO: 修复purchaseInboundService.getById()方法调用
                // PurchaseInbound inbound = purchaseInboundService.getById(batch.getInboundId());
                // if (inbound == null) {
                //     throw new ServiceException("采购入库批次关联的入库单不存在，批次号：" + batch.getInternalBatchNumber());
                // }
                // if (!PurchaseInboundStatus.DRAFT.getValue().equals(inbound.getInboundStatus())) {
                //     throw new ServiceException("采购入库批次所属入库单【" + inbound.getInboundName() +
                //         "】状态为【" + inbound.getInboundStatus() + "】，不允许删除批次");
                // }

                // 2. 检查是否已关联库存记录
                // 通过内部批次号查询是否已生成库存批次
                if (StringUtils.isNotBlank(batch.getInternalBatchNumber())) {
                    // TODO: 添加queryByInternalBatchNumber方法
                    // InventoryBatchVo inventoryBatch = inventoryBatchService.queryByInternalBatchNumber(batch.getInternalBatchNumber());
                    // if (inventoryBatch != null) {
                    //     throw new ServiceException("采购入库批次【" + batch.getInternalBatchNumber() +
                    //         "】已关联库存记录，不允许删除");
                    // }
                }

                log.info("删除采购入库批次明细校验通过，批次号：{}", batch.getInternalBatchNumber());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除采购入库批次成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除采购入库批次失败：{}", e.getMessage(), e);
            throw new ServiceException("删除采购入库批次失败：" + e.getMessage());
        }
    }

    /**
     * 批量插入采购入库批次明细
     *
     * @param batches 明细
     * @return 是否插入成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertOrUpdateBatch(List<PurchaseInboundItemBatchBo> batches) {
        List<PurchaseInboundItemBatch> entities = MapstructUtils.convert(batches, PurchaseInboundItemBatch.class);
        return baseMapper.insertOrUpdateBatch(entities);
    }

    /**
     * 根据明细ID查询批次列表
     *
     * @param itemId 明细ID
     * @return 批次列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<PurchaseInboundItemBatch> queryByItemId(Long itemId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(PurchaseInboundItemBatch.class)
            .eq(PurchaseInboundItemBatch::getItemId, itemId)
        );
    }
}
