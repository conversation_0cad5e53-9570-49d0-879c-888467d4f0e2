<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iotlaser.spms.common.mapper.SourceItemMapper">

    <resultMap id="BaseResultMap" type="com.iotlaser.spms.common.domain.SourceItem">
        <result column="item_id" property="itemId"/>
        <result column="main_id" property="mainId"/>
        <result column="product_id" property="productId"/>
        <result column="product_code" property="productCode"/>
        <result column="product_name" property="productName"/>
        <result column="unit_id" property="unitId"/>
        <result column="unit_code" property="unitCode"/>
        <result column="unit_name" property="unitName"/>
        <result column="location_id" property="locationId"/>
        <result column="location_code" property="locationCode"/>
        <result column="location_name" property="locationName"/>
        <result column="quantity" property="quantity"/>
        <result column="finish_quantity" property="finishQuantity"/>
        <result column="price" property="price"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <sql id="Base_Column_List">
        item_id
        ,inbound_id
        ,product_id
        ,product_code
        ,product_name
        ,unit_id
        ,unit_code
        ,unit_name
        ,location_id
        ,location_code
        ,location_name
        ,quantity
        ,finish_quantity
        ,price
        ,remark
        ,status
        ,del_flag
        ,create_by
        ,create_time
        ,update_by
        ,update_time
        ,create_dept
        ,tenant_id
    </sql>

    <resultMap id="SourceItemResultMap" type="com.iotlaser.spms.common.domain.SourceItem" extends="BaseResultMap">
        <association property="product" javaType="com.iotlaser.spms.pro.domain.Product">
            <id column="sub_product_product_id" property="productId"/>
            <result column="sub_product_product_code" property="productCode"/>
            <result column="sub_product_product_name" property="productName"/>
            <result column="sub_product_product_type" property="productType"/>
            <result column="sub_product_product_specs" property="productSpecs"/>
            <result column="sub_product_category_id" property="categoryId"/>
            <result column="sub_product_category_code" property="categoryCode"/>
            <result column="sub_product_category_name" property="categoryName"/>
            <result column="sub_product_unit_id" property="unitId"/>
            <result column="sub_product_unit_code" property="unitCode"/>
            <result column="sub_product_unit_name" property="unitName"/>
            <result column="sub_product_code_rule_id" property="codeRuleId"/>
            <result column="sub_product_batch_flag" property="batchFlag"/>
            <result column="sub_product_batch_policy" property="batchPolicy"/>
            <result column="sub_product_shelf_life_days" property="shelfLifeDays"/>
            <result column="sub_product_safe_stock_flag" property="safeStockFlag"/>
            <result column="sub_product_high_value_flag" property="highValueFlag"/>
            <result column="sub_product_min_stock" property="minStock"/>
            <result column="sub_product_max_stock" property="maxStock"/>
            <result column="sub_product_purchase_price" property="purchasePrice"/>
            <result column="sub_product_purchase_tax_rate" property="purchaseTaxRate"/>
            <result column="sub_product_purchase_price_exclusive_tax" property="purchasePriceExclusiveTax"/>
            <result column="sub_product_standard_cost" property="standardCost"/>
            <result column="sub_product_sale_price" property="salePrice"/>
            <result column="sub_product_sale_tax_rate" property="saleTaxRate"/>
            <result column="sub_product_sale_price_exclusive_tax" property="salePriceExclusiveTax"/>
            <result column="sub_product_iqc_plan_id" property="iqcPlanId"/>
            <result column="sub_product_fqc_plan_id" property="fqcPlanId"/>
            <result column="sub_product_oqc_plan_id" property="oqcPlanId"/>
            <result column="sub_product_remark" property="remark"/>
            <result column="sub_product_status" property="status"/>
            <result column="sub_product_del_flag" property="delFlag"/>
            <result column="sub_product_create_by" property="createBy"/>
            <result column="sub_product_create_time" property="createTime"/>
            <result column="sub_product_update_by" property="updateBy"/>
            <result column="sub_product_update_time" property="updateTime"/>
            <result column="sub_product_create_dept" property="createDept"/>
            <result column="sub_product_tenant_id" property="tenantId"/>
        </association>
        <collection property="batches" ofType="com.iotlaser.spms.common.domain.SourceItemBatch">
            <id column="sub_batches_batch_id" property="batchId"/>
            <result column="sub_batches_item_id" property="itemId"/>
            <result column="sub_batches_main_id" property="mainId"/>
            <result column="sub_batches_inventory_batch_id" property="inventoryBatchId"/>
            <result column="sub_batches_internal_batch_number" property="internalBatchNumber"/>
            <result column="sub_batches_supplier_batch_number" property="supplierBatchNumber"/>
            <result column="sub_batches_serial_number" property="serialNumber"/>
            <result column="sub_batches_product_id" property="productId"/>
            <result column="sub_batches_product_code" property="productCode"/>
            <result column="sub_batches_product_name" property="productName"/>
            <result column="sub_batches_unit_id" property="unitId"/>
            <result column="sub_batches_unit_code" property="unitCode"/>
            <result column="sub_batches_unit_name" property="unitName"/>
            <result column="sub_batches_quantity" property="quantity"/>
            <result column="sub_batches_price" property="price"/>
            <result column="sub_batches_location_id" property="locationId"/>
            <result column="sub_batches_location_code" property="locationCode"/>
            <result column="sub_batches_location_name" property="locationName"/>
            <result column="sub_batches_remark" property="remark"/>
            <result column="sub_batches_status" property="status"/>
            <result column="sub_batches_del_flag" property="delFlag"/>
            <result column="sub_batches_create_by" property="createBy"/>
            <result column="sub_batches_create_time" property="createTime"/>
            <result column="sub_batches_update_by" property="updateBy"/>
            <result column="sub_batches_update_time" property="updateTime"/>
            <result column="sub_batches_create_dept" property="createDept"/>
            <result column="sub_batches_tenant_id" property="tenantId"/>
        </collection>
    </resultMap>


    <select id="queryItemByIdWith" resultMap="SourceItemResultMap">
        SELECT
        item.item_id
        ,item.${mainId} as main_id
        ,item.product_id
        ,item.product_code
        ,item.product_name
        ,item.unit_id
        ,item.unit_code
        ,item.unit_name
        ,item.location_id
        ,item.location_code
        ,item.location_name
        ,item.quantity
        ,item.finish_quantity
        ,item.price
        ,item.remark
        ,item.status
        ,item.del_flag
        ,item.create_by
        ,item.create_time
        ,item.update_by
        ,item.update_time
        ,item.create_dept
        ,item.tenant_id
        product.product_id AS sub_product_product_id,
        product.product_code AS sub_product_product_code,
        product.product_name AS sub_product_product_name,
        product.product_type AS sub_product_product_type,
        product.product_specs AS sub_product_product_specs,
        product.category_id AS sub_product_category_id,
        product.category_code AS sub_product_category_code,
        product.category_name AS sub_product_category_name,
        product.unit_id AS sub_product_unit_id,
        product.unit_code AS sub_product_unit_code,
        product.unit_name AS sub_product_unit_name,
        product.code_rule_id AS sub_product_code_rule_id,
        product.batch_flag AS sub_product_batch_flag,
        product.batch_policy AS sub_product_batch_policy,
        product.shelf_life_days AS sub_product_shelf_life_days,
        product.safe_stock_flag AS sub_product_safe_stock_flag,
        product.high_value_flag AS sub_product_high_value_flag,
        product.min_stock AS sub_product_min_stock,
        product.max_stock AS sub_product_max_stock,
        product.purchase_price AS sub_product_purchase_price,
        product.purchase_tax_rate AS sub_product_purchase_tax_rate,
        product.purchase_price_exclusive_tax AS sub_product_purchase_price_exclusive_tax,
        product.standard_cost AS sub_product_standard_cost,
        product.sale_price AS sub_product_sale_price,
        product.sale_tax_rate AS sub_product_sale_tax_rate,
        product.sale_price_exclusive_tax AS sub_product_sale_price_exclusive_tax,
        product.iqc_plan_id AS sub_product_iqc_plan_id,
        product.fqc_plan_id AS sub_product_fqc_plan_id,
        product.oqc_plan_id AS sub_product_oqc_plan_id,
        product.remark AS sub_product_remark,
        product.status AS sub_product_status,
        product.del_flag AS sub_product_del_flag,
        product.create_by AS sub_product_create_by,
        product.create_time AS sub_product_create_time,
        product.update_by AS sub_product_update_by,
        product.update_time AS sub_product_update_time,
        product.create_dept AS sub_product_create_dept,
        product.tenant_id AS sub_product_tenant_id
        <if test="isJoinBatch != null and isJoinBatch">
            ,
            batches.batch_id AS sub_batches_batch_id,
            batches.item_id AS sub_batches_item_id,
            batches.${mainId} AS sub_batches_main_id,
            batches.inventory_batch_id AS sub_batches_inventory_batch_id,
            batches.internal_batch_number AS sub_batches_internal_batch_number,
            batches.supplier_batch_number AS sub_batches_supplier_batch_number,
            batches.serial_number AS sub_batches_serial_number,
            batches.product_id AS sub_batches_product_id,
            batches.product_code AS sub_batches_product_code,
            batches.product_name AS sub_batches_product_name,
            batches.unit_id AS sub_batches_unit_id,
            batches.unit_code AS sub_batches_unit_code,
            batches.unit_name AS sub_batches_unit_name,
            batches.quantity AS sub_batches_quantity,
            batches.price AS sub_batches_price,
            batches.location_id AS sub_batches_location_id,
            batches.location_code AS sub_batches_location_code,
            batches.location_name AS sub_batches_location_name,
            batches.remark AS sub_batches_remark,
            batches.status AS sub_batches_status,
            batches.del_flag AS sub_batches_del_flag,
            batches.create_by AS sub_batches_create_by,
            batches.create_time AS sub_batches_create_time,
            batches.update_by AS sub_batches_update_by,
            batches.update_time AS sub_batches_update_time,
            batches.create_dept AS sub_batches_create_dept,
            batches.tenant_id AS sub_batches_tenant_id
        </if>
        FROM
        ${itemTable} item
        LEFT JOIN pro_product product ON item.product_id = product.product_id
        <if test="isJoinBatch != null and isJoinBatch">
            LEFT JOIN ${batchTable} batches ON item.item_id = batches.item_id
            and batches.del_flag = '0'
        </if>
        WHERE item.item_id = #{itemId}
        and item.del_flag = '0'
    </select>

    <select id="queryItemPageListWith" resultMap="SourceItemResultMap">
        SELECT
        item.item_id
        ,item.${mainId} as main_id
        ,item.product_id
        ,item.product_code
        ,item.product_name
        ,item.unit_id
        ,item.unit_code
        ,item.unit_name
        ,item.location_id
        ,item.location_code
        ,item.location_name
        ,item.quantity
        ,item.finish_quantity
        ,item.price
        ,item.remark
        ,item.status
        ,item.del_flag
        ,item.create_by
        ,item.create_time
        ,item.update_by
        ,item.update_time
        ,item.create_dept
        ,item.tenant_id
        product.product_id AS sub_product_product_id,
        product.product_code AS sub_product_product_code,
        product.product_name AS sub_product_product_name,
        product.product_type AS sub_product_product_type,
        product.product_specs AS sub_product_product_specs,
        product.category_id AS sub_product_category_id,
        product.category_code AS sub_product_category_code,
        product.category_name AS sub_product_category_name,
        product.unit_id AS sub_product_unit_id,
        product.unit_code AS sub_product_unit_code,
        product.unit_name AS sub_product_unit_name,
        product.code_rule_id AS sub_product_code_rule_id,
        product.batch_flag AS sub_product_batch_flag,
        product.batch_policy AS sub_product_batch_policy,
        product.shelf_life_days AS sub_product_shelf_life_days,
        product.safe_stock_flag AS sub_product_safe_stock_flag,
        product.high_value_flag AS sub_product_high_value_flag,
        product.min_stock AS sub_product_min_stock,
        product.max_stock AS sub_product_max_stock,
        product.purchase_price AS sub_product_purchase_price,
        product.purchase_tax_rate AS sub_product_purchase_tax_rate,
        product.purchase_price_exclusive_tax AS sub_product_purchase_price_exclusive_tax,
        product.standard_cost AS sub_product_standard_cost,
        product.sale_price AS sub_product_sale_price,
        product.sale_tax_rate AS sub_product_sale_tax_rate,
        product.sale_price_exclusive_tax AS sub_product_sale_price_exclusive_tax,
        product.iqc_plan_id AS sub_product_iqc_plan_id,
        product.fqc_plan_id AS sub_product_fqc_plan_id,
        product.oqc_plan_id AS sub_product_oqc_plan_id,
        product.remark AS sub_product_remark,
        product.status AS sub_product_status,
        product.del_flag AS sub_product_del_flag,
        product.create_by AS sub_product_create_by,
        product.create_time AS sub_product_create_time,
        product.update_by AS sub_product_update_by,
        product.update_time AS sub_product_update_time,
        product.create_dept AS sub_product_create_dept,
        product.tenant_id AS sub_product_tenant_id
        <if test="isJoinBatch != null and isJoinBatch">
            ,
            batches.batch_id AS sub_batches_batch_id,
            batches.item_id AS sub_batches_item_id,
            batches.${mainId} AS sub_batches_main_id,
            batches.inventory_batch_id AS sub_batches_inventory_batch_id,
            batches.internal_batch_number AS sub_batches_internal_batch_number,
            batches.supplier_batch_number AS sub_batches_supplier_batch_number,
            batches.serial_number AS sub_batches_serial_number,
            batches.product_id AS sub_batches_product_id,
            batches.product_code AS sub_batches_product_code,
            batches.product_name AS sub_batches_product_name,
            batches.unit_id AS sub_batches_unit_id,
            batches.unit_code AS sub_batches_unit_code,
            batches.unit_name AS sub_batches_unit_name,
            batches.quantity AS sub_batches_quantity,
            batches.price AS sub_batches_price,
            batches.location_id AS sub_batches_location_id,
            batches.location_code AS sub_batches_location_code,
            batches.location_name AS sub_batches_location_name,
            batches.remark AS sub_batches_remark,
            batches.status AS sub_batches_status,
            batches.del_flag AS sub_batches_del_flag,
            batches.create_by AS sub_batches_create_by,
            batches.create_time AS sub_batches_create_time,
            batches.update_by AS sub_batches_update_by,
            batches.update_time AS sub_batches_update_time,
            batches.create_dept AS sub_batches_create_dept,
            batches.tenant_id AS sub_batches_tenant_id
        </if>
        FROM
        ${itemTable} item
        LEFT JOIN pro_product product ON item.product_id = product.product_id
        <if test="isJoinBatch != null and isJoinBatch">
            LEFT JOIN ${batchTable} batches ON item.item_id = batches.item_id
            AND batches.del_flag = '0'
        </if>
        <if test="ew != null and ew.customSqlSegment != null and ew.customSqlSegment != ''">
            ${ew.customSqlSegment}
        </if>
    </select>

</mapper>
