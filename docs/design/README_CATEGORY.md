
---

### **系统核心分类字段枚举值全集 (最终修订版)**

#### **一、 基础模块 (`base_`)**

*   **`base_company` (公司信息表)**
    *   `company_type`: 公司类型
        *   `INTERNAL`: 内部组织
        *   `SUPPLIER`: 供应商
        *   `CUSTOMER`: 客户
        *   `PARTNER`: 合作伙伴

*   **`base_location` (位置库位表)**
    *   `location_type`: 库位类型
        *   `WAREHOUSE`: 仓库
        *   `ZONE`: 库区 (如：收货区、发货区、存储区、待检区)
        *   `AISLE`: 巷道
        *   `RACK`: 货架
        *   `BIN`: 货位/料箱
        *   `STAGING_AREA`: 生产线边仓/暂存区
        *   `DOCK`: 装卸月台

*   **`base_auto_code_rule` (编码生成规则表)**
    *   `padded_method`: 补齐方式
        *   `L`: 左补齐 (Left Padding)
        *   `R`: 右补齐 (Right Padding)

*   **`base_auto_code_part` (编码生成规则组成表)**
    *   `part_type`: 分段类型
        *   `FIX_CHAR`: 固定字符
        *   `INPUT_CHAR`: 输入字符
        *   `DATE`: 日期
        *   `SERIAL_NO`: 流水号
    *   `cycle_method`: 流水号循环方式
        *   `YEAR`: 按年
        *   `MONTH`: 按月
        *   `DAY`: 按日
        *   `PERMANENT`: 永久 (不循环)

---
#### **二、 产品与工艺模块 (`pro_`)**

*   **`pro_product` (产品信息表)**
    *   `product_type`: 产品类型
        *   `FINISHED_GOOD`: 产成品
        *   `SEMI_FINISHED_GOOD`: 半成品
        *   `RAW_MATERIAL`: 原材料
        *   `ASSET`: 资产/工装
        *   `SERVICE`: 服务
    *   `batch_policy`: 批次管理策略
        *   `MANUAL`: 手工创建批次
        *   `AUTO_DATE`: 按日期生成 (如 YYYYMMDD)
        *   `AUTO_SHIFT`: 按班次生成 (如 YYYYMMDD-A)

*   **`pro_process` (工序表)**
    *   `process_category`: 工序类别
        *   `MACHINING`: 机加
        *   `WELDING`: 焊接
        *   `ASSEMBLY`: 装配
        *   `TESTING`: 测试
        *   `PAINTING`: 喷涂
        *   `PACKING`: 包装
    *   `report_type`: 默认报工类型
        *   `BY_QUANTITY`: 按数量报工
        *   `BY_HOURS`: 按工时报工
        *   `BY_PIECE`: 按计件报工

*   **`pro_routing` (工艺路线表)**
    *   `routing_status`: 路线状态
        *   `DRAFT`: 草稿
        *   `ACTIVE`: 激活
        *   `ARCHIVED`: 归档

---
#### **三、 生产模块 (`mes_`)**

*   **`mes_production_order` (生产订单)**
    *   `order_type`: 生产订单类型
        *   `STANDARD`: 标准生产订单
        *   `REWORK`: 返工订单
        *   `PROTOTYPE`: 试制/样品订单
        *   `DISASSEMBLY`: 拆解订单

*   **`mes_production_report` (生产报工记录)**
    *   `report_type`: 报工事件类型
        *   `START`: 开工
        *   `COMPLETE`: 完工 (含产出数量)
        *   `PAUSE`: 暂停
        *   `RESUME`: 恢复
        *   `CONSUME`: 物料消耗
        *   `SCRAP`: 报废

---
#### **四、 仓库管理模块 (`wms_`)**

*   **`wms_inventory_log` (库存日志)**
    *   `source_type`: 来源单据类型 (业务操作类型)
        *   `PURCHASE_INBOUND`: 采购入库
        *   `PRODUCTION_INBOUND`: 生产入库
        *   `SALE_RETURN_INBOUND`: 销售退货入库
        *   `PROD_RETURN_INBOUND`: 生产退料入库
        *   `TRANSFER_INBOUND`: 移库入库
        *   `CHECK_PROFIT_INBOUND`: 盘点盘盈入库
        *   `SALE_OUTBOUND`: 销售出库
        *   `PURCHASE_RETURN_OUTBOUND`: 采购退货出库
        *   `PROD_ISSUE_OUTBOUND`: 生产领料出库
        *   `TRANSFER_OUTBOUND`: 移库出库
        *   `CHECK_LOSS_OUTBOUND`: 盘点盘亏出库
    *   `direction`: 库存方向
        *   `1`: 入库 (增加)
        *   `-1`: 出库 (减少)

*   **`wms_transfer` (移库单)**
    *   `transfer_type`: 移库单类型
        *   `STANDARD`: 标准移库
        *   `REPLENISHMENT`: 补货移库
        *   `CONSOLIDATION`: 库存整理移库
        *   `STAGING`: 向线边仓备料

*   **`wms_inventory_batch` (库存批次)**
    *   `management_type`: 管理方式
        *   `BATCH`: 批次管理
        *   `SERIAL`: 序列号管理
    *   `inventory_status`: 库存状态
        *   `AVAILABLE`: 可用
        *   `ON_HOLD`: 冻结
        *   `IN_TRANSIT`: 在途/移动中
        *   `EXPIRED`: 已过期

*   **`wms_inventory_check` (盘点任务)**
    *   `check_type`: 盘点类型
        *   `CYCLE_COUNT`: 循环盘点
        *   `FULL_COUNT`: 全面盘点
        *   `DYNAMIC_COUNT`: 动态盘点 (对频繁出入库的库位进行)

---
#### **五、 财务模块 (`erp_fin_`)**

*   **`erp_fin_account` (账户表)**
    *   `account_type`: 账户类型
        *   `BANK_CASH`: 银行存款
        *   `CASH`: 库存现金
        *   `THIRD_PARTY`: 第三方支付 (如支付宝)
        *   `VIRTUAL`: 虚拟账户

*   **`erp_fin_account_ledger` (账户流水)**
    *   `direction`: 资金方向
        *   `INCOME`: 收入
        *   `EXPENSE`: 支出
    *   `transaction_type`: 交易类型
        *   `SALES_RECEIPT`: 销售收款
        *   `PURCHASE_PAYMENT`: 采购付款
        *   `EXPENSE_REIMBURSEMENT`: 费用报销
        *   `OTHER`: 其他

*   **通用收付款类型 (用于 `erp_fin_ap_payment_order` 和 `erp_fin_ar_receipt_order`)**
    *   `payment_method`: 付款/收款方式
        *   `BANK_TRANSFER`: 银行转账
        *   `ALIPAY`: 支付宝
        *   `WECHAT_PAY`: 微信支付
        *   `CASH`: 现金
        *   `CHECK`: 支票
        *   `ACCEPTANCE_BILL`: 承兑汇票
