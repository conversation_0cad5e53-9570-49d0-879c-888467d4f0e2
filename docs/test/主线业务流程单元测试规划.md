# 主线业务流程单元测试规划

**日期**: 2025-06-24  
**规划人员**: Augment Agent  
**目标**: 通过单元测试确保核心主线业务流程的功能完整性和独立性

## 🎯 测试目标

### 核心原则
1. **功能完整性**: 确保每个主线流程的关键节点都能正常工作
2. **独立性保证**: 通过Mock隔离外部依赖，避免其他模块错误影响
3. **业务逻辑验证**: 重点测试业务规则、数据计算、状态流转
4. **异常处理**: 验证各种异常情况下的处理逻辑

### 主线业务流程识别
基于代码分析，确定以下4条核心主线业务流程：

1. **销售主线**: 销售订单 → 出库 → 应收 → 收款
2. **采购主线**: 采购订单 → 入库 → 应付 → 付款  
3. **生产主线**: 生产订单 → 领料 → 报工 → 完工入库
4. **库存主线**: 入库 → 批次管理 → 库存统计 → 出库

## 📋 测试策略

### 1. 测试层次设计
```
单元测试层次：
├── Service层核心方法测试 (重点)
├── 业务流程集成测试 (关键)
├── 数据计算逻辑测试 (重要)
└── 异常处理测试 (必要)
```

### 2. Mock策略
- **外部依赖全部Mock**: Mapper、其他Service、工具类
- **业务逻辑真实执行**: 核心计算、状态流转、数据校验
- **数据隔离**: 使用测试数据，不依赖真实数据库

### 3. 测试覆盖范围
- **正常流程**: 标准业务流程的完整执行
- **边界条件**: 数量为0、金额为0、状态边界等
- **异常情况**: 数据不存在、状态不匹配、权限不足等
- **业务规则**: 价税分离、库存充足性、状态流转合法性

## 🔍 详细测试计划

### 销售主线流程测试 (SaleOrderServiceImpl)

#### 核心测试方法
1. **insertByBo()** - 销售订单创建
   - 正常创建：编码生成、状态设置、冗余字段填充
   - 明细处理：价税分离计算、金额汇总
   - 异常处理：必填字段校验、重复编码检查

2. **confirmOrder()** - 订单确认
   - 状态流转：DRAFT → CONFIRMED
   - 库存检查：库存充足性验证
   - 后续流程：自动创建出库单

3. **autoCreateOutbound()** - 自动创建出库单
   - 前置条件：订单状态、库存充足性
   - 流程调用：调用SaleOutboundService
   - 数据传递：订单明细到出库明细

#### Mock依赖
```java
@Mock private SaleOrderMapper baseMapper;
@Mock private ISaleOrderItemService itemService;
@Mock private ISaleOutboundService saleOutboundService;
@Mock private IInventoryService inventoryService;
@Mock private AutoCodeGenerator gen;
```

### 采购主线流程测试 (PurchaseOrderServiceImpl)

#### 核心测试方法
1. **insertByBo()** - 采购订单创建
2. **confirmOrder()** - 订单确认  
3. **autoCreateInbound()** - 自动创建入库单

#### 关键业务逻辑
- 供应商信息填充
- 价税分离计算
- 入库单自动生成

### 生产主线流程测试 (ProductionOrderServiceImpl)

#### 核心测试方法
1. **insertByBo()** - 生产订单创建
2. **releaseOrder()** - 订单下达
3. **startProduction()** - 开始生产
4. **completeProduction()** - 完成生产

#### 关键业务逻辑
- BOM信息填充
- 生产状态流转
- 物料需求检查

### 库存主线流程测试 (InventoryServiceImpl + InventoryBatchServiceImpl)

#### 核心测试方法
1. **adjustInventory()** - 库存调整
2. **freezeInventory()** - 库存冻结
3. **deductInventoryWithFIFO()** - FIFO库存扣减
4. **updateInventorySummary()** - 库存汇总更新

#### 关键业务逻辑
- FIFO算法实现
- 批次状态管理
- 库存汇总计算

## 🛠️ 测试实现框架

### 基础测试类模板
```java
@ExtendWith(MockitoExtension.class)
class SaleOrderServiceImplTest {
    
    @Mock private SaleOrderMapper baseMapper;
    @Mock private ISaleOrderItemService itemService;
    @Mock private ISaleOutboundService saleOutboundService;
    @Mock private AutoCodeGenerator gen;
    
    @InjectMocks
    private SaleOrderServiceImpl saleOrderService;
    
    @Test
    void should_CreateOrderSuccessfully_when_ValidDataProvided() {
        // Given: 准备测试数据
        SaleOrderBo orderBo = createValidSaleOrderBo();
        when(gen.code(any())).thenReturn("SO202506240001");
        when(baseMapper.insert(any())).thenReturn(1);
        
        // When: 执行测试方法
        Boolean result = saleOrderService.insertByBo(orderBo);
        
        // Then: 验证结果
        assertTrue(result);
        verify(baseMapper).insert(any(SaleOrder.class));
        verify(itemService).insertOrUpdateBatch(any());
    }
}
```

### 测试数据构建工具
```java
public class TestDataBuilder {
    
    public static SaleOrderBo createValidSaleOrderBo() {
        SaleOrderBo bo = new SaleOrderBo();
        bo.setOrderName("测试销售订单");
        bo.setCustomerId(1L);
        bo.setCustomerName("测试客户");
        bo.setOrderDate(LocalDate.now());
        bo.setItems(createSaleOrderItems());
        return bo;
    }
    
    public static List<SaleOrderItemBo> createSaleOrderItems() {
        SaleOrderItemBo item = new SaleOrderItemBo();
        item.setProductId(1L);
        item.setProductName("测试产品");
        item.setQuantity(new BigDecimal("10"));
        item.setPrice(new BigDecimal("100.00"));
        item.setTaxRate(new BigDecimal("13"));
        return List.of(item);
    }
}
```

## 📊 测试执行计划

### 第一阶段：核心Service单元测试 (3-4天)
- **销售主线**: SaleOrderServiceImpl, SaleOutboundServiceImpl
- **采购主线**: PurchaseOrderServiceImpl, PurchaseInboundServiceImpl  
- **重点**: 基础CRUD、状态流转、数据计算

### 第二阶段：业务流程集成测试 (2-3天)
- **端到端流程**: 订单创建到完成的完整流程
- **跨Service调用**: Service间的协作和数据传递
- **重点**: 业务流程完整性、数据一致性

### 第三阶段：库存和生产测试 (2-3天)
- **库存管理**: InventoryServiceImpl, InventoryBatchServiceImpl
- **生产管理**: ProductionOrderServiceImpl, ProductionIssueServiceImpl
- **重点**: 复杂算法、批次管理、状态机

### 第四阶段：异常和边界测试 (1-2天)
- **异常处理**: 各种业务异常的处理逻辑
- **边界条件**: 极值、空值、状态边界
- **重点**: 系统健壮性、错误处理

## 🎯 成功标准

### 测试覆盖率目标
- **方法覆盖率**: ≥ 90%
- **分支覆盖率**: ≥ 80%
- **核心业务逻辑**: 100%

### 质量标准
- **所有测试通过**: 无失败用例
- **Mock验证**: 所有依赖调用正确
- **业务逻辑正确**: 计算结果准确
- **异常处理完善**: 异常情况处理得当

### 文档输出
- **测试报告**: 详细的测试执行结果
- **覆盖率报告**: 代码覆盖情况分析
- **问题清单**: 发现的问题和修复建议

---

**通过这套完整的单元测试体系，确保核心主线业务流程的功能完整性和独立性，为系统稳定运行提供坚实保障。**
