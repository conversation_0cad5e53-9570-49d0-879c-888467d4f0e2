# iotlaser-spms项目单元测试总体计划

## 📋 **测试概述**

**项目名称**: iotlaser-spms 企业级ERP+MES+WMS+QMS+APS+PRO集成系统
**测试目标**: 建立完整的单元测试体系，确保代码质量和业务逻辑正确性
**测试框架**: JUnit 5.9.3 + Mockito + Spring Boot Test + Maven Surefire 3.0.0-M9
**覆盖率目标**: 行覆盖率≥80%，分支覆盖率≥70%，方法覆盖率≥90%
**实施周期**: 预计2-3周完成（调整后6阶段计划）
**当前状态**: ✅ 测试基础设施已完成，核心逻辑验证100%通过

## 🎯 **测试范围和目标**

### **测试范围统计（调整后6阶段计划）**
| 模块 | Service数量 | 核心方法数 | 枚举数量 | 测试优先级 | 实施阶段 |
|------|-------------|------------|----------|------------|----------|
| **BASE** | 6个 | 25个 | 5个 | ⭐⭐⭐⭐⭐ | 第一阶段 |
| **PRO** | 3个 | 12个 | 1个 | ⭐⭐⭐⭐ | 第二阶段 |
| **ERP** | 30个 | 120个 | 12个 | ⭐⭐⭐⭐⭐ | 第三阶段 |
| **WMS** | 13个 | 52个 | 6个 | ⭐⭐⭐⭐ | 第四阶段 |
| **MES** | 12个 | 48个 | 4个 | ⭐⭐⭐ | 第五阶段 |
| **FIN** | 15个 | 60个 | 8个 | ⭐⭐⭐⭐ | 第六阶段 |
| **暂缓** | QMS、APS | - | - | - | 暂不实施 |
| **总计** | **79个** | **317个** | **36个** | - | 6个阶段 |

### **重点测试功能**
1. **已修复的284个业务方法** - 验证修复后的功能正确性
2. **37个枚举类的类型安全性** - 验证枚举标准化效果
3. **15个跨模块集成点** - 验证模块间协作
4. **FIFO批次管理算法** - 验证库存管理核心逻辑
5. **业务闭环流程** - 验证端到端业务流程

## 🗓️ **分阶段实施计划（调整后6阶段）**

### **第一阶段：BASE模块深度测试实施 (2-3天)**
**目标**: 基于已完成的测试基础设施，深度验证基础数据管理功能
**当前状态**: ✅ 测试框架已完成，CompanyServiceImpl已验证

#### **测试内容**
- ✅ **CompanyServiceImpl**: 公司信息管理（已完成Mock单元测试）
- 🔄 **LocationServiceImpl**: 库位管理（当前任务）
- **MeasureUnitServiceImpl**: 计量单位管理
- **AutoCodePartServiceImpl**: 编码规则管理
- **ProductCategoryServiceImpl**: 产品分类管理
- **ProductServiceImpl**: 产品信息管理

#### **关键测试点**
- 标准CRUD方法：insertByBo, updateByBo, queryById, queryPageList, deleteByIds
- 业务特定方法：根据各Service的实际接口方法
- 数据校验逻辑和异常处理
- 编码规则生成和唯一性验证
- 枚举类型安全性和转换
- Mock依赖注入和业务逻辑隔离

#### **技术规范**
- 使用@ExtendWith(MockitoExtension.class)和@Mock/@InjectMocks注解
- 遵循命名规范：should{ExpectedBehavior}_when{StateUnderTest}
- 每个核心方法至少3个测试用例：正常流程、异常处理、边界条件
- 使用实际的BO/VO/Entity类，确保字段匹配

#### **交付物**
- ✅ 测试基础框架已搭建（JUnit 5.9.3 + Surefire 3.0.0-M9 + JaCoCo）
- ✅ CompanyServiceImplUnitTest已完成
- 🔄 LocationServiceImplTest（当前开发中）
- 5个Service测试类（待完成）
- 第一阶段测试报告

### **第二阶段：PRO模块产品管理测试 (2-3天)**
**目标**: 验证产品信息管理、BOM结构、产品分类体系

#### **测试内容**
- **ProductServiceImpl**: 产品信息管理核心功能
- **产品分类体系**: 产品分类层级管理
- **BOM结构管理**: 产品物料清单
- **产品工艺管理**: 工艺路线和工序

#### **关键测试点**
- 产品信息CRUD操作
- 产品分类层级管理
- BOM展开和成本计算
- 产品工艺路线验证
- 产品规格和属性管理
- 产品状态管理

#### **交付物**
- 3个PRO Service测试类
- 产品管理功能验证测试
- BOM结构验证测试
- 第二阶段测试报告

### **第三阶段：ERP模块业务流程测试 (5-7天)**
**目标**: 验证销售、采购、财务核心业务流程

#### **测试内容**
- **销售流程**: SaleOrder → SaleOutbound → FinArReceivable → FinArReceiptOrder
- **采购流程**: PurchaseOrder → PurchaseInbound → FinApInvoice → FinApPaymentOrder
- **财务核销**: 收付款核销、三单匹配、对账处理
- **退货流程**: SaleReturn、PurchaseReturn
- **库存影响**: 订单对库存的影响验证

#### **关键测试点**
- 订单状态流转验证
- 库存影响计算
- 财务凭证生成
- 核销关系建立
- 业务规则校验
- 跨模块数据传递
- 价税分离计算

#### **交付物**
- 30个ERP Service测试类
- 业务流程集成测试
- 财务核算验证测试
- 第三阶段测试报告

### **第四阶段：WMS模块库存管理测试 (3-4天)**
**目标**: 验证库存管理和批次管理核心功能

#### **测试内容**
- **库存三层架构**: Inventory + InventoryBatch + InventoryLog
- **FIFO批次管理**: 先进先出算法验证
- **入库流程**: Inbound → InventoryBatch生成
- **出库流程**: Outbound → FIFO批次扣减
- **移库流程**: Transfer → 库位间转移
- **盘点流程**: Count → 库存调整

#### **关键测试点**
- FIFO算法正确性
- 批次状态管理
- 库存数量一致性
- 日志记录完整性
- 异常情况处理
- 并发操作安全性

#### **交付物**
- 13个WMS Service测试类
- FIFO算法专项测试
- 库存一致性验证测试
- 第四阶段测试报告

### **第五阶段：MES模块生产执行测试 (3-4天)**
**目标**: 验证生产执行和物料管理功能

#### **测试内容**
- **生产订单**: ProductionOrder状态流转
- **生产领料**: ProductionIssue → 库存扣减
- **生产退料**: ProductionReturn → 库存增加
- **生产入库**: ProductionInbound → 成品入库
- **工序报工**: 生产进度跟踪

#### **关键测试点**
- 生产状态管理
- 物料需求计算
- 库存扣减逻辑
- 成本核算
- 生产效率统计

#### **交付物**
- 12个MES Service测试类
- 生产流程集成测试
- 物料管理验证测试
- 第五阶段测试报告

### **第六阶段：FIN模块财务管理测试 (4-5天)**
**目标**: 验证财务管理、应收应付、成本核算功能

#### **测试内容**
- **应收管理**: FinArReceivable、FinArReceiptOrder、核销流程
- **应付管理**: FinApInvoice、FinApPaymentOrder、核销流程
- **财务核算**: 价税分离计算、对账处理
- **成本管理**: 成本核算、成本分摊
- **财务报表**: 账龄分析、资金流水

#### **关键测试点**
- 价税分离计算逻辑
- 财务核销流程
- 对账处理机制
- 成本核算算法
- 财务报表生成
- 异常处理和回滚

#### **交付物**
- 15个FIN Service测试类
- 财务核算验证测试
- 价税分离专项测试
- 第六阶段测试报告

### **暂缓实施模块**
- **QMS模块**: 质量管理系统（暂不实施）
- **APS模块**: 高级计划排程系统（暂不实施）

## 🔧 **测试技术规范**

### **测试框架配置（已完成）**
```xml
<!-- JUnit 5.9.3 -->
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <version>5.9.3</version>
    <scope>test</scope>
</dependency>

<!-- JUnit Platform Launcher -->
<dependency>
    <groupId>org.junit.platform</groupId>
    <artifactId>junit-platform-launcher</artifactId>
    <version>1.9.3</version>
    <scope>test</scope>
</dependency>

<!-- Maven Surefire Plugin -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <version>3.0.0-M9</version>
</plugin>

<!-- JaCoCo Plugin -->
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.8</version>
</plugin>
```

### **测试类命名规范**
- Service测试类: `{ServiceName}Test`
- 集成测试类: `{ModuleName}IntegrationTest`
- 工具类测试: `{UtilityName}Test`

### **测试方法命名规范**
- 格式: `should{ExpectedBehavior}_when{StateUnderTest}`
- 示例: `shouldReturnSuccess_whenValidDataProvided`

### **测试数据管理**
- 使用`@Sql`注解加载测试数据
- 每个测试方法独立的数据准备
- 测试后自动清理数据

## 📊 **质量保证标准**

### **覆盖率要求**
- **行覆盖率**: ≥ 80%
- **分支覆盖率**: ≥ 70%
- **方法覆盖率**: ≥ 90%

### **测试质量标准**
- 每个核心业务方法至少3个测试用例（正常、异常、边界）
- 所有枚举类型转换测试
- 所有异常处理路径测试
- 所有事务边界测试

### **性能基准**
- 单个Service方法响应时间 < 100ms
- 批量操作响应时间 < 1s
- 集成测试端到端响应时间 < 5s

## 🚀 **持续集成配置**

### **Maven配置**
```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

### **CI/CD集成**
- 每次提交自动运行单元测试
- 覆盖率报告自动生成
- 测试失败阻止部署
- 性能回归检测

## 📋 **风险控制**

### **测试风险识别**
1. **数据依赖风险**: 测试数据准备复杂
2. **环境依赖风险**: 数据库、缓存等外部依赖
3. **并发测试风险**: 多线程场景测试复杂
4. **集成测试风险**: 跨模块依赖复杂

### **风险缓解措施**
1. 使用TestContainers隔离环境
2. Mock外部依赖减少耦合
3. 并发测试使用专门的测试工具
4. 分层测试策略降低复杂度

## 📈 **成功标准**

### **阶段性目标（调整后6阶段）**
- ✅ **测试基础设施**: 已完成（JUnit 5.9.3 + Surefire + JaCoCo）
- 🔄 **第一阶段**: BASE模块深度测试覆盖率 > 90%
- **第二阶段**: PRO模块产品管理测试完成
- **第三阶段**: ERP模块核心流程测试完成
- **第四阶段**: WMS模块FIFO算法验证通过
- **第五阶段**: MES模块生产流程验证通过
- **第六阶段**: FIN模块财务核算验证通过

### **最终目标**
- 整体测试覆盖率 > 80%
- 所有核心业务方法测试通过
- 所有集成点验证通过
- 性能基准测试通过
- 零关键缺陷

---

**计划制定时间**: 2025-06-22
**计划更新时间**: 2025-06-22（调整为6阶段计划）
**预计完成时间**: 15-18个工作日（调整后）
**负责团队**: 开发团队 + QA团队
**审核标准**: RuoYi-Vue-Plus 5.4.0框架规范
**当前进度**: ✅ 测试基础设施完成，🔄 第一阶段BASE模块进行中
