# 出入库批次管理改进计划

## 📋 项目信息

**项目名称**: iotlaser-admin模块出入库批次管理优化  
**计划制定时间**: 2025-06-24  
**预计总工期**: 10-12天  
**技术框架**: RuoYi-Vue-Plus 5.4.0 + Spring Boot 3.x

## 🎯 改进目标

### 核心目标
1. **数据准确性**: 确保所有数量和金额计算的准确性和一致性
2. **系统稳定性**: 完善并发控制和事务处理机制
3. **可维护性**: 统一代码规范和数据处理模式
4. **性能优化**: 提升查询和计算性能

### 成功标准
- [ ] 所有主表汇总字段正确计算和存储
- [ ] 库存扣减操作无并发安全问题
- [ ] 数据一致性检查机制完善
- [ ] 单元测试覆盖率达到80%以上

## 📅 详细执行计划

### 第一阶段：数据结构完善 (2-3天)

#### 🔧 任务1.1：主表汇总字段添加 (1天)
**负责人**: 后端开发工程师  
**优先级**: P0 - 关键

**具体任务**:
```sql
-- 1. 销售订单表
ALTER TABLE erp_sale_order ADD COLUMN total_quantity DECIMAL(15,4) DEFAULT 0 COMMENT '总数量';
ALTER TABLE erp_sale_order ADD COLUMN total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总金额(含税)';
ALTER TABLE erp_sale_order ADD COLUMN total_amount_exclusive_tax DECIMAL(15,2) DEFAULT 0 COMMENT '总金额(不含税)';
ALTER TABLE erp_sale_order ADD COLUMN total_tax_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总税额';

-- 2. 采购订单表
ALTER TABLE erp_purchase_order ADD COLUMN total_quantity DECIMAL(15,4) DEFAULT 0 COMMENT '总数量';
ALTER TABLE erp_purchase_order ADD COLUMN total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总金额(含税)';
ALTER TABLE erp_purchase_order ADD COLUMN total_amount_exclusive_tax DECIMAL(15,2) DEFAULT 0 COMMENT '总金额(不含税)';
ALTER TABLE erp_purchase_order ADD COLUMN total_tax_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总税额';

-- 3. 仓库入库表
ALTER TABLE wms_inbound ADD COLUMN total_quantity DECIMAL(15,4) DEFAULT 0 COMMENT '总数量';
ALTER TABLE wms_inbound ADD COLUMN total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总金额';

-- 4. 仓库出库表
ALTER TABLE wms_outbound ADD COLUMN total_quantity DECIMAL(15,4) DEFAULT 0 COMMENT '总数量';
ALTER TABLE wms_outbound ADD COLUMN total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总金额';

-- 5. 采购入库表
ALTER TABLE erp_purchase_inbound ADD COLUMN total_quantity DECIMAL(15,4) DEFAULT 0 COMMENT '总数量';
ALTER TABLE erp_purchase_inbound ADD COLUMN total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总金额';

-- 6. 销售出库表
ALTER TABLE erp_sale_outbound ADD COLUMN total_quantity DECIMAL(15,4) DEFAULT 0 COMMENT '总数量';
ALTER TABLE erp_sale_outbound ADD COLUMN total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总金额';
```

**验收标准**:
- [ ] 所有主表成功添加汇总字段
- [ ] 字段类型和精度符合规范
- [ ] 默认值设置正确

#### 🔧 任务1.2：实体类更新 (0.5天)
**负责人**: 后端开发工程师  
**优先级**: P0 - 关键

**具体任务**:
```java
// 1. 更新SaleOrder实体类
@TableField("total_quantity")
private BigDecimal totalQuantity;

@TableField("total_amount")
private BigDecimal totalAmount;

@TableField("total_amount_exclusive_tax")
private BigDecimal totalAmountExclusiveTax;

@TableField("total_tax_amount")
private BigDecimal totalTaxAmount;

// 2. 更新对应的BO和VO类
// 3. 更新其他主表实体类
```

**验收标准**:
- [ ] 所有实体类字段映射正确
- [ ] BO和VO类同步更新
- [ ] 编译无错误

#### 🔧 任务1.3：数量字段类型统一 (0.5天)
**负责人**: 后端开发工程师  
**优先级**: P1 - 重要

**具体任务**:
```sql
-- 检查并修改数量字段类型
ALTER TABLE mes_production_inbound_item MODIFY COLUMN finish_quantity DECIMAL(15,4);
-- 其他需要修改的表...
```

**验收标准**:
- [ ] 所有数量字段类型统一为DECIMAL(15,4)
- [ ] 对应实体类字段类型为BigDecimal
- [ ] 数据迁移无丢失

### 第二阶段：业务逻辑完善 (3-4天)

#### 🔧 任务2.1：汇总计算逻辑实现 (1.5天)
**负责人**: 后端开发工程师  
**优先级**: P0 - 关键

**具体任务**:
```java
// 1. 实现SaleOrderServiceImpl中的汇总计算
private void updateTotalAmounts(Long orderId) {
    List<SaleOrderItemVo> items = saleOrderItemService.queryListByOrderId(orderId);
    
    BigDecimal totalQuantity = items.stream()
        .map(item -> item.getQuantity() != null ? item.getQuantity() : BigDecimal.ZERO)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    
    BigDecimal totalAmount = items.stream()
        .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    
    BigDecimal totalAmountExclusiveTax = items.stream()
        .map(item -> item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    
    BigDecimal totalTaxAmount = totalAmount.subtract(totalAmountExclusiveTax);
    
    // 更新主表
    SaleOrder update = new SaleOrder();
    update.setOrderId(orderId);
    update.setTotalQuantity(totalQuantity);
    update.setTotalAmount(totalAmount);
    update.setTotalAmountExclusiveTax(totalAmountExclusiveTax);
    update.setTotalTaxAmount(totalTaxAmount);
    
    baseMapper.updateById(update);
}

// 2. 实现其他Service类的汇总计算逻辑
```

**验收标准**:
- [ ] 所有主表Service类实现汇总计算
- [ ] 计算逻辑准确无误
- [ ] 精度处理符合规范

#### 🔧 任务2.2：库存汇总逻辑实现 (1天)
**负责人**: 后端开发工程师  
**优先级**: P0 - 关键

**具体任务**:
```java
// 1. 实现InventoryServiceImpl中的真实汇总逻辑
public Boolean updateInventorySummary(Long productId) {
    // 从批次汇总到产品级别
    BigDecimal totalQuantity = inventoryBatchService.sumQuantityByProductId(productId);
    BigDecimal availableQuantity = inventoryBatchService.sumAvailableQuantityByProductId(productId);
    BigDecimal lockedQuantity = inventoryBatchService.sumLockedQuantityByProductId(productId);
    
    // 更新汇总表
    Inventory inventory = new Inventory();
    inventory.setProductId(productId);
    inventory.setQuantity(totalQuantity);
    inventory.setAvailableQuantity(availableQuantity);
    inventory.setLockedQuantity(lockedQuantity);
    
    return baseMapper.updateById(inventory) > 0;
}

// 2. 实现InventoryBatchServiceImpl中的汇总方法
public BigDecimal sumQuantityByProductId(Long productId) {
    LambdaQueryWrapper<InventoryBatch> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(InventoryBatch::getProductId, productId);
    wrapper.in(InventoryBatch::getBatchStatus, 
               InventoryBatchStatus.AVAILABLE, 
               InventoryBatchStatus.LOCKED);
    
    List<InventoryBatch> batches = baseMapper.selectList(wrapper);
    return batches.stream()
        .map(InventoryBatch::getQuantity)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
}
```

**验收标准**:
- [ ] 库存汇总逻辑正确实现
- [ ] 批次状态正确处理
- [ ] 性能满足要求

#### 🔧 任务2.3：并发控制机制 (1.5天)
**负责人**: 后端开发工程师  
**优先级**: P1 - 重要

**具体任务**:
```java
// 1. 实现库存扣减的并发控制
@Transactional(rollbackFor = Exception.class)
public Boolean deductInventoryWithLock(Long productId, Long locationId, BigDecimal quantity) {
    // 使用SELECT FOR UPDATE锁定批次
    List<InventoryBatch> batches = inventoryBatchMapper.selectAvailableBatchesForUpdate(productId, locationId);
    
    BigDecimal remainingQty = quantity;
    for (InventoryBatch batch : batches) {
        if (remainingQty.compareTo(BigDecimal.ZERO) <= 0) break;
        
        BigDecimal batchQty = batch.getQuantity();
        if (batchQty.compareTo(BigDecimal.ZERO) <= 0) continue;
        
        BigDecimal deductFromBatch = remainingQty.min(batchQty);
        batch.setQuantity(batchQty.subtract(deductFromBatch));
        
        if (batch.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
            batch.setBatchStatus(InventoryBatchStatus.EXHAUSTED);
        }
        
        baseMapper.updateById(batch);
        remainingQty = remainingQty.subtract(deductFromBatch);
    }
    
    if (remainingQty.compareTo(BigDecimal.ZERO) > 0) {
        throw new ServiceException("库存不足，缺少数量：" + remainingQty);
    }
    
    return true;
}

// 2. 实现Mapper中的SELECT FOR UPDATE方法
@Select("SELECT * FROM wms_inventory_batch WHERE product_id = #{productId} " +
        "AND location_id = #{locationId} AND batch_status = 'AVAILABLE' " +
        "AND quantity > 0 ORDER BY create_time ASC FOR UPDATE")
List<InventoryBatch> selectAvailableBatchesForUpdate(@Param("productId") Long productId, 
                                                     @Param("locationId") Long locationId);
```

**验收标准**:
- [ ] 库存扣减操作线程安全
- [ ] 并发测试通过
- [ ] 性能影响可接受

### 第三阶段：功能增强 (2-3天)

#### 🔧 任务3.1：数据一致性检查机制 (1天)
**负责人**: 后端开发工程师
**优先级**: P1 - 重要

**具体任务**:
```java
// 1. 实现数据一致性检查服务
@Service
public class DataConsistencyService {

    public Boolean validateOrderConsistency(Long orderId, String orderType) {
        switch (orderType) {
            case "SALE_ORDER":
                return validateSaleOrderConsistency(orderId);
            case "PURCHASE_ORDER":
                return validatePurchaseOrderConsistency(orderId);
            default:
                return false;
        }
    }

    private Boolean validateSaleOrderConsistency(Long orderId) {
        // 1. 计算明细汇总
        BigDecimal detailTotalQuantity = saleOrderItemService.sumQuantityByOrderId(orderId);
        BigDecimal detailTotalAmount = saleOrderItemService.sumAmountByOrderId(orderId);

        // 2. 获取主表汇总
        SaleOrderVo order = saleOrderService.queryById(orderId);

        // 3. 比较并修复不一致
        if (!detailTotalQuantity.equals(order.getTotalQuantity()) ||
            !detailTotalAmount.equals(order.getTotalAmount())) {

            log.warn("销售订单{}数据不一致：明细汇总数量{} 主表数量{} 明细汇总金额{} 主表金额{}",
                     orderId, detailTotalQuantity, order.getTotalQuantity(),
                     detailTotalAmount, order.getTotalAmount());

            return repairSaleOrderConsistency(orderId, detailTotalQuantity, detailTotalAmount);
        }
        return true;
    }

    private Boolean repairSaleOrderConsistency(Long orderId, BigDecimal correctQuantity, BigDecimal correctAmount) {
        SaleOrder update = new SaleOrder();
        update.setOrderId(orderId);
        update.setTotalQuantity(correctQuantity);
        update.setTotalAmount(correctAmount);

        return saleOrderService.updateById(update);
    }
}

// 2. 实现定时任务进行数据一致性检查
@Component
public class DataConsistencyTask {

    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void checkDataConsistency() {
        log.info("开始执行数据一致性检查任务");

        // 检查最近7天的订单数据一致性
        LocalDateTime startTime = LocalDateTime.now().minusDays(7);
        List<Long> orderIds = getRecentOrderIds(startTime);

        for (Long orderId : orderIds) {
            try {
                dataConsistencyService.validateOrderConsistency(orderId, "SALE_ORDER");
            } catch (Exception e) {
                log.error("检查订单{}一致性失败：{}", orderId, e.getMessage());
            }
        }

        log.info("数据一致性检查任务完成");
    }
}
```

**验收标准**:
- [ ] 数据一致性检查逻辑正确
- [ ] 自动修复机制有效
- [ ] 定时任务正常运行

#### 🔧 任务3.2：批次状态管理统一 (1天)
**负责人**: 后端开发工程师
**优先级**: P2 - 一般

**具体任务**:
```sql
-- 1. 为批次表添加状态字段
ALTER TABLE erp_purchase_inbound_item_batch ADD COLUMN batch_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '批次状态';
ALTER TABLE erp_sale_outbound_item_batch ADD COLUMN batch_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '批次状态';
ALTER TABLE wms_inbound_item_batch ADD COLUMN batch_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '批次状态';
ALTER TABLE wms_outbound_item_batch ADD COLUMN batch_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '批次状态';
```

```java
// 2. 创建统一的批次状态枚举
public enum BatchStatus {
    PENDING("PENDING", "待处理"),
    PROCESSING("PROCESSING", "处理中"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELLED("CANCELLED", "已取消"),
    FAILED("FAILED", "失败");

    private final String code;
    private final String description;

    // 构造函数和getter方法...
}

// 3. 实现统一的批次状态管理服务
@Service
public class BatchStatusService {

    public Boolean updateBatchStatus(Long batchId, String batchType, BatchStatus newStatus, String reason) {
        // 记录状态变更日志
        recordStatusChangeLog(batchId, batchType, newStatus, reason);

        // 更新批次状态
        switch (batchType) {
            case "PURCHASE_INBOUND":
                return updatePurchaseInboundBatchStatus(batchId, newStatus);
            case "SALE_OUTBOUND":
                return updateSaleOutboundBatchStatus(batchId, newStatus);
            // 其他批次类型...
        }
        return false;
    }

    private void recordStatusChangeLog(Long batchId, String batchType, BatchStatus newStatus, String reason) {
        // 创建状态变更日志记录
        BatchStatusLog log = new BatchStatusLog();
        log.setBatchId(batchId);
        log.setBatchType(batchType);
        log.setNewStatus(newStatus.getCode());
        log.setReason(reason);
        log.setOperatorId(SecurityUtils.getUserId());
        log.setOperatorName(SecurityUtils.getUsername());
        log.setCreateTime(LocalDateTime.now());

        batchStatusLogService.save(log);
    }
}
```

**验收标准**:
- [ ] 所有批次表添加状态字段
- [ ] 状态管理逻辑统一
- [ ] 状态变更日志完整

#### 🔧 任务3.3：性能优化 (1天)
**负责人**: 后端开发工程师
**优先级**: P2 - 一般

**具体任务**:
```sql
-- 1. 添加必要的数据库索引
-- 产品ID索引
CREATE INDEX idx_inventory_batch_product_id ON wms_inventory_batch(product_id);
CREATE INDEX idx_sale_order_item_order_id ON erp_sale_order_item(order_id);
CREATE INDEX idx_purchase_order_item_order_id ON erp_purchase_order_item(order_id);

-- 复合索引
CREATE INDEX idx_inventory_batch_product_location ON wms_inventory_batch(product_id, location_id);
CREATE INDEX idx_inventory_batch_status_time ON wms_inventory_batch(batch_status, create_time);

-- 2. 优化汇总查询
-- 使用物化视图或定期更新汇总表
CREATE TABLE wms_inventory_summary (
    product_id BIGINT PRIMARY KEY,
    total_quantity DECIMAL(15,4) DEFAULT 0,
    available_quantity DECIMAL(15,4) DEFAULT 0,
    locked_quantity DECIMAL(15,4) DEFAULT 0,
    last_update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

```java
// 3. 实现批量操作优化
@Service
public class BatchOperationService {

    public Boolean batchUpdateInventory(List<InventoryUpdateRequest> requests) {
        // 按产品ID分组，减少数据库访问次数
        Map<Long, List<InventoryUpdateRequest>> groupedRequests =
            requests.stream().collect(Collectors.groupingBy(InventoryUpdateRequest::getProductId));

        for (Map.Entry<Long, List<InventoryUpdateRequest>> entry : groupedRequests.entrySet()) {
            Long productId = entry.getKey();
            List<InventoryUpdateRequest> productRequests = entry.getValue();

            // 批量处理同一产品的库存更新
            processBatchInventoryUpdate(productId, productRequests);
        }

        return true;
    }

    private void processBatchInventoryUpdate(Long productId, List<InventoryUpdateRequest> requests) {
        // 一次性获取所有相关批次
        List<InventoryBatch> batches = inventoryBatchService.getAvailableBatches(productId);

        // 批量计算和更新
        for (InventoryUpdateRequest request : requests) {
            // 处理单个更新请求
        }

        // 批量保存更新
        inventoryBatchService.updateBatchList(batches);
    }
}
```

**验收标准**:
- [ ] 数据库索引优化完成
- [ ] 查询性能提升明显
- [ ] 批量操作效率提高

### 第四阶段：测试和验证 (2-3天)

#### 🔧 任务4.1：单元测试 (1天)
**负责人**: 测试工程师 + 后端开发工程师
**优先级**: P1 - 重要

**具体任务**:
```java
// 1. 数量计算逻辑测试
@Test
public void testQuantityCalculation() {
    // 准备测试数据
    List<SaleOrderItemBo> items = createTestOrderItems();

    // 执行计算
    BigDecimal totalQuantity = saleOrderService.calculateTotalQuantity(items);

    // 验证结果
    assertEquals(new BigDecimal("100.0000"), totalQuantity);
}

// 2. 金额计算逻辑测试
@Test
public void testAmountCalculation() {
    // 测试价税分离计算
    BigDecimal amount = new BigDecimal("113.00");
    BigDecimal taxRate = new BigDecimal("13.00");

    AmountCalculationResult result = calculationService.calculateAmountWithTax(amount, taxRate);

    assertEquals(new BigDecimal("100.00"), result.getAmountExclusiveTax());
    assertEquals(new BigDecimal("13.00"), result.getTaxAmount());
}

// 3. 批次管理逻辑测试
@Test
public void testBatchFIFODeduction() {
    // 准备批次数据
    List<InventoryBatch> batches = createTestBatches();

    // 执行FIFO扣减
    Boolean result = inventoryBatchService.deductBatchesFIFO(1L, 1L, new BigDecimal("50"));

    // 验证扣减结果
    assertTrue(result);
    // 验证批次数量变化
}
```

**验收标准**:
- [ ] 单元测试覆盖率达到80%
- [ ] 所有核心计算逻辑测试通过
- [ ] 边界条件测试完整

#### 🔧 任务4.2：集成测试 (1天)
**负责人**: 测试工程师
**优先级**: P1 - 重要

**具体任务**:
- [ ] 完整出入库流程测试
- [ ] 数据一致性保证机制测试
- [ ] 并发场景测试
- [ ] 异常处理测试

**验收标准**:
- [ ] 端到端流程测试通过
- [ ] 数据一致性验证通过
- [ ] 并发安全测试通过

#### 🔧 任务4.3：性能测试 (1天)
**负责人**: 测试工程师
**优先级**: P2 - 一般

**具体任务**:
- [ ] 大数据量性能测试
- [ ] 并发操作性能测试
- [ ] 查询性能基准测试

**验收标准**:
- [ ] 性能指标满足要求
- [ ] 无明显性能瓶颈
- [ ] 资源使用合理

## 📊 风险评估和应对措施

### 高风险项
1. **数据库结构变更风险**
   - 风险：可能影响现有功能
   - 应对：充分测试，准备回滚方案

2. **并发控制实现风险**
   - 风险：可能引入死锁或性能问题
   - 应对：分阶段实施，充分测试

### 中风险项
1. **数据迁移风险**
   - 风险：历史数据可能不完整
   - 应对：编写数据修复脚本

2. **性能影响风险**
   - 风险：新增字段和逻辑可能影响性能
   - 应对：性能监控和优化

## 🎯 项目里程碑

| 里程碑 | 完成时间 | 关键交付物 |
|--------|----------|------------|
| 数据结构完善 | 第3天 | 数据库脚本、实体类更新 |
| 核心逻辑实现 | 第7天 | 汇总计算、并发控制逻辑 |
| 功能增强完成 | 第10天 | 一致性检查、状态管理 |
| 测试验证完成 | 第12天 | 测试报告、性能报告 |

## 📈 预期收益

### 短期收益 (1个月内)
- ✅ 数据计算准确性提升95%
- ✅ 并发安全问题解决
- ✅ 系统稳定性显著提升

### 长期收益 (3个月内)
- ✅ 维护成本降低30%
- ✅ 查询性能提升50%
- ✅ 用户满意度提升

---

**计划制定人**: Augment Agent
**审核人**: 项目经理
**最后更新**: 2025-06-24
