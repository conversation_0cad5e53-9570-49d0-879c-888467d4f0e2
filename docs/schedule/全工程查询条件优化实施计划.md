# 全工程查询条件优化实施计划

## 📋 项目信息

**项目名称**: iotlaser-spms全工程查询条件系统性优化  
**计划制定时间**: 2025-06-24  
**预计完成时间**: 2025-06-24  
**执行优先级**: 高  
**影响范围**: 整个iotlaser-spms工程所有模块

---

## 🎯 实施目标

### 核心目标
1. **移除无效查询条件**: 删除所有数量、金额、价格等数值字段的精确匹配查询
2. **优化日期查询**: 将所有单一日期查询改为范围查询模式
3. **统一查询规范**: 建立一致的查询条件设计和命名规范
4. **保持兼容性**: 确保API接口向后兼容

### 成功标准
- ✅ 所有无效的数值精确匹配查询条件被移除
- ✅ 所有日期字段支持范围查询
- ✅ 查询参数命名符合统一规范
- ✅ 现有功能不受影响

---

## 📝 详细实施计划

### 阶段一: iotlaser-admin模块核心业务优化 (优先级: 最高)

#### 1.1 BOM管理相关优化
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/pro/service/impl/BomItemServiceImpl.java`

**需要修改的方法**: 
- `buildQueryWrapper(BomItemBo bo)` (第86-105行)
- `buildQueryWrapperWith(BomItemBo bo)` (第300-321行)

**具体修改内容**:
```java
// 移除以下无效查询条件:
// 第97行: lqw.eq(bo.getQuantity() != null, BomItem::getQuantity, bo.getQuantity());
// 第318行: wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
```

#### 1.2 销售订单管理优化
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/erp/service/impl/SaleOrderServiceImpl.java`

**需要修改的方法**: `buildQueryWrapper(SaleOrderBo bo)` (第101-130行)

**具体修改内容**:
```java
// 移除日期精确匹配，改为范围查询:
// 第108行: lqw.eq(bo.getOrderDate() != null, SaleOrder::getOrderDate, bo.getOrderDate());

// 改为:
lqw.between(params.get("beginOrderDate") != null && params.get("endOrderDate") != null,
    SaleOrder::getOrderDate, params.get("beginOrderDate"), params.get("endOrderDate"));
```

**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/erp/service/impl/SaleOrderItemServiceImpl.java`

**需要修改的方法**: `buildQueryWrapper(SaleOrderItemBo bo)` (第79-95行)

**具体修改内容**:
```java
// 移除以下无效查询条件:
// 第87行: lqw.eq(bo.getQuantity() != null, SaleOrderItem::getQuantity, bo.getQuantity());
// 第88行: lqw.eq(bo.getPrice() != null, SaleOrderItem::getPrice, bo.getPrice());
```

#### 1.3 采购订单管理优化
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/erp/service/impl/PurchaseOrderServiceImpl.java`

**需要修改的方法**: `buildQueryWrapper(PurchaseOrderBo bo)` (第100-130行)

**具体修改内容**:
```java
// 移除日期精确匹配，改为范围查询:
// 第114行: lqw.eq(bo.getOrderDate() != null, PurchaseOrder::getOrderDate, bo.getOrderDate());

// 改为:
lqw.between(params.get("beginOrderDate") != null && params.get("endOrderDate") != null,
    PurchaseOrder::getOrderDate, params.get("beginOrderDate"), params.get("endOrderDate"));
```

**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/erp/service/impl/PurchaseOrderItemServiceImpl.java`

**需要修改的方法**: 
- `buildQueryWrapper(PurchaseOrderItemBo bo)` (第84-100行)
- `buildQueryWrapperWith(PurchaseOrderItemBo bo)` (第220-237行)

**具体修改内容**:
```java
// 移除以下无效查询条件:
// 第92行: lqw.eq(bo.getQuantity() != null, PurchaseOrderItem::getQuantity, bo.getQuantity());
// 第232行: wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
// 第233行: wrapper.eq(bo.getPrice() != null, "item.price", bo.getPrice());
```

#### 1.4 生产订单管理优化
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/mes/service/impl/ProductionOrderServiceImpl.java`

**需要修改的方法**: `buildQueryWrapper(ProductionOrderBo bo)` (第91-120行)

**预期修改**: 优化计划日期、完成日期等日期字段查询

#### 1.5 基础数据管理优化
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/base/service/impl/MeasureUnitServiceImpl.java`

**需要修改的方法**: `buildQueryWrapper(MeasureUnitBo bo)` (第67-80行)

**具体修改内容**:
```java
// 移除以下无效查询条件:
// 第74行: lqw.eq(bo.getUnitRatio() != null, MeasureUnit::getUnitRatio, bo.getUnitRatio());
// 第76行: lqw.eq(bo.getOrderNum() != null, MeasureUnit::getOrderNum, bo.getOrderNum());
```

**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/base/service/impl/LocationServiceImpl.java`

**需要修改的方法**: `buildQueryWrapper(LocationBo bo)` (第65-78行)

**具体修改内容**:
```java
// 移除以下无效查询条件:
// 第73行: lqw.eq(bo.getOrderNum() != null, Location::getOrderNum, bo.getOrderNum());
```

#### 1.6 产品实例管理优化
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/pro/service/impl/InstanceUsageServiceImpl.java`

**需要修改的方法**: `buildQueryWrapper(InstanceUsageBo bo)` (第77-90行)

**具体修改内容**:
```java
// 移除以下无效查询条件:
// 第85行: lqw.eq(bo.getQuantity() != null, InstanceUsage::getQuantity, bo.getQuantity());
```

### 阶段二: iotlaser-nonstandard模块扩展业务优化

#### 2.1 实例管理优化
**文件**: `iotlaser-modules/iotlaser-nonstandard/src/main/java/com/iotlaser/spms/mes/service/impl/InstanceManagerServiceImpl.java`

**需要修改的方法**: `buildQueryWrapperWith(InstanceManagerBo bo)` (第330-349行)

**具体修改内容**:
```java
// 移除以下无效查询条件:
// 第342行: wrapper.eq(bo.getAmountCost() != null, "manager.amount_cost ", bo.getAmountCost());
// 第343行: wrapper.eq(bo.getAmountSale() != null, "manager.amount_sale", bo.getAmountSale());
// 第344行: wrapper.eq(bo.getAmountReceived() != null, "manager.amount_received", bo.getAmountReceived());
// 第345行: wrapper.eq(bo.getAmountUnreceived() != null, "manager.amount_unreceived", bo.getAmountUnreceived());
```

#### 2.2 实例管理日志优化
**文件**: `iotlaser-modules/iotlaser-nonstandard/src/main/java/com/iotlaser/spms/mes/service/impl/InstanceManagerLogServiceImpl.java`

**需要修改的方法**: `buildQueryWrapper(InstanceManagerLogBo bo)` (第74-85行)

**具体修改内容**:
```java
// 移除日期精确匹配，改为范围查询:
// 第82行: lqw.eq(bo.getOperatorTime() != null, InstanceManagerLog::getOperatorTime, bo.getOperatorTime());

// 改为:
lqw.between(params.get("beginOperatorTime") != null && params.get("endOperatorTime") != null,
    InstanceManagerLog::getOperatorTime, params.get("beginOperatorTime"), params.get("endOperatorTime"));
```

### 阶段三: 其他模块检查和优化

#### 3.1 ruoyi-workflow模块
**文件**: `ruoyi-modules/ruoyi-workflow/src/main/java/org/dromara/workflow/service/impl/TestLeaveServiceImpl.java`

**检查内容**: 验证是否有需要优化的查询条件

#### 3.2 其他模块
**说明**: ruoyi-system和ruoyi-demo模块已经实现了标准的日期范围查询，无需优化

---

## 📊 实施时间表

| 阶段 | 模块 | 任务 | 预计时间 | 状态 |
|------|------|------|----------|------|
| 1.1 | iotlaser-admin | BOM管理优化 | 20分钟 | ✅ 已完成 |
| 1.2 | iotlaser-admin | 销售订单管理优化 | 30分钟 | ✅ 已完成 |
| 1.3 | iotlaser-admin | 采购订单管理优化 | 30分钟 | ✅ 已完成 |
| 1.4 | iotlaser-admin | 生产订单管理优化 | 25分钟 | ✅ 已完成 |
| 1.5 | iotlaser-admin | 基础数据管理优化 | 15分钟 | ✅ 已完成 |
| 1.6 | iotlaser-admin | 产品实例管理优化 | 10分钟 | ✅ 已完成 |
| 2.1 | iotlaser-nonstandard | 实例管理优化 | 15分钟 | ✅ 已完成 |
| 2.2 | iotlaser-nonstandard | 实例管理日志优化 | 10分钟 | ✅ 已完成 |
| 3 | 其他模块 | 检查和验证 | 15分钟 | ✅ 已完成 |

**总预计时间**: 2小时50分钟

---

## ⚠️ 风险控制和注意事项

### 1. 兼容性风险
**风险**: 前端可能依赖被移除的查询条件  
**控制措施**: 
- 保持API接口参数不变
- 只是忽略无效的查询条件，不报错
- 逐步通知前端团队调整

### 2. 功能回归风险
**风险**: 优化可能影响现有查询功能  
**控制措施**:
- 保留所有有业务价值的查询条件
- 充分测试核心查询场景
- 准备回滚方案

### 3. 跨模块影响风险
**风险**: 不同模块间可能存在查询依赖  
**控制措施**:
- 分模块逐步实施
- 重点测试模块间接口调用
- 保持服务接口稳定

---

## 🎯 预期收益

### 1. 用户体验提升
- 查询条件更符合实际业务需求
- 日期范围查询提供更好的灵活性
- 减少无用查询选项的困扰

### 2. 系统性能优化
- 减少无效查询条件的处理开销
- 简化查询逻辑，提高代码可维护性
- 为后续性能优化奠定基础

### 3. 代码质量改进
- 统一查询条件设计规范
- 提高代码的一致性和可读性
- 减少潜在的查询逻辑错误

---

*计划制定时间: 2025-06-24*  
*计划执行范围: 整个iotlaser-spms工程*  
*遵循约束: 不新增数据库字段，保持API兼容性*
