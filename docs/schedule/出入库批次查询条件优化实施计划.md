# 出入库批次查询条件优化实施计划

## 📋 项目信息

**项目名称**: 出入库批次查询条件系统性优化实施  
**计划制定时间**: 2025-06-24  
**预计完成时间**: 2025-06-24  
**执行优先级**: 高  
**影响范围**: iotlaser-admin模块出入库批次管理

---

## 🎯 实施目标

### 核心目标
1. **移除无效查询条件**: 删除数量、金额等数值字段的精确匹配查询
2. **优化日期查询**: 将单一日期查询改为范围查询模式
3. **统一查询规范**: 建立一致的日期范围查询参数命名
4. **保持兼容性**: 确保API接口向后兼容

### 成功标准
- ✅ 所有无效的数值精确匹配查询条件被移除
- ✅ 所有日期字段支持范围查询
- ✅ 查询参数命名符合系统规范
- ✅ 现有功能不受影响

---

## 📝 详细实施计划

### 阶段一: 库存批次查询优化 (优先级: 最高)

#### 1.1 InventoryBatchServiceImpl 优化
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/wms/service/impl/InventoryBatchServiceImpl.java`

**需要修改的方法**: `buildQueryWrapper(InventoryBatchBo bo)` (第76-106行)

**具体修改内容**:
```java
// 移除以下无效查询条件:
// 第98行: lqw.eq(bo.getQuantity() != null, InventoryBatch::getQuantity, bo.getQuantity());
// 第99行: lqw.eq(bo.getCostPrice() != null, InventoryBatch::getCostPrice, bo.getCostPrice());

// 修改日期查询为范围查询:
// 第100行: lqw.eq(bo.getInventoryTime() != null, InventoryBatch::getInventoryTime, bo.getInventoryTime());
// 第101行: lqw.eq(bo.getExpiryTime() != null, InventoryBatch::getExpiryTime, bo.getExpiryTime());

// 改为:
lqw.between(params.get("beginInventoryTime") != null && params.get("endInventoryTime") != null,
    InventoryBatch::getInventoryTime, params.get("beginInventoryTime"), params.get("endInventoryTime"));
lqw.between(params.get("beginExpiryTime") != null && params.get("endExpiryTime") != null,
    InventoryBatch::getExpiryTime, params.get("beginExpiryTime"), params.get("endExpiryTime"));
```

**保留的查询条件**:
- 管理方式、批次号、序列号等字符串字段
- 产品ID、库位ID等关联字段
- 库存状态等枚举字段

#### 1.2 InventoryBatchBo 类优化 (如需要)
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/wms/domain/bo/InventoryBatchBo.java`

**说明**: 由于使用params参数传递日期范围，Bo类本身可能不需要修改

### 阶段二: 出库批次查询优化

#### 2.1 OutboundItemBatchServiceImpl 优化
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/wms/service/impl/OutboundItemBatchServiceImpl.java`

**需要修改的方法**: `buildQueryWrapper(OutboundItemBatchBo bo)` (第77-100行)

**具体修改内容**:
```java
// 移除以下无效查询条件:
// 第93行: lqw.eq(bo.getQuantity() != null, OutboundItemBatch::getQuantity, bo.getQuantity());
// 第94行: lqw.eq(bo.getPrice() != null, OutboundItemBatch::getPrice, bo.getPrice());
```

**保留的查询条件**:
- 明细ID、出库单ID、库存批次ID等关联字段
- 批次号、产品编码等标识字段
- 产品名称、库位名称等模糊查询字段

#### 2.2 InboundItemBatchServiceImpl 优化
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/wms/service/impl/InboundItemBatchServiceImpl.java`

**预期修改**: 类似OutboundItemBatch的优化模式，移除数量和价格的精确匹配查询

### 阶段三: 其他批次相关查询优化

#### 3.1 SaleOutboundItemBatchServiceImpl 优化
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/erp/service/impl/SaleOutboundItemBatchServiceImpl.java`

#### 3.2 PurchaseInboundItemBatchServiceImpl 优化  
**文件**: `iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/erp/service/impl/PurchaseInboundItemBatchServiceImpl.java`

#### 3.3 其他相关批次Service类
- ProductionInboundItemBatchServiceImpl
- ProductionIssueItemBatchServiceImpl
- TransferItemBatchServiceImpl

### 阶段四: 测试验证和文档更新

#### 4.1 功能测试
- 验证查询功能正常工作
- 确认移除的查询条件不影响现有功能
- 测试日期范围查询的准确性

#### 4.2 性能测试
- 对比优化前后的查询性能
- 验证查询条件简化后的响应时间

#### 4.3 文档更新
- 更新API文档中的查询参数说明
- 记录优化内容和影响范围

---

## ⚠️ 风险控制和注意事项

### 1. 兼容性风险
**风险**: 前端可能依赖被移除的查询条件  
**控制措施**: 
- 保持API接口参数不变
- 只是忽略无效的查询条件，不报错
- 逐步通知前端团队调整

### 2. 功能回归风险
**风险**: 优化可能影响现有查询功能  
**控制措施**:
- 保留所有有业务价值的查询条件
- 充分测试核心查询场景
- 准备回滚方案

### 3. 性能风险
**风险**: 日期范围查询可能影响性能  
**控制措施**:
- 日期范围查询性能通常优于或等同于精确查询
- 如有性能问题，考虑添加数据库索引

---

## 📊 实施时间表

| 阶段 | 任务 | 预计时间 | 负责人 | 状态 |
|------|------|----------|--------|------|
| 1 | 库存批次查询优化 | 30分钟 | Agent | ✅ 已完成 |
| 2 | 出库批次查询优化 | 30分钟 | Agent | ✅ 已完成 |
| 3 | 其他批次查询优化 | 45分钟 | Agent | ✅ 已完成 |
| 4 | 测试验证 | 15分钟 | Agent | ✅ 已完成 |
| 5 | 文档更新 | 15分钟 | Agent | ✅ 已完成 |

**总预计时间**: 2小时15分钟

---

## 🎯 预期收益

### 1. 用户体验提升
- 查询条件更符合实际业务需求
- 日期范围查询提供更好的灵活性
- 减少无用查询选项的困扰

### 2. 系统性能优化
- 减少无效查询条件的处理开销
- 简化查询逻辑，提高代码可维护性
- 为后续性能优化奠定基础

### 3. 代码质量改进
- 统一查询条件设计规范
- 提高代码的一致性和可读性
- 减少潜在的查询逻辑错误

---

*计划制定时间: 2025-06-24*  
*计划执行范围: iotlaser-admin模块*  
*遵循约束: 不新增数据库字段，保持API兼容性*
