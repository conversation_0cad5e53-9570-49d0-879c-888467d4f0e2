# 出入库批次管理数据库索引优化建议

## 📋 文档信息

**创建时间**: 2025-06-24  
**适用范围**: iotlaser-admin模块出入库批次管理  
**优化目标**: 提升查询性能，优化汇总计算效率  

## 🎯 索引优化策略

### 1. 核心原则
- **高频查询优先**: 优先为高频查询字段创建索引
- **复合索引优化**: 合理设计复合索引，减少索引数量
- **避免过度索引**: 平衡查询性能和写入性能
- **定期维护**: 定期分析索引使用情况，清理无效索引

### 2. 查询模式分析
- **按产品查询**: product_id是最常用的查询条件
- **按库位查询**: location_id常与product_id组合查询
- **按时间范围查询**: create_time、update_time用于时间范围查询
- **按状态查询**: 各种状态字段用于状态筛选
- **汇总计算**: 需要支持SUM、COUNT等聚合查询

## 📊 推荐索引清单

### 1. 库存批次表 (wms_inventory_batch)

#### 1.1 单列索引
```sql
-- 产品ID索引（高频查询）
CREATE INDEX idx_inventory_batch_product_id ON wms_inventory_batch(product_id);

-- 库位ID索引（高频查询）
CREATE INDEX idx_inventory_batch_location_id ON wms_inventory_batch(location_id);

-- 批次状态索引（状态筛选）
CREATE INDEX idx_inventory_batch_status ON wms_inventory_batch(inventory_status);

-- 创建时间索引（FIFO排序）
CREATE INDEX idx_inventory_batch_create_time ON wms_inventory_batch(create_time);
```

#### 1.2 复合索引
```sql
-- 产品+库位复合索引（最常用组合）
CREATE INDEX idx_inventory_batch_product_location ON wms_inventory_batch(product_id, location_id);

-- 产品+状态复合索引（可用库存查询）
CREATE INDEX idx_inventory_batch_product_status ON wms_inventory_batch(product_id, inventory_status);

-- 库位+状态复合索引（库位库存查询）
CREATE INDEX idx_inventory_batch_location_status ON wms_inventory_batch(location_id, inventory_status);

-- 状态+时间复合索引（FIFO扣减）
CREATE INDEX idx_inventory_batch_status_time ON wms_inventory_batch(inventory_status, create_time);

-- 产品+库位+状态复合索引（精确查询）
CREATE INDEX idx_inventory_batch_product_location_status ON wms_inventory_batch(product_id, location_id, inventory_status);
```

#### 1.3 覆盖索引
```sql
-- 库存汇总查询覆盖索引
CREATE INDEX idx_inventory_batch_summary ON wms_inventory_batch(product_id, location_id, inventory_status, quantity);
```

### 2. 销售订单明细表 (erp_sale_order_item)

#### 2.1 单列索引
```sql
-- 订单ID索引（明细查询）
CREATE INDEX idx_sale_order_item_order_id ON erp_sale_order_item(order_id);

-- 产品ID索引（产品相关查询）
CREATE INDEX idx_sale_order_item_product_id ON erp_sale_order_item(product_id);
```

#### 2.2 复合索引
```sql
-- 订单+产品复合索引
CREATE INDEX idx_sale_order_item_order_product ON erp_sale_order_item(order_id, product_id);
```

#### 2.3 覆盖索引
```sql
-- 订单汇总查询覆盖索引
CREATE INDEX idx_sale_order_item_summary ON erp_sale_order_item(order_id, quantity, amount, amount_exclusive_tax);
```

### 3. 采购订单明细表 (erp_purchase_order_item)

#### 3.1 单列索引
```sql
-- 订单ID索引
CREATE INDEX idx_purchase_order_item_order_id ON erp_purchase_order_item(order_id);

-- 产品ID索引
CREATE INDEX idx_purchase_order_item_product_id ON erp_purchase_order_item(product_id);
```

#### 3.2 复合索引
```sql
-- 订单+产品复合索引
CREATE INDEX idx_purchase_order_item_order_product ON erp_purchase_order_item(order_id, product_id);
```

#### 3.3 覆盖索引
```sql
-- 订单汇总查询覆盖索引
CREATE INDEX idx_purchase_order_item_summary ON erp_purchase_order_item(order_id, quantity, amount, amount_exclusive_tax);
```

### 4. 仓库入库明细表 (wms_inbound_item)

#### 4.1 单列索引
```sql
-- 入库单ID索引
CREATE INDEX idx_inbound_item_inbound_id ON wms_inbound_item(inbound_id);

-- 产品ID索引
CREATE INDEX idx_inbound_item_product_id ON wms_inbound_item(product_id);
```

#### 4.2 复合索引
```sql
-- 入库单+产品复合索引
CREATE INDEX idx_inbound_item_inbound_product ON wms_inbound_item(inbound_id, product_id);
```

### 5. 仓库出库明细表 (wms_outbound_item)

#### 5.1 单列索引
```sql
-- 出库单ID索引
CREATE INDEX idx_outbound_item_outbound_id ON wms_outbound_item(outbound_id);

-- 产品ID索引
CREATE INDEX idx_outbound_item_product_id ON wms_outbound_item(product_id);
```

#### 5.2 复合索引
```sql
-- 出库单+产品复合索引
CREATE INDEX idx_outbound_item_outbound_product ON wms_outbound_item(outbound_id, product_id);
```

### 6. 批次表索引

#### 6.1 采购入库批次表 (erp_purchase_inbound_item_batch)
```sql
-- 明细ID索引
CREATE INDEX idx_purchase_inbound_batch_item_id ON erp_purchase_inbound_item_batch(item_id);

-- 产品ID索引
CREATE INDEX idx_purchase_inbound_batch_product_id ON erp_purchase_inbound_item_batch(product_id);

-- 批次号索引
CREATE INDEX idx_purchase_inbound_batch_number ON erp_purchase_inbound_item_batch(batch_number);
```

#### 6.2 销售出库批次表 (erp_sale_outbound_item_batch)
```sql
-- 明细ID索引
CREATE INDEX idx_sale_outbound_batch_item_id ON erp_sale_outbound_item_batch(item_id);

-- 产品ID索引
CREATE INDEX idx_sale_outbound_batch_product_id ON erp_sale_outbound_item_batch(product_id);

-- 批次号索引
CREATE INDEX idx_sale_outbound_batch_number ON erp_sale_outbound_item_batch(batch_number);
```

#### 6.3 仓库入库批次表 (wms_inbound_item_batch)
```sql
-- 明细ID索引
CREATE INDEX idx_inbound_batch_item_id ON wms_inbound_item_batch(item_id);

-- 产品ID索引
CREATE INDEX idx_inbound_batch_product_id ON wms_inbound_item_batch(product_id);

-- 批次号索引
CREATE INDEX idx_inbound_batch_number ON wms_inbound_item_batch(batch_number);
```

#### 6.4 仓库出库批次表 (wms_outbound_item_batch)
```sql
-- 明细ID索引
CREATE INDEX idx_outbound_batch_item_id ON wms_outbound_item_batch(item_id);

-- 产品ID索引
CREATE INDEX idx_outbound_batch_product_id ON wms_outbound_item_batch(product_id);

-- 批次号索引
CREATE INDEX idx_outbound_batch_number ON wms_outbound_item_batch(batch_number);
```

## 🚀 性能优化建议

### 1. 查询优化
- **使用覆盖索引**: 对于汇总查询，使用覆盖索引避免回表
- **合理使用LIMIT**: 分页查询时使用LIMIT减少数据传输
- **避免SELECT ***: 只查询需要的字段
- **使用EXISTS替代IN**: 对于子查询，使用EXISTS性能更好

### 2. 索引维护
- **定期分析**: 使用EXPLAIN分析查询执行计划
- **监控使用情况**: 定期检查索引使用统计
- **清理无效索引**: 删除从未使用的索引
- **重建碎片索引**: 定期重建碎片化严重的索引

### 3. 写入优化
- **批量操作**: 使用批量插入/更新减少索引维护开销
- **合理的事务大小**: 避免长事务影响索引维护
- **延迟索引更新**: 对于大批量操作，考虑临时禁用索引

## 📈 预期性能提升

### 1. 查询性能提升
- **库存查询**: 提升50-80%
- **订单汇总**: 提升60-90%
- **批次查询**: 提升40-70%
- **状态筛选**: 提升30-60%

### 2. 并发性能提升
- **锁等待时间**: 减少20-40%
- **死锁概率**: 降低30-50%
- **事务吞吐量**: 提升25-45%

### 3. 存储优化
- **索引空间**: 合理控制在表空间的15-25%
- **查询缓存**: 提升缓存命中率20-35%

## ⚠️ 注意事项

### 1. 索引创建顺序
- 先创建单列索引，再创建复合索引
- 避免重复索引（复合索引可以覆盖单列索引的功能）
- 考虑现有数据量，大表创建索引时选择低峰期

### 2. 监控指标
- 查询响应时间
- 索引使用率
- 锁等待时间
- 磁盘I/O使用率

### 3. 回滚计划
- 保留索引创建脚本的回滚脚本
- 监控创建索引后的性能变化
- 如有性能下降，及时回滚

---

**文档维护**: 定期更新索引使用情况和性能数据  
**执行建议**: 分批执行，先在测试环境验证效果  
**监控周期**: 索引创建后持续监控1-2周
