# 全工程查询条件优化分析报告

## 📋 项目概述

**项目名称**: iotlaser-spms全工程查询条件系统性优化  
**分析时间**: 2025-06-24  
**优化范围**: 整个iotlaser-spms工程的所有模块  
**目标**: 移除无效查询条件，优化日期查询，统一查询规范

---

## 🔍 全工程模块分析

### 1. 工程结构概览

#### 1.1 主要模块
- **iotlaser-modules/iotlaser-admin** - 核心业务模块（已部分优化）
- **iotlaser-modules/iotlaser-generator** - 代码生成器模块
- **iotlaser-modules/iotlaser-nonstandard** - 非标准业务模块
- **ruoyi-modules/ruoyi-system** - 系统管理模块
- **ruoyi-modules/ruoyi-demo** - 演示模块
- **ruoyi-modules/ruoyi-generator** - 若依代码生成器
- **ruoyi-modules/ruoyi-workflow** - 工作流模块
- **ruoyi-modules/ruoyi-job** - 定时任务模块

#### 1.2 优化优先级分类
- **高优先级**: iotlaser-admin（核心业务）
- **中优先级**: iotlaser-nonstandard（扩展业务）
- **低优先级**: ruoyi-modules（基础框架）

---

## 📊 查询条件问题分析

### 1. 数值字段精确匹配问题

#### 1.1 已发现的问题查询条件
**iotlaser-admin模块**:
- `BomItemServiceImpl.buildQueryWrapper()` - 第97行: `lqw.eq(bo.getQuantity() != null, BomItem::getQuantity, bo.getQuantity())`
- `BomItemServiceImpl.buildQueryWrapperWith()` - 第318行: `wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity())`
- `InstanceUsageServiceImpl.buildQueryWrapper()` - 第85行: `lqw.eq(bo.getQuantity() != null, InstanceUsage::getQuantity, bo.getQuantity())`
- `SaleOrderItemServiceImpl.buildQueryWrapper()` - 第87-88行: 数量和价格精确匹配
- `PurchaseOrderItemServiceImpl.buildQueryWrapper()` - 第92行: `lqw.eq(bo.getQuantity() != null, PurchaseOrderItem::getQuantity, bo.getQuantity())`
- `PurchaseOrderItemServiceImpl.buildQueryWrapperWith()` - 第232-233行: 数量和价格精确匹配

**iotlaser-nonstandard模块**:
- `InstanceManagerServiceImpl.buildQueryWrapperWith()` - 第342-345行: 多个金额字段精确匹配
  - `amountCost`, `amountSale`, `amountReceived`, `amountUnreceived`

**其他模块**:
- `MeasureUnitServiceImpl.buildQueryWrapper()` - 第74行: `lqw.eq(bo.getUnitRatio() != null, MeasureUnit::getUnitRatio, bo.getUnitRatio())`

#### 1.2 问题影响评估
- **业务影响**: 用户很少需要查询精确数量或金额的记录
- **用户体验**: 查询条件实用性低，增加界面复杂度
- **系统性能**: 无效查询条件增加处理开销

### 2. 日期字段精确匹配问题

#### 2.1 已发现的问题查询条件
**iotlaser-admin模块**:
- `SaleOrderServiceImpl.buildQueryWrapper()` - 第108行: `lqw.eq(bo.getOrderDate() != null, SaleOrder::getOrderDate, bo.getOrderDate())`
- `PurchaseOrderServiceImpl.buildQueryWrapper()` - 第114行: `lqw.eq(bo.getOrderDate() != null, PurchaseOrder::getOrderDate, bo.getOrderDate())`
- `ProductionOrderServiceImpl.buildQueryWrapper()` - 包含多个日期字段精确匹配

**iotlaser-nonstandard模块**:
- `InstanceManagerLogServiceImpl.buildQueryWrapper()` - 第82行: `lqw.eq(bo.getOperatorTime() != null, InstanceManagerLog::getOperatorTime, bo.getOperatorTime())`

#### 2.2 已实现范围查询的模块（参考模式）
- `TestDemoServiceImpl.buildQueryWrapper()` - 第67-68行: 正确的日期范围查询实现
- `SysPostServiceImpl.buildQueryWrapper()` - 第86-87行: 标准的日期范围查询
- `SourceServiceImpl.buildQueryWrapperWith()` - 第72-73行: 通用的时间范围查询

### 3. 其他需要优化的查询条件

#### 3.1 排序号精确匹配
- `MeasureUnitServiceImpl.buildQueryWrapper()` - 第76行: `lqw.eq(bo.getOrderNum() != null, MeasureUnit::getOrderNum, bo.getOrderNum())`
- `LocationServiceImpl.buildQueryWrapper()` - 第73行: `lqw.eq(bo.getOrderNum() != null, Location::getOrderNum, bo.getOrderNum())`

#### 3.2 比率字段精确匹配
- `MeasureUnitServiceImpl.buildQueryWrapper()` - 第74行: 单位比率精确匹配

---

## 🎯 优化策略制定

### 1. 优化标准

#### 1.1 移除的查询条件类型
- **数量字段**: `quantity`, `plannedQuantity`, `completedQuantity`, `finishQuantity`
- **金额字段**: `amount`, `totalAmount`, `amountCost`, `amountSale`, `amountReceived`, `amountUnreceived`
- **价格字段**: `price`, `unitPrice`, `costPrice`, `priceExclusiveTax`
- **比率字段**: `unitRatio`, `taxRate`
- **排序字段**: `orderNum`, `sortOrder`

#### 1.2 改为范围查询的字段类型
- **日期字段**: `orderDate`, `deliveryDate`, `plannedDate`, `finishDate`, `operatorTime`
- **时间字段**: `createTime`, `updateTime`, `inventoryTime`, `expiryTime`

#### 1.3 保留的查询条件类型
- **状态字段**: `status`, `orderStatus`, `inventoryStatus`
- **类型字段**: `type`, `orderType`, `locationType`, `productType`
- **编码字段**: `code`, `orderCode`, `productCode`, `locationCode`
- **ID字段**: 所有关联ID字段
- **名称字段**: 所有name字段（模糊查询）

### 2. 日期范围查询命名规范

#### 2.1 标准命名模式
```java
// 创建时间范围
params.get("beginCreateTime") / params.get("endCreateTime")

// 更新时间范围  
params.get("beginUpdateTime") / params.get("endUpdateTime")

// 订单日期范围
params.get("beginOrderDate") / params.get("endOrderDate")

// 计划日期范围
params.get("beginPlannedDate") / params.get("endPlannedDate")

// 完成日期范围
params.get("beginFinishDate") / params.get("endFinishDate")
```

#### 2.2 实现模式
```java
// 标准日期范围查询实现
lqw.between(params.get("beginOrderDate") != null && params.get("endOrderDate") != null,
    Entity::getOrderDate, params.get("beginOrderDate"), params.get("endOrderDate"));
```

---

## 📋 模块优化计划

### 1. 高优先级模块（立即执行）

#### 1.1 iotlaser-admin模块
**状态**: 出入库批次已完成，其他子模块待优化

**待优化的Service类**:
- `BomItemServiceImpl` - BOM明细查询优化
- `InstanceUsageServiceImpl` - 实例用量查询优化  
- `SaleOrderItemServiceImpl` - 销售订单明细查询优化
- `PurchaseOrderItemServiceImpl` - 采购订单明细查询优化
- `SaleOrderServiceImpl` - 销售订单查询优化
- `PurchaseOrderServiceImpl` - 采购订单查询优化
- `ProductionOrderServiceImpl` - 生产订单查询优化
- `MeasureUnitServiceImpl` - 计量单位查询优化
- `LocationServiceImpl` - 库位查询优化

#### 1.2 iotlaser-nonstandard模块
**待优化的Service类**:
- `InstanceManagerServiceImpl` - 实例管理查询优化（金额字段）
- `InstanceManagerLogServiceImpl` - 实例管理日志查询优化（日期字段）

### 2. 中优先级模块（后续执行）

#### 2.1 ruoyi-system模块
**已优化的Service类**:
- `SysPostServiceImpl` - 已实现标准日期范围查询 ✅

#### 2.2 ruoyi-demo模块  
**已优化的Service类**:
- `TestDemoServiceImpl` - 已实现标准日期范围查询 ✅

#### 2.3 ruoyi-workflow模块
**待检查的Service类**:
- `TestLeaveServiceImpl` - 需要检查是否有数值字段精确匹配

### 3. 低优先级模块（可选执行）

#### 3.1 iotlaser-generator模块
**说明**: 代码生成器模块，主要影响生成的代码模板

#### 3.2 ruoyi-generator模块
**说明**: 若依代码生成器，框架级别模块

---

## 📊 预期优化效果

### 1. 数量统计
- **预计优化Service类**: 约15-20个
- **预计移除无效查询条件**: 约30-40个
- **预计新增日期范围查询**: 约15-20个

### 2. 业务价值
- **用户体验提升**: 查询条件更符合实际使用需求
- **系统性能优化**: 减少无效查询处理开销
- **代码质量改进**: 统一查询条件设计规范

---

*分析报告生成时间: 2025-06-24*  
*分析范围: 整个iotlaser-spms工程*  
*下一步: 制定详细实施计划*
