# 应收发票明细表设计文档

## 📋 设计概述

**表名**: `erp_fin_ar_receivable_item`  
**用途**: 存储应收发票的明细信息，支持精细化对账和业务追溯  
**设计时间**: 2025-06-24  
**状态**: TODO - 待数据库实现  

## 🗃️ 表结构设计

### 主要字段

```sql
CREATE TABLE erp_fin_ar_receivable_item (
    -- 主键和关联字段
    item_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '明细ID',
    receivable_id BIGINT NOT NULL COMMENT '应收单ID',
    source_item_id BIGINT COMMENT '来源明细ID(销售订单明细ID)',
    
    -- 产品信息
    product_id BIGINT COMMENT '产品ID',
    product_code VARCHAR(100) COMMENT '产品编码',
    product_name VARCHAR(200) COMMENT '产品名称',
    
    -- 计量单位信息
    unit_id BIGINT COMMENT '计量单位ID',
    unit_code VARCHAR(50) COMMENT '计量单位编码',
    unit_name VARCHAR(100) COMMENT '计量单位名称',
    
    -- 数量和价格
    quantity DECIMAL(15,4) NOT NULL DEFAULT 0 COMMENT '数量',
    price DECIMAL(15,4) COMMENT '含税单价',
    price_exclusive_tax DECIMAL(15,4) COMMENT '不含税单价',
    
    -- 金额信息
    amount DECIMAL(15,2) NOT NULL DEFAULT 0 COMMENT '含税金额',
    amount_exclusive_tax DECIMAL(15,2) DEFAULT 0 COMMENT '不含税金额',
    tax_rate DECIMAL(5,2) DEFAULT 0 COMMENT '税率(%)',
    tax_amount DECIMAL(15,2) DEFAULT 0 COMMENT '税额',
    
    -- 业务状态
    item_status VARCHAR(50) DEFAULT 'NORMAL' COMMENT '明细状态',
    remark VARCHAR(500) COMMENT '备注',
    
    -- 系统字段
    status CHAR(1) DEFAULT '1' COMMENT '状态(1正常 0停用)',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志(0代表存在 2代表删除)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by BIGINT COMMENT '更新者',
    
    -- 租户字段
    tenant_id VARCHAR(20) DEFAULT '000000' COMMENT '租户编号',
    
    -- 外键约束
    FOREIGN KEY (receivable_id) REFERENCES erp_fin_ar_receivable(receivable_id),
    FOREIGN KEY (source_item_id) REFERENCES erp_sale_order_item(item_id),
    
    -- 索引
    INDEX idx_receivable_item_receivable_id (receivable_id),
    INDEX idx_receivable_item_source (source_item_id),
    INDEX idx_receivable_item_product (product_id),
    INDEX idx_receivable_item_tenant (tenant_id)
) COMMENT='应收发票明细表';
```

## 🔗 数据关联关系

### 主要关联

1. **应收发票主表**: `erp_fin_ar_receivable`
   - 关联字段: `receivable_id`
   - 关系: 一对多（一个应收单对应多个明细）

2. **销售订单明细**: `erp_sale_order_item`
   - 关联字段: `source_item_id`
   - 关系: 一对一（一个订单明细对应一个应收明细）

3. **产品主数据**: `erp_product`
   - 关联字段: `product_id`
   - 关系: 多对一（多个明细可以是同一产品）

4. **计量单位**: `erp_unit`
   - 关联字段: `unit_id`
   - 关系: 多对一（多个明细可以使用同一单位）

## 📊 业务字段说明

### 核心业务字段

| 字段名 | 类型 | 说明 | 业务用途 |
|--------|------|------|----------|
| `receivable_id` | BIGINT | 应收单ID | 关联应收发票主表 |
| `source_item_id` | BIGINT | 来源明细ID | 追溯到销售订单明细 |
| `product_id` | BIGINT | 产品ID | 产品主数据关联 |
| `quantity` | DECIMAL(15,4) | 数量 | 开票数量 |
| `amount` | DECIMAL(15,2) | 含税金额 | 明细含税金额 |

### 冗余字段设计

为了提升查询性能和保证历史数据稳定性，设计了以下冗余字段：

| 冗余字段 | 来源表 | 说明 |
|----------|--------|------|
| `product_code` | erp_product | 产品编码冗余 |
| `product_name` | erp_product | 产品名称冗余 |
| `unit_code` | erp_unit | 单位编码冗余 |
| `unit_name` | erp_unit | 单位名称冗余 |

## 🔄 数据流转设计

### 数据来源

```mermaid
graph TD
    A[销售订单明细] --> B[应收发票明细]
    C[产品主数据] --> B
    D[计量单位] --> B
    E[税率配置] --> B
```

### 数据传递逻辑

```java
// 从销售订单明细创建应收发票明细
public void createReceivableItemFromOrderItem(Long receivableId, SaleOrderItemVo orderItem) {
    FinArReceivableItem item = new FinArReceivableItem();
    
    // 关联信息
    item.setReceivableId(receivableId);
    item.setSourceItemId(orderItem.getItemId());
    
    // 产品信息传递
    item.setProductId(orderItem.getProductId());
    item.setProductCode(orderItem.getProductCode());
    item.setProductName(orderItem.getProductName());
    
    // 计量单位传递
    item.setUnitId(orderItem.getUnitId());
    item.setUnitCode(orderItem.getUnitCode());
    item.setUnitName(orderItem.getUnitName());
    
    // 数量和金额传递
    item.setQuantity(orderItem.getQuantity());
    item.setPrice(orderItem.getPrice());
    item.setPriceExclusiveTax(orderItem.getPriceExclusiveTax());
    item.setAmount(orderItem.getAmount());
    item.setAmountExclusiveTax(orderItem.getAmountExclusiveTax());
    item.setTaxRate(orderItem.getTaxRate());
    item.setTaxAmount(orderItem.getTaxAmount());
    
    // 保存明细
    receivableItemService.insert(item);
}
```

## 🎯 业务价值

### 1. 精细化对账
- 支持明细级别的对账
- 可以精确追踪每个产品的开票情况
- 便于发现明细级别的差异

### 2. 业务追溯
- 从应收发票明细可以追溯到销售订单明细
- 支持完整的业务链路追踪
- 便于问题定位和分析

### 3. 报表分析
- 支持按产品维度的应收分析
- 支持按客户+产品的详细报表
- 提供更丰富的数据分析维度

### 4. 数据一致性
- 明细金额汇总与主表金额一致性校验
- 支持明细级别的数据校验
- 提升数据质量

## 🔧 实现计划

### 第一阶段：实体类设计
```java
// TODO: 创建 FinArReceivableItem 实体类
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_ar_receivable_item")
public class FinArReceivableItem extends TenantEntity {
    // 字段定义...
}
```

### 第二阶段：Service层实现
```java
// TODO: 创建 IFinArReceivableItemService 接口
public interface IFinArReceivableItemService {
    // 基础CRUD方法
    // 业务方法：从订单明细创建应收明细
    // 查询方法：按应收单ID查询明细列表
}
```

### 第三阶段：业务集成
```java
// TODO: 在 FinArReceivableServiceImpl 中集成明细创建逻辑
public Long generateFromSaleOrder(...) {
    // 创建应收发票主记录
    Long receivableId = createReceivableHeader(...);
    
    // 创建应收发票明细
    createReceivableItems(receivableId, orderItems);
    
    return receivableId;
}
```

## ⚠️ 注意事项

### 数据一致性
1. 明细金额汇总必须等于主表金额
2. 明细数量汇总应该合理
3. 税额计算必须准确

### 性能考虑
1. 合理设计索引，提升查询性能
2. 考虑分页查询，避免大数据量问题
3. 冗余字段权衡存储空间和查询性能

### 扩展性
1. 预留扩展字段，支持未来业务需求
2. 状态字段设计要考虑业务流程
3. 支持多租户架构

---

**设计状态**: TODO - 待实现  
**优先级**: P1 - 重要  
**预计工作量**: 3-5天  
**依赖**: 应收发票主表功能完善
