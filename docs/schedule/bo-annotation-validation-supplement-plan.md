# Bo类注解校验补充计划

## 📋 **补充概述**

根据validEntityBeforeSave方法重构结果，需要补充相关Bo类的注解配置，确保基础校验完全覆盖删除的Entity校验逻辑。

## 🔍 **需要补充的注解配置**

### **1. TransferBo类补充**

#### **当前缺失的注解**：
- `transferName` 字段缺少 `@NotBlank` 注解

#### **补充方案**：
```java
/**
 * 移库单名称
 */
@NotBlank(message = "移库单名称不能为空", groups = {AddGroup.class, EditGroup.class})
private String transferName;
```

### **2. InboundItemBo类补充**

#### **当前缺失的注解**：
- `locationId` 字段缺少 `@NotNull` 注解
- `quantity` 字段缺少 `@DecimalMin` 注解

#### **补充方案**：
```java
/**
 * 位置库位ID
 */
@NotNull(message = "库位不能为空", groups = {AddGroup.class, EditGroup.class})
private Long locationId;

/**
 * 待完成数量
 */
@NotNull(message = "待完成数量不能为空", groups = {AddGroup.class, EditGroup.class})
@DecimalMin(value = "0.01", message = "入库数量必须大于0")
private BigDecimal quantity;
```

### **3. OutboundItemBo类补充**

#### **需要检查的字段**：
- `outboundId` - 应有 `@NotNull` 注解
- `productId` - 应有 `@NotNull` 注解
- `locationId` - 应有 `@NotNull` 注解
- `quantity` - 应有 `@NotNull` 和 `@DecimalMin` 注解

### **4. TransferItemBo类补充**

#### **需要检查的字段**：
- `transferId` - 应有 `@NotNull` 注解
- `productId` - 应有 `@NotNull` 注解
- `fromLocationId` - 应有 `@NotNull` 注解
- `toLocationId` - 应有 `@NotNull` 注解
- `quantity` - 应有 `@NotNull` 和 `@DecimalMin` 注解

### **5. InboundItemBatchBo类补充**

#### **需要检查的字段**：
- `itemId` - 应有 `@NotNull` 注解
- `internalBatchNumber` - 应有 `@NotBlank` 注解
- `locationId` - 应有 `@NotNull` 注解
- `quantity` - 应有 `@NotNull` 和 `@DecimalMin` 注解

### **6. OutboundItemBatchBo类补充**

#### **需要检查的字段**：
- `itemId` - 应有 `@NotNull` 注解
- `inventoryBatchId` - 应有 `@NotNull` 注解
- `quantity` - 应有 `@NotNull` 和 `@DecimalMin` 注解

## 📊 **注解配置对应关系表**

| 删除的Entity校验 | Bo类字段 | 需要的注解 | 注解示例 |
|-----------------|----------|-----------|----------|
| `StringUtils.isBlank(entity.getTransferName())` | `transferName` | `@NotBlank` | `@NotBlank(message = "移库单名称不能为空", groups = {AddGroup.class, EditGroup.class})` |
| `entity.getTransferDate() == null` | `transferDate` | `@NotNull` | ✅ 已存在 |
| `entity.getInboundId() == null` | `inboundId` | `@NotNull` | ✅ 已存在 |
| `entity.getProductId() == null` | `productId` | `@NotNull` | ✅ 已存在 |
| `entity.getLocationId() == null` | `locationId` | `@NotNull` | ⚠️ 需要补充 |
| `entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0` | `quantity` | `@DecimalMin` | ⚠️ 需要补充 |
| `StringUtils.isBlank(entity.getInternalBatchNumber())` | `internalBatchNumber` | `@NotBlank` | ⚠️ 需要检查 |

## 🎯 **执行计划**

### **第一步：补充TransferBo注解**
```java
// 文件：iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/wms/domain/bo/TransferBo.java
// 第43行修改：
@NotBlank(message = "移库单名称不能为空", groups = {AddGroup.class, EditGroup.class})
private String transferName;
```

### **第二步：补充InboundItemBo注解**
```java
// 文件：iotlaser-modules/iotlaser-admin/src/main/java/com/iotlaser/spms/wms/domain/bo/InboundItemBo.java
// 第79行修改：
@NotNull(message = "库位不能为空", groups = {AddGroup.class, EditGroup.class})
private Long locationId;

// 第94-95行修改：
@NotNull(message = "待完成数量不能为空", groups = {AddGroup.class, EditGroup.class})
@DecimalMin(value = "0.01", message = "入库数量必须大于0")
private BigDecimal quantity;
```

### **第三步：检查并补充其他Bo类注解**
1. 检查OutboundItemBo类注解配置
2. 检查TransferItemBo类注解配置
3. 检查InboundItemBatchBo类注解配置
4. 检查OutboundItemBatchBo类注解配置

## 🔧 **注解配置标准**

### **字段非空校验注解**
```java
// 对象ID字段
@NotNull(message = "XXX不能为空", groups = {AddGroup.class, EditGroup.class})
private Long xxxId;

// 字符串字段
@NotBlank(message = "XXX不能为空", groups = {AddGroup.class, EditGroup.class})
private String xxxName;
```

### **数值校验注解**
```java
// 数量字段（必须大于0）
@NotNull(message = "数量不能为空", groups = {AddGroup.class, EditGroup.class})
@DecimalMin(value = "0.01", message = "数量必须大于0")
private BigDecimal quantity;

// 金额字段（可以为0）
@DecimalMin(value = "0.00", message = "金额不能小于0")
private BigDecimal amount;
```

### **日期校验注解**
```java
// 必填日期字段
@NotNull(message = "日期不能为空", groups = {AddGroup.class, EditGroup.class})
private LocalDate date;

// 可选日期字段
private LocalDateTime optionalDate;
```

## 📝 **验证方案**

### **注解配置验证**
1. **编译验证**：确保注解配置语法正确
2. **功能验证**：测试注解校验是否生效
3. **消息验证**：确认错误消息符合业务要求

### **校验覆盖验证**
1. **对比验证**：确认Bo注解覆盖所有删除的Entity校验
2. **场景验证**：测试各种校验场景
3. **边界验证**：测试边界值校验

## 🎉 **预期效果**

### **校验完整性**
- ✅ **基础校验覆盖**：Bo注解完全覆盖删除的Entity校验
- ✅ **校验一致性**：校验逻辑与原Entity校验保持一致
- ✅ **错误消息统一**：错误消息格式统一规范

### **开发体验**
- ✅ **校验前置**：在Controller层就能拦截无效请求
- ✅ **错误提示清晰**：提供明确的字段校验错误信息
- ✅ **框架集成**：与RuoYi-Vue-Plus校验机制完美集成

### **代码质量**
- ✅ **职责分离**：校验逻辑与业务逻辑分离
- ✅ **维护性提升**：校验规则集中管理
- ✅ **一致性保证**：统一的校验标准和实现方式

## 📋 **执行时间表**

### **今天执行**
1. ✅ **TransferBo补充**：添加transferName的@NotBlank注解
2. ✅ **InboundItemBo补充**：添加locationId和quantity注解
3. 🔄 **其他Bo类检查**：检查剩余4个Bo类的注解配置

### **明天执行**
1. 补充发现的缺失注解配置
2. 编写注解校验测试用例
3. 验证校验功能正常工作

### **验收标准**
1. 所有Bo类注解配置完整
2. 注解校验功能正常
3. 校验消息符合业务要求
4. 与删除的Entity校验逻辑一致
