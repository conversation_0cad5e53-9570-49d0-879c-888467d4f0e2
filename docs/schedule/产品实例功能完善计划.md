# 产品实例(Product Instance)功能完善计划

## 📋 项目概述

**制定时间**: 2025-06-24  
**目标模块**: iotlaser-admin模块中的产品实例功能  
**核心目标**: 实现完整的产品实例生命周期管理，支持从生产开始到生产结束的全过程追溯

## 🎯 业务流程分析

### 核心业务流程
1. **生产报工(Production Report)** → **初始化产品实例(Initialize Product Instance)**
2. **生产领料(Material Requisition)** → **关联到产品实例**
3. **生产报工(Production Report)** → **更新产品实例状态**
4. **产品实例使用记录(Product Instance Usage Record)** → **记录全生命周期**

### 现有代码结构分析

#### ✅ 已实现的功能
- **基础CRUD操作**: 产品实例的增删改查
- **实体定义完整**: Instance、InstanceBo、InstanceVo、InstanceUsage等
- **状态枚举定义**: InstanceStatus枚举(DRAFT, ACTIVE, IN_USE, MAINTENANCE, RETIRED, ARCHIVED)
- **编码生成**: 自动生成产品实例编码
- **基础查询**: 支持多条件查询和分页

#### ❌ 缺失的核心功能
1. **生产报工集成**: 与ProductionReport的关联逻辑
2. **生产领料集成**: 与ProductionIssue的关联逻辑  
3. **状态流转管理**: 完整的状态机实现
4. **物料消耗记录**: InstanceUsage的自动创建
5. **生命周期追溯**: 完整的追溯链构建
6. **业务规则校验**: 状态转换和操作权限校验

## 📊 功能完善计划

### 第一阶段：核心业务流程实现 (优先级：高)

#### 1.1 生产报工集成功能
**目标**: 实现生产报工时自动创建/更新产品实例

**实现方案**:
```java
// 在ProductionReportServiceImpl中增强
@Transactional
public String startProduction(Long orderId, Long routingStepId, Long operatorId) {
    // 1. 创建产品实例
    String instanceCode = instanceService.createProductInstance(orderId, productId, routingId);
    
    // 2. 创建开工报工记录
    ProductionReport report = new ProductionReport();
    report.setInstanceId(instanceId); // 关联产品实例
    report.setReportType("START");
    // ... 其他字段设置
    
    // 3. 更新产品实例状态为IN_PROGRESS
    instanceService.updateInstanceStatus(instanceCode, InstanceStatus.ACTIVE.getValue(), routingStepId);
    
    return instanceCode;
}
```

**涉及文件**:
- `InstanceServiceImpl.java` - 增强createProductInstance方法
- `ProductionReportServiceImpl.java` - 集成产品实例创建逻辑
- `IInstanceService.java` - 添加业务方法接口

#### 1.2 生产领料集成功能  
**目标**: 生产领料时自动记录物料消耗到产品实例

**实现方案**:
```java
// 在ProductionIssueServiceImpl中增强
@Transactional  
public Boolean completeIssue(Long issueId, List<MaterialConsumption> consumptions) {
    // 1. 完成领料单
    updateIssueStatus(issueId, ProductionIssueStatus.COMPLETED);
    
    // 2. 为每个消耗的物料创建实例使用记录
    for (MaterialConsumption consumption : consumptions) {
        instanceUsageService.createMaterialConsume(
            consumption.getInstanceId(),
            consumption.getMaterialBatchId(), 
            consumption.getQuantity(),
            consumption.getOperatorId()
        );
    }
    
    return true;
}
```

**涉及文件**:
- `InstanceUsageServiceImpl.java` - 实现createMaterialConsume方法
- `ProductionIssueServiceImpl.java` - 集成物料消耗记录逻辑

#### 1.3 状态流转管理
**目标**: 实现完整的产品实例状态机

**状态流转规则**:
```
DRAFT → ACTIVE → IN_USE → MAINTENANCE → RETIRED
  ↓       ↓        ↓         ↓          ↓
ARCHIVED (任何状态都可以归档)
```

**实现方案**:
```java
// 在InstanceServiceImpl中实现
public Boolean updateInstanceStatus(String instanceCode, InstanceStatus newStatus, String reason) {
    // 1. 验证状态转换合法性
    if (!isValidStatusTransition(currentStatus, newStatus)) {
        throw new ServiceException("无效的状态转换");
    }
    
    // 2. 更新状态
    // 3. 记录状态变更日志
    // 4. 触发相关业务逻辑
}

private Boolean isValidStatusTransition(InstanceStatus from, InstanceStatus to) {
    // 实现状态转换规则校验
}
```

### 第二阶段：追溯和查询增强 (优先级：中)

#### 2.1 完整追溯链构建
**目标**: 提供产品实例的完整生命周期追溯

**实现方案**:
```java
// 在InstanceServiceImpl中实现
public Map<String, Object> getCompleteTraceability(String instanceCode) {
    // 1. 获取产品实例基本信息
    // 2. 获取所有报工记录
    // 3. 获取所有物料消耗记录  
    // 4. 获取质量检验记录
    // 5. 构建完整追溯链
}
```

#### 2.2 查询条件优化
**目标**: 优化现有查询条件，移除无意义的精确匹配

**优化内容**:
- 移除数量、金额等数值字段的精确查询
- 增加日期范围查询(生产时间、使用时间等)
- 保留有意义的精确匹配(ID、编码、状态等)

### 第三阶段：性能和扩展优化 (优先级：低)

#### 3.1 批量操作支持
**目标**: 支持批量创建和更新产品实例

#### 3.2 缓存优化  
**目标**: 对频繁查询的数据进行缓存优化

#### 3.3 异步处理
**目标**: 对耗时的追溯查询进行异步处理

## 🔧 技术实现细节

### 临时变量标注
在实现过程中，以下字段作为临时变量使用：

```java
// 在InstanceVo中添加临时变量
@TableField(exist = false)
private Integer totalReports; // 临时变量：报工记录总数

@TableField(exist = false)  
private Integer totalMaterialUsages; // 临时变量：物料使用记录总数

@TableField(exist = false)
private Double progressPercent; // 临时变量：生产进度百分比

@TableField(exist = false)
private String currentStepName; // 临时变量：当前工序名称
```

### TODO建议的新字段
以下字段建议在后续版本中添加到数据库：

```sql
-- 产品实例表增强字段
ALTER TABLE pro_instance ADD COLUMN current_step_id BIGINT COMMENT '当前工序ID';
ALTER TABLE pro_instance ADD COLUMN routing_id BIGINT COMMENT '工艺路线ID'; 
ALTER TABLE pro_instance ADD COLUMN start_time DATETIME COMMENT '开始时间';
ALTER TABLE pro_instance ADD COLUMN end_time DATETIME COMMENT '结束时间';
ALTER TABLE pro_instance ADD COLUMN operator_id BIGINT COMMENT '当前操作员ID';

-- 产品实例使用记录表增强字段  
ALTER TABLE pro_instance_usage ADD COLUMN batch_id BIGINT COMMENT '批次ID';
ALTER TABLE pro_instance_usage ADD COLUMN operator_id BIGINT COMMENT '操作员ID';
ALTER TABLE pro_instance_usage ADD COLUMN step_id BIGINT COMMENT '工序ID';
ALTER TABLE pro_instance_usage ADD COLUMN usage_type VARCHAR(50) COMMENT '使用类型';
```

## 📝 约束条件说明

1. **不新增数据库字段**: 严格遵循现有字段进行功能实现
2. **临时变量标注**: 所有临时变量需要明确标注`@TableField(exist = false)`
3. **API兼容性**: 保持现有API的向后兼容性
4. **事务一致性**: 所有涉及多表操作的方法必须使用`@Transactional`
5. **异常处理**: 统一使用`ServiceException`进行业务异常处理

## 🎯 预期成果

完成本计划后，产品实例功能将具备：

1. ✅ **完整的生命周期管理**: 从创建到归档的全流程支持
2. ✅ **自动化集成**: 与生产报工、生产领料的无缝集成  
3. ✅ **完整的追溯能力**: 支持物料、工序、质量的全链路追溯
4. ✅ **灵活的状态管理**: 支持复杂的状态流转和业务规则
5. ✅ **高性能查询**: 优化的查询条件和缓存机制

## 📅 实施时间表

- **第一阶段**: 2-3周 (核心业务流程实现)
- **第二阶段**: 1-2周 (追溯和查询增强)  
- **第三阶段**: 1周 (性能和扩展优化)

**总计**: 4-6周完成全部功能

---

## 📋 单元测试计划

### 测试框架和规范

**测试框架**: JUnit 5 + Mockito + Spring Boot Test
**命名规范**: `should_ExpectedBehavior_When_StateUnderTest`
**覆盖率目标**: Service层 ≥ 85%，Controller层 ≥ 80%

### 测试计划详情

#### 1. InstanceServiceImpl 测试计划

**测试类**: `InstanceServiceImplTest`

**核心测试用例**:

```java
@ExtendWith(MockitoExtension.class)
@DisplayName("产品实例服务测试")
class InstanceServiceImplTest {

    @Test
    @DisplayName("应该成功创建产品实例当传入有效参数时")
    void should_CreateInstanceSuccessfully_When_ValidParametersProvided() {
        // Given: 准备有效的输入参数
        // When: 调用insertByBo方法
        // Then: 验证实例创建成功，编码自动生成，状态为DRAFT
    }

    @Test
    @DisplayName("应该抛出异常当实例编码重复时")
    void should_ThrowException_When_InstanceCodeDuplicated() {
        // Given: 准备重复的实例编码
        // When: 调用insertByBo方法
        // Then: 验证抛出ServiceException
    }

    @Test
    @DisplayName("应该成功更新实例状态当状态转换合法时")
    void should_UpdateStatusSuccessfully_When_ValidStatusTransition() {
        // Given: 准备合法的状态转换
        // When: 调用updateInstanceStatus方法
        // Then: 验证状态更新成功
    }

    @Test
    @DisplayName("应该拒绝删除当实例状态不是草稿时")
    void should_RejectDeletion_When_InstanceStatusNotDraft() {
        // Given: 准备非草稿状态的实例
        // When: 调用deleteWithValidByIds方法
        // Then: 验证抛出ServiceException
    }

    @ParameterizedTest
    @ValueSource(strings = {"DRAFT", "ACTIVE", "IN_USE", "MAINTENANCE", "RETIRED", "ARCHIVED"})
    @DisplayName("应该正确处理所有状态类型")
    void should_HandleAllStatusTypes_When_DifferentStatusProvided(String status) {
        // 参数化测试所有状态类型
    }
}
```

#### 2. InstanceUsageServiceImpl 测试计划

**测试类**: `InstanceUsageServiceImplTest`

**核心测试用例**:

```java
@ExtendWith(MockitoExtension.class)
@DisplayName("产品实例使用记录服务测试")
class InstanceUsageServiceImplTest {

    @Test
    @DisplayName("应该成功创建物料消耗记录当参数有效时")
    void should_CreateMaterialConsumeSuccessfully_When_ValidParameters() {
        // Given: 准备有效的物料消耗参数
        // When: 调用createMaterialConsume方法
        // Then: 验证消耗记录创建成功
    }

    @Test
    @DisplayName("应该返回正确的追溯信息当实例ID存在时")
    void should_ReturnCorrectTraceability_When_InstanceIdExists() {
        // Given: 准备存在的实例ID
        // When: 调用queryByInstanceId方法
        // Then: 验证返回正确的追溯信息
    }

    @Test
    @DisplayName("应该按时间排序返回使用记录")
    void should_ReturnUsageRecordsOrderedByTime_When_QueryingByInstanceId() {
        // Given: 准备多条使用记录
        // When: 调用queryByInstanceId方法
        // Then: 验证记录按使用时间排序
    }
}
```

#### 3. ProductionReportServiceImpl 集成测试

**测试类**: `ProductionReportServiceImplIntegrationTest`

**核心测试用例**:

```java
@SpringBootTest
@Transactional
@DisplayName("生产报工服务集成测试")
class ProductionReportServiceImplIntegrationTest {

    @Test
    @DisplayName("应该创建产品实例当开工报工时")
    void should_CreateProductInstance_When_StartProduction() {
        // Given: 准备生产订单和工艺步骤
        // When: 调用startProduction方法
        // Then: 验证产品实例被创建，状态正确
    }

    @Test
    @DisplayName("应该记录物料消耗当物料消耗报工时")
    void should_RecordMaterialConsume_When_ReportMaterialConsume() {
        // Given: 准备产品实例和物料批次
        // When: 调用reportMaterialConsume方法
        // Then: 验证物料消耗记录被创建
    }

    @Test
    @DisplayName("应该更新实例状态当完工报工时")
    void should_UpdateInstanceStatus_When_CompleteProduction() {
        // Given: 准备进行中的产品实例
        // When: 调用completeProduction方法
        // Then: 验证实例状态更新为完成
    }
}
```

#### 4. Controller层测试计划

**测试类**: `InstanceControllerTest`

```java
@WebMvcTest(InstanceController.class)
@DisplayName("产品实例控制器测试")
class InstanceControllerTest {

    @Test
    @DisplayName("应该返回分页数据当查询产品实例列表时")
    void should_ReturnPagedData_When_QueryInstanceList() {
        // Given: 准备查询参数
        // When: 发送GET请求到/spms/pro/instance/list
        // Then: 验证返回正确的分页数据
    }

    @Test
    @DisplayName("应该返回实例详情当根据ID查询时")
    void should_ReturnInstanceDetail_When_QueryById() {
        // Given: 准备存在的实例ID
        // When: 发送GET请求到/spms/pro/instance/{id}
        // Then: 验证返回正确的实例详情
    }

    @Test
    @DisplayName("应该创建成功当提交有效的实例数据时")
    void should_CreateSuccessfully_When_ValidInstanceDataSubmitted() {
        // Given: 准备有效的实例数据
        // When: 发送POST请求到/spms/pro/instance
        // Then: 验证创建成功，返回正确响应
    }
}
```

### 测试数据准备

#### 测试数据工厂类

```java
@Component
public class InstanceTestDataFactory {

    public static InstanceBo createValidInstanceBo() {
        InstanceBo bo = new InstanceBo();
        bo.setProductId(1L);
        bo.setProductCode("PROD001");
        bo.setProductName("测试产品");
        bo.setProductionOrderId(1L);
        bo.setProductionOrderCode("PO001");
        return bo;
    }

    public static Instance createValidInstance() {
        Instance instance = new Instance();
        instance.setInstanceId(1L);
        instance.setInstanceCode("INST001");
        instance.setProductId(1L);
        instance.setInstanceStatus(InstanceStatus.DRAFT.getValue());
        return instance;
    }
}
```

### 性能测试计划

#### 批量操作性能测试

```java
@Test
@DisplayName("批量创建性能测试")
void should_HandleBatchCreation_When_LargeDataSet() {
    // Given: 准备1000条实例数据
    List<InstanceBo> instances = createBatchInstances(1000);

    // When: 执行批量创建
    long startTime = System.currentTimeMillis();
    batchCreateInstances(instances);
    long endTime = System.currentTimeMillis();

    // Then: 验证性能指标
    assertThat(endTime - startTime).isLessThan(5000); // 5秒内完成
}
```

### API兼容性测试

#### 向后兼容性验证

```java
@Test
@DisplayName("API向后兼容性测试")
void should_MaintainBackwardCompatibility_When_ApiCalled() {
    // 验证现有API接口的响应格式保持不变
    // 验证查询参数的处理逻辑保持一致
    // 验证返回数据结构的兼容性
}
```

### 测试执行策略

1. **单元测试**: 每次代码提交时自动执行
2. **集成测试**: 每日构建时执行
3. **性能测试**: 每周执行一次
4. **兼容性测试**: 发布前执行

### 测试覆盖率要求

- **Service层**: ≥ 85%
- **Controller层**: ≥ 80%
- **关键业务方法**: 100%
- **异常处理**: 100%

### 测试报告

测试完成后生成详细报告，包括：
- 测试用例执行结果
- 代码覆盖率统计
- 性能测试指标
- API兼容性验证结果

---

## 🚀 优化建议

### 1. 架构设计优化建议

#### 1.1 引入设计模式优化

**状态机模式**:
```java
// TODO: 建议引入状态机模式管理产品实例状态
public interface InstanceStateMachine {
    boolean canTransition(InstanceStatus from, InstanceStatus to);
    void transition(String instanceCode, InstanceStatus to, String reason);
    List<InstanceStatus> getAvailableTransitions(InstanceStatus current);
}

@Component
public class InstanceStateMachineImpl implements InstanceStateMachine {
    // 实现状态转换规则和业务逻辑
}
```

**策略模式**:
```java
// TODO: 建议使用策略模式处理不同类型的报工
public interface ProductionReportStrategy {
    void handleReport(ProductionReportContext context);
}

@Component("startReportStrategy")
public class StartReportStrategy implements ProductionReportStrategy {
    // 处理开工报工逻辑
}

@Component("completeReportStrategy")
public class CompleteReportStrategy implements ProductionReportStrategy {
    // 处理完工报工逻辑
}
```

#### 1.2 事件驱动架构

**建议引入Spring Events**:
```java
// TODO: 建议使用事件驱动解耦业务逻辑
@Component
public class InstanceEventPublisher {

    @EventListener
    public void handleInstanceCreated(InstanceCreatedEvent event) {
        // 处理实例创建后的业务逻辑
    }

    @EventListener
    public void handleInstanceStatusChanged(InstanceStatusChangedEvent event) {
        // 处理状态变更后的业务逻辑
    }
}
```

### 2. 数据库设计优化建议

#### 2.1 建议新增的核心字段

**产品实例表(pro_instance)增强**:
```sql
-- TODO: 建议在后续版本中添加以下字段以完善功能

-- 工艺流程相关
ALTER TABLE pro_instance ADD COLUMN routing_id BIGINT COMMENT '工艺路线ID';
ALTER TABLE pro_instance ADD COLUMN current_step_id BIGINT COMMENT '当前工序ID';
ALTER TABLE pro_instance ADD COLUMN next_step_id BIGINT COMMENT '下一工序ID';

-- 时间管理
ALTER TABLE pro_instance ADD COLUMN planned_start_time DATETIME COMMENT '计划开始时间';
ALTER TABLE pro_instance ADD COLUMN planned_end_time DATETIME COMMENT '计划结束时间';
ALTER TABLE pro_instance ADD COLUMN actual_start_time DATETIME COMMENT '实际开始时间';
ALTER TABLE pro_instance ADD COLUMN actual_end_time DATETIME COMMENT '实际结束时间';

-- 操作人员
ALTER TABLE pro_instance ADD COLUMN current_operator_id BIGINT COMMENT '当前操作员ID';
ALTER TABLE pro_instance ADD COLUMN current_operator_name VARCHAR(100) COMMENT '当前操作员姓名';

-- 质量管理
ALTER TABLE pro_instance ADD COLUMN quality_status VARCHAR(50) COMMENT '质量状态';
ALTER TABLE pro_instance ADD COLUMN quality_level VARCHAR(50) COMMENT '质量等级';

-- 成本核算
ALTER TABLE pro_instance ADD COLUMN material_cost DECIMAL(15,4) COMMENT '物料成本';
ALTER TABLE pro_instance ADD COLUMN labor_cost DECIMAL(15,4) COMMENT '人工成本';
ALTER TABLE pro_instance ADD COLUMN overhead_cost DECIMAL(15,4) COMMENT '制造费用';
ALTER TABLE pro_instance ADD COLUMN total_cost DECIMAL(15,4) COMMENT '总成本';

-- 追溯信息
ALTER TABLE pro_instance ADD COLUMN parent_instance_id BIGINT COMMENT '父实例ID(用于返工)';
ALTER TABLE pro_instance ADD COLUMN trace_code VARCHAR(200) COMMENT '追溯码';

-- 扩展信息
ALTER TABLE pro_instance ADD COLUMN priority_level INT COMMENT '优先级(1-5)';
ALTER TABLE pro_instance ADD COLUMN batch_number VARCHAR(100) COMMENT '批次号';
ALTER TABLE pro_instance ADD COLUMN serial_number VARCHAR(100) COMMENT '序列号';
```

**产品实例使用记录表(pro_instance_usage)增强**:
```sql
-- TODO: 建议在后续版本中添加以下字段

-- 批次追溯
ALTER TABLE pro_instance_usage ADD COLUMN batch_id BIGINT COMMENT '库存批次ID';
ALTER TABLE pro_instance_usage ADD COLUMN batch_code VARCHAR(100) COMMENT '批次编码';

-- 工序信息
ALTER TABLE pro_instance_usage ADD COLUMN step_id BIGINT COMMENT '工序ID';
ALTER TABLE pro_instance_usage ADD COLUMN step_code VARCHAR(100) COMMENT '工序编码';
ALTER TABLE pro_instance_usage ADD COLUMN step_name VARCHAR(200) COMMENT '工序名称';

-- 操作信息
ALTER TABLE pro_instance_usage ADD COLUMN operator_id BIGINT COMMENT '操作员ID';
ALTER TABLE pro_instance_usage ADD COLUMN operator_name VARCHAR(100) COMMENT '操作员姓名';
ALTER TABLE pro_instance_usage ADD COLUMN operation_type VARCHAR(50) COMMENT '操作类型(CONSUME/RETURN)';

-- 质量信息
ALTER TABLE pro_instance_usage ADD COLUMN quality_status VARCHAR(50) COMMENT '质量状态';
ALTER TABLE pro_instance_usage ADD COLUMN inspection_result VARCHAR(50) COMMENT '检验结果';

-- 成本信息
ALTER TABLE pro_instance_usage ADD COLUMN unit_cost DECIMAL(15,4) COMMENT '单位成本';
ALTER TABLE pro_instance_usage ADD COLUMN total_cost DECIMAL(15,4) COMMENT '总成本';

-- 位置信息
ALTER TABLE pro_instance_usage ADD COLUMN warehouse_id BIGINT COMMENT '仓库ID';
ALTER TABLE pro_instance_usage ADD COLUMN location_id BIGINT COMMENT '库位ID';
```

#### 2.2 建议新增的关联表

**产品实例状态变更日志表**:
```sql
-- TODO: 建议新增状态变更日志表
CREATE TABLE pro_instance_status_log (
    log_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    instance_id BIGINT NOT NULL COMMENT '产品实例ID',
    from_status VARCHAR(50) COMMENT '原状态',
    to_status VARCHAR(50) NOT NULL COMMENT '新状态',
    change_reason VARCHAR(500) COMMENT '变更原因',
    operator_id BIGINT COMMENT '操作员ID',
    operator_name VARCHAR(100) COMMENT '操作员姓名',
    change_time DATETIME NOT NULL COMMENT '变更时间',
    remark TEXT COMMENT '备注',
    tenant_id VARCHAR(20) NOT NULL COMMENT '租户ID',
    create_time DATETIME NOT NULL,
    create_by BIGINT,
    update_time DATETIME,
    update_by BIGINT
);
```

**产品实例工序记录表**:
```sql
-- TODO: 建议新增工序记录表
CREATE TABLE pro_instance_step_record (
    record_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    instance_id BIGINT NOT NULL COMMENT '产品实例ID',
    step_id BIGINT NOT NULL COMMENT '工序ID',
    step_code VARCHAR(100) COMMENT '工序编码',
    step_name VARCHAR(200) COMMENT '工序名称',
    step_status VARCHAR(50) COMMENT '工序状态',
    planned_duration INT COMMENT '计划工时(分钟)',
    actual_duration INT COMMENT '实际工时(分钟)',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    operator_id BIGINT COMMENT '操作员ID',
    operator_name VARCHAR(100) COMMENT '操作员姓名',
    quality_result VARCHAR(50) COMMENT '质量结果',
    remark TEXT COMMENT '备注',
    tenant_id VARCHAR(20) NOT NULL COMMENT '租户ID',
    create_time DATETIME NOT NULL,
    create_by BIGINT,
    update_time DATETIME,
    update_by BIGINT
);
```

### 3. 性能优化建议

#### 3.1 查询优化

**建议添加索引**:
```sql
-- TODO: 建议添加以下索引提升查询性能

-- 产品实例表索引
CREATE INDEX idx_instance_code ON pro_instance(instance_code);
CREATE INDEX idx_instance_status ON pro_instance(instance_status);
CREATE INDEX idx_instance_production_order ON pro_instance(production_order_id);
CREATE INDEX idx_instance_product ON pro_instance(product_id);
CREATE INDEX idx_instance_time ON pro_instance(production_time);

-- 使用记录表索引
CREATE INDEX idx_usage_instance ON pro_instance_usage(instance_id);
CREATE INDEX idx_usage_time ON pro_instance_usage(usage_time);
CREATE INDEX idx_usage_product ON pro_instance_usage(product_id);
```

**分页查询优化**:
```java
// TODO: 建议使用游标分页替代传统分页
public class InstanceCursorPageQuery {
    private String lastInstanceCode;
    private Integer pageSize = 20;
    private String sortDirection = "ASC";
}
```

#### 3.2 缓存策略

**Redis缓存建议**:
```java
// TODO: 建议对热点数据进行缓存
@Service
public class InstanceCacheService {

    @Cacheable(value = "instance", key = "#instanceCode")
    public InstanceVo getByInstanceCode(String instanceCode) {
        // 缓存产品实例基本信息
    }

    @Cacheable(value = "instance:progress", key = "#instanceCode")
    public Map<String, Object> getProductionProgress(String instanceCode) {
        // 缓存生产进度信息
    }
}
```

### 4. 业务逻辑优化建议

#### 4.1 异步处理

**建议异步处理耗时操作**:
```java
// TODO: 建议使用异步处理提升响应速度
@Service
public class InstanceAsyncService {

    @Async("instanceTaskExecutor")
    public CompletableFuture<Map<String, Object>> buildTraceabilityAsync(String instanceCode) {
        // 异步构建追溯信息
    }

    @Async("instanceTaskExecutor")
    public CompletableFuture<Void> updateCostAsync(String instanceCode) {
        // 异步更新成本信息
    }
}
```

#### 4.2 批量操作优化

**建议支持批量操作**:
```java
// TODO: 建议实现批量操作接口
public interface IBatchInstanceService {

    BatchResult batchCreateInstances(List<InstanceBo> instances);
    BatchResult batchUpdateStatus(List<InstanceStatusUpdate> updates);
    BatchResult batchDelete(List<Long> instanceIds);
}
```

### 5. 监控和运维优化建议

#### 5.1 业务监控

**建议添加业务指标监控**:
```java
// TODO: 建议添加业务监控指标
@Component
public class InstanceMetrics {

    private final MeterRegistry meterRegistry;

    public void recordInstanceCreated(String productType) {
        Counter.builder("instance.created")
            .tag("product.type", productType)
            .register(meterRegistry)
            .increment();
    }

    public void recordStatusTransition(String fromStatus, String toStatus) {
        Counter.builder("instance.status.transition")
            .tag("from", fromStatus)
            .tag("to", toStatus)
            .register(meterRegistry)
            .increment();
    }
}
```

#### 5.2 日志优化

**建议结构化日志**:
```java
// TODO: 建议使用结构化日志便于分析
@Slf4j
public class InstanceServiceImpl {

    public void logInstanceOperation(String operation, String instanceCode, Object... params) {
        log.info("Instance operation: operation={}, instanceCode={}, params={}",
            operation, instanceCode, params);
    }
}
```

### 6. 安全性优化建议

#### 6.1 数据权限

**建议实现数据权限控制**:
```java
// TODO: 建议实现基于角色的数据权限
@Component
public class InstanceDataPermission {

    public boolean canAccessInstance(String instanceCode, Long userId) {
        // 检查用户是否有权限访问指定实例
    }

    public List<String> getAccessibleInstances(Long userId) {
        // 获取用户可访问的实例列表
    }
}
```

#### 6.2 操作审计

**建议添加操作审计**:
```java
// TODO: 建议添加操作审计日志
@Aspect
@Component
public class InstanceAuditAspect {

    @Around("@annotation(Auditable)")
    public Object auditInstanceOperation(ProceedingJoinPoint joinPoint) {
        // 记录操作审计信息
    }
}
```

### 7. 扩展性优化建议

#### 7.1 插件化架构

**建议支持插件扩展**:
```java
// TODO: 建议实现插件化架构支持业务扩展
public interface InstancePlugin {
    String getName();
    void onInstanceCreated(InstanceCreatedEvent event);
    void onInstanceStatusChanged(InstanceStatusChangedEvent event);
    void onInstanceCompleted(InstanceCompletedEvent event);
}
```

#### 7.2 配置化业务规则

**建议配置化业务规则**:
```java
// TODO: 建议将业务规则配置化
@ConfigurationProperties(prefix = "instance.business.rules")
public class InstanceBusinessRules {
    private Map<String, List<String>> statusTransitions;
    private Map<String, Integer> timeoutSettings;
    private Map<String, String> validationRules;
}
```

这些优化建议将在功能完善的基础上，进一步提升系统的性能、可维护性和扩展性。建议按优先级分阶段实施，确保系统稳定性的同时逐步完善功能。
