# 第四阶段：详细工作计划

## 计划制定时间
**制定时间**: 2025-06-24  
**计划范围**: 生产管理流程检查和完善的具体实施计划  
**工作原则**: 每个任务约20分钟开发工作量，按优先级排序，不新增数据库字段

## 总体任务概览

### 任务分类统计
- **高优先级修复任务**: 6个子任务 (约120分钟)
- **中优先级完善任务**: 6个子任务 (约120分钟)  
- **低优先级优化任务**: 6个子任务 (约120分钟)
- **总计**: 18个具体任务 (约360分钟 = 6小时)

## 高优先级修复任务 (影响核心业务流程)

### 任务1: 修复ProductionIssueBo字段注释错误
**预估时间**: 20分钟  
**优先级**: 最高  
**文件位置**: `ProductionIssueBo.java`  
**具体内容**:
- 修正第29-32行的字段注释，将"退货单"改为"领料单"
- 确保所有字段注释与实际业务含义一致
- 验证修改后的注释准确性

**约束条件**: 仅修改注释，不涉及数据库字段变更

### 任务2: 完善销售订单到生产订单转换逻辑
**预估时间**: 20分钟  
**优先级**: 最高  
**文件位置**: `ProductionOrderServiceImpl.java`  
**具体内容**:
- 实现`createFromSaleOrder`方法中的TODO部分
- 添加销售订单服务注入：`@Autowired private ISaleOrderService saleOrderService;`
- 实现产品信息映射：从销售订单明细获取产品信息
- 实现数量映射：从销售订单明细汇总生产数量
- 设置BOM信息：根据产品ID获取默认BOM

**约束条件**: 使用现有字段，不新增数据库字段

### 任务3: 实现BOM展开和物料需求计算
**预估时间**: 20分钟  
**优先级**: 最高  
**文件位置**: `ProductionIssueServiceImpl.java`  
**具体内容**:
- 实现`createIssueItemsFromBOM`方法
- 根据生产订单的产品ID获取BOM信息
- 计算物料需求：BOM用量 × 生产数量
- 创建领料明细记录
- 设置默认库位信息

**约束条件**: 集成现有BOM服务，不新增数据库字段

### 任务4: 完善生产订单状态同步机制
**预估时间**: 20分钟  
**优先级**: 高  
**文件位置**: `ProductionOrderServiceImpl.java`, `SaleOrderServiceImpl.java`  
**具体内容**:
- 在创建生产订单成功后，更新销售订单状态为`PENDING_PRODUCTION`
- 在生产订单开始生产时，更新销售订单状态为`IN_PRODUCTION`
- 添加状态同步的异常处理机制
- 确保状态变更的事务一致性

**约束条件**: 使用现有状态枚举，不新增状态值

### 任务5: 添加完工数量超产控制
**预估时间**: 20分钟  
**优先级**: 高  
**文件位置**: `ProductionOrderServiceImpl.java`  
**具体内容**:
- 在`finishProduction`方法中添加超产检查
- 验证：`newFinishQuantity <= order.getQuantity()`
- 添加超产警告日志和异常处理
- 支持配置允许的超产比例（使用临时变量）

**约束条件**: 使用现有字段进行计算，不新增数据库字段

### 任务6: 完善生产完工入库状态同步
**预估时间**: 20分钟  
**优先级**: 高  
**文件位置**: `ProductionInboundServiceImpl.java`  
**具体内容**:
- 在完工入库完成后，自动更新生产订单状态
- 当生产订单全部完工时，更新销售订单状态为`READY_TO_SHIP`
- 添加状态同步失败的回滚机制
- 记录状态变更日志

**约束条件**: 使用现有状态枚举和字段

## 中优先级完善任务 (完善业务逻辑完整性)

### 任务7: 实现库存预留机制
**预估时间**: 20分钟  
**优先级**: 中  
**文件位置**: `ProductionIssueServiceImpl.java`  
**具体内容**:
- 在创建领料单时预留库存
- 使用现有的`finishQuantity`字段记录预留数量
- 实现预留库存的释放机制
- 添加预留失败的处理逻辑

**约束条件**: 利用现有字段实现，不新增数据库字段

### 任务8: 完善数量核对逻辑
**预估时间**: 20分钟  
**优先级**: 中  
**文件位置**: `ProductionInboundServiceImpl.java`  
**具体内容**:
- 在完工入库时验证入库数量与生产订单数量的一致性
- 添加数量差异的警告和处理机制
- 实现数量核对的详细日志记录
- 支持合理范围内的数量差异

**约束条件**: 使用现有字段进行核对

### 任务9: 实现生产进度跟踪计算
**预估时间**: 20分钟  
**优先级**: 中  
**文件位置**: `ProductionReportServiceImpl.java`  
**具体内容**:
- 基于生产报工数据计算生产进度
- 使用临时变量存储进度计算结果
- 实现进度的自动更新机制
- 添加进度异常的检测和处理

**约束条件**: 使用临时变量存储计算结果，不持久化到数据库

### 任务10: 完善生产退料库存回退逻辑
**预估时间**: 20分钟  
**优先级**: 中  
**文件位置**: `ProductionReturnServiceImpl.java`  
**具体内容**:
- 实现退料完成后的库存增加逻辑
- 处理退料批次信息的正确回退
- 实现退料成本的调整计算
- 添加退料异常的处理机制

**约束条件**: 使用现有字段和批次管理机制

### 任务11: 实现基本成本计算逻辑
**预估时间**: 20分钟  
**优先级**: 中  
**文件位置**: `ProductionIssueServiceImpl.java`  
**具体内容**:
- 实现`getProductCostPrice`方法
- 从库存批次中获取加权平均成本价
- 支持不同成本计算方法的配置
- 添加成本计算异常的处理

**约束条件**: 使用现有的库存批次成本信息

### 任务12: 完善安全库存检查
**预估时间**: 20分钟  
**优先级**: 中  
**文件位置**: `InventoryServiceImpl.java`  
**具体内容**:
- 在库存可用性检查中添加安全库存验证
- 使用产品主数据中的安全库存字段
- 实现安全库存预警机制
- 添加安全库存不足的处理建议

**约束条件**: 使用现有的产品安全库存字段

## 低优先级优化任务 (系统优化和增强)

### 任务13-18: 系统优化任务
每个任务预估20分钟，包括：
- 统一时间字段类型为LocalDateTime
- 完善异常处理机制和错误信息
- 实现工艺流程控制基础框架
- 优化数据校验逻辑和业务规则
- 完善关键操作的日志记录
- 添加数据权限验证基础框架

## 实施计划

### 第一周 (高优先级任务)
**目标**: 修复影响核心业务流程的关键问题
- 周一: 任务1-2 (注释修复 + 订单转换逻辑)
- 周二: 任务3-4 (BOM展开 + 状态同步)  
- 周三: 任务5-6 (超产控制 + 入库状态同步)

### 第二周 (中优先级任务)
**目标**: 完善业务逻辑完整性
- 周一: 任务7-8 (库存预留 + 数量核对)
- 周二: 任务9-10 (进度跟踪 + 退料逻辑)
- 周三: 任务11-12 (成本计算 + 安全库存)

### 第三周 (低优先级任务)
**目标**: 系统优化和增强
- 周一-周三: 任务13-18 (系统优化任务)

## 质量保证

### 测试要求
- 每个任务完成后进行单元测试
- 关键业务流程进行集成测试
- 数据一致性验证测试

### 文档要求
- 每个任务完成后更新相关文档
- 记录修改内容和影响范围
- 更新API文档和业务流程说明

### 代码审查
- 关键修改进行代码审查
- 确保符合编码规范
- 验证业务逻辑正确性

---
**计划制定完成时间**: 2025-06-24  
**下一阶段**: 逐步实施
