# ERP财务系统数据链路梳理报告

## 📋 报告概述

**梳理时间**: 2025-06-24  
**梳理范围**: 销售订单→订单明细→出库单→应收发票→财务对账完整数据流转链路  
**目标**: 识别关键数据字段、传递规则、依赖关系和约束条件  

## 🔗 完整数据链路图

```mermaid
graph TD
    A[销售订单 SaleOrder] --> B[订单明细 SaleOrderItem]
    B --> C[出库单 OutboundOrder]
    C --> D[出库明细 OutboundOrderItem]
    D --> E[应收发票 FinArReceivable]
    E --> F[收款单 FinArReceiptOrder]
    F --> G[核销关联 FinArReceiptReceivableLink]
    G --> H[财务对账 FinancialReconciliation]
    
    A --> I[金额汇总计算]
    I --> A
    
    B --> J[数量状态更新]
    J --> B
    
    E --> K[应收发票明细 TODO]
    
    style K fill:#ffcccc
    style I fill:#ffffcc
```

## 📊 数据实体结构分析

### 1. 销售订单主表 (SaleOrder)

#### 核心字段
| 字段名 | 类型 | 说明 | 数据流转作用 |
|--------|------|------|-------------|
| `orderId` | Long | 订单ID | 全链路主键关联 |
| `orderCode` | String | 订单编号 | 业务追溯标识 |
| `orderName` | String | 订单名称 | 业务描述 |
| `customerId` | Long | 客户ID | 客户关联主键 |
| `customerCode` | String | 客户编码 | 客户业务标识 |
| `customerName` | String | 客户名称 | 客户冗余信息 |
| `orderDate` | LocalDate | 下单日期 | 业务时间基准 |
| `orderStatus` | SaleOrderStatus | 订单状态 | 业务流程控制 |

#### 汇总字段 (临时变量)
| 字段名 | 类型 | 说明 | TODO状态 |
|--------|------|------|---------|
| `totalQuantity` | BigDecimal | 总数量 | 需要数据库字段 |
| `totalAmount` | BigDecimal | 总金额(含税) | 需要数据库字段 |
| `totalAmountExclusiveTax` | BigDecimal | 总金额(不含税) | 需要数据库字段 |
| `totalTaxAmount` | BigDecimal | 总税额 | 需要数据库字段 |

### 2. 销售订单明细表 (SaleOrderItem)

#### 核心字段
| 字段名 | 类型 | 说明 | 数据流转作用 |
|--------|------|------|-------------|
| `itemId` | Long | 明细ID | 明细主键 |
| `orderId` | Long | 订单ID | 关联主表 |
| `productId` | Long | 产品ID | 产品关联 |
| `productCode` | String | 产品编码 | 产品标识 |
| `productName` | String | 产品名称 | 产品描述 |
| `quantity` | BigDecimal | 订单数量 | 基础数量 |
| `price` | BigDecimal | 含税单价 | 价格基准 |
| `amount` | BigDecimal | 含税金额 | 金额基准 |

#### 业务状态字段
| 字段名 | 类型 | 说明 | 业务意义 |
|--------|------|------|---------|
| `finishQuantity` | BigDecimal | 已完成数量 | 生产进度 |
| `shippedQuantity` | BigDecimal | 已发货数量 | 出库依据 |
| `invoicedQuantity` | BigDecimal | 已开票数量 | 开票依据 |
| `invoicedAmount` | BigDecimal | 已开票金额 | 开票金额 |

### 3. 出库单 (OutboundOrder) - 推断结构

#### 预期核心字段
| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|---------|
| `outboundId` | Long | 出库单ID | 系统生成 |
| `outboundCode` | String | 出库单编号 | 系统生成 |
| `sourceOrderId` | Long | 来源订单ID | SaleOrder.orderId |
| `sourceOrderCode` | String | 来源订单编号 | SaleOrder.orderCode |
| `customerId` | Long | 客户ID | SaleOrder.customerId |
| `outboundDate` | LocalDate | 出库日期 | 业务操作时间 |
| `totalQuantity` | BigDecimal | 出库总数量 | 明细汇总 |
| `totalAmount` | BigDecimal | 出库总金额 | 明细汇总 |

### 4. 应收发票 (FinArReceivable)

#### 核心字段
| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|---------|
| `receivableId` | Long | 应收ID | 系统生成 |
| `receivableCode` | String | 应收编号 | 系统生成 |
| `sourceId` | Long | 来源ID | SaleOrder.orderId |
| `sourceCode` | String | 来源编号 | SaleOrder.orderCode |
| `sourceType` | String | 来源类型 | "SALE_ORDER" |
| `customerId` | Long | 客户ID | SaleOrder.customerId |
| `amount` | BigDecimal | 含税金额 | 订单明细汇总 |
| `amountExclusiveTax` | BigDecimal | 不含税金额 | 订单明细汇总 |
| `taxAmount` | BigDecimal | 税额 | 订单明细汇总 |

### 5. 收款单 (FinArReceiptOrder)

#### 核心字段
| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|---------|
| `receiptId` | Long | 收款ID | 系统生成 |
| `receiptCode` | String | 收款编号 | 系统生成 |
| `customerId` | Long | 客户ID | 业务关联 |
| `paymentAmount` | BigDecimal | 收款金额 | 业务输入 |
| `appliedAmount` | BigDecimal | 已核销金额 | 核销计算 |
| `unappliedAmount` | BigDecimal | 未核销金额 | 核销计算 |

#### 来源关联字段 (临时变量)
| 字段名 | 类型 | 说明 | TODO状态 |
|--------|------|------|---------|
| `sourceOrderId` | Long | 来源订单ID | 需要数据库字段 |
| `sourceOrderCode` | String | 来源订单编号 | 需要数据库字段 |
| `sourceOrderType` | String | 来源订单类型 | 需要数据库字段 |

### 6. 核销关联 (FinArReceiptReceivableLink)

#### 核心字段
| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|---------|
| `linkId` | Long | 关联ID | 系统生成 |
| `receiptId` | Long | 收款单ID | FinArReceiptOrder.receiptId |
| `receivableId` | Long | 应收单ID | FinArReceivable.receivableId |
| `appliedAmount` | BigDecimal | 核销金额 | 业务计算 |
| `cancellationDate` | LocalDate | 核销日期 | 业务操作时间 |

#### 经办人字段 (临时变量)
| 字段名 | 类型 | 说明 | TODO状态 |
|--------|------|------|---------|
| `handlerId` | Long | 经办人ID | 需要数据库字段 |
| `handlerName` | String | 经办人姓名 | 需要数据库字段 |
| `handleTime` | LocalDateTime | 经办时间 | 需要数据库字段 |

## 🔄 数据传递规则分析

### 规则1: 销售订单 → 订单明细
**传递方向**: 主表 → 明细表  
**传递内容**: 
- 订单基础信息 (orderId, orderCode, customerId等)
- 客户信息冗余 (customerCode, customerName)

**约束条件**:
- 明细表必须关联有效的主表记录
- 客户信息必须保持一致
- 明细汇总金额应与主表金额一致 (TODO: 主表字段缺失)

### 规则2: 订单明细 → 出库单明细
**传递方向**: 销售明细 → 出库明细  
**传递内容**:
- 产品信息 (productId, productCode, productName)
- 数量信息 (quantity → outboundQuantity)
- 金额信息 (amount → outboundAmount)

**约束条件**:
- 出库数量不能超过订单数量
- 累计出库数量更新到 shippedQuantity
- 出库金额按比例计算

### 规则3: 出库单 → 应收发票
**传递方向**: 出库单 → 应收发票  
**传递内容**:
- 客户信息 (customerId, customerCode, customerName)
- 来源信息 (sourceId, sourceCode, sourceType)
- 金额信息 (出库金额 → 应收金额)

**约束条件**:
- 开票金额不能超过出库金额
- 开票依据必须是已出库商品
- 累计开票金额更新到明细表

### 规则4: 应收发票 ↔ 收款单 (核销)
**传递方向**: 双向关联  
**传递内容**:
- 核销金额分配
- 状态同步更新
- 余额重新计算

**约束条件**:
- 核销金额不能超过任一方的可用金额
- 核销后状态必须同步更新
- 支持部分核销和多次核销

## ⚠️ 数据传递问题识别

### 问题1: 主表金额汇总缺失
**问题描述**: SaleOrder主表缺少金额汇总字段  
**影响范围**: 金额一致性校验、对账功能、报表统计  
**解决方案**: 添加汇总字段并实现自动计算  
**优先级**: P0 - 紧急  

### 问题2: 出库单数据链路缺失
**问题描述**: 缺少出库单相关实体和业务逻辑  
**影响范围**: 完整业务流程、开票依据、数量状态更新  
**解决方案**: 设计出库单实体和业务流程  
**优先级**: P1 - 重要  

### 问题3: 应收发票明细缺失
**问题描述**: 应收发票缺少明细表，无法精细化对账  
**影响范围**: 明细级对账、产品维度分析  
**解决方案**: 创建应收发票明细表  
**优先级**: P1 - 重要  

### 问题4: 来源关联字段缺失
**问题描述**: 收款单缺少与订单的直接关联  
**影响范围**: 业务追溯、数据完整性  
**解决方案**: 添加来源关联字段  
**优先级**: P0 - 紧急  

### 问题5: 经办人信息缺失
**问题描述**: 核销记录缺少经办人信息  
**影响范围**: 审计追踪、责任追溯  
**解决方案**: 添加经办人相关字段  
**优先级**: P1 - 重要  

## 🎯 数据一致性约束

### 约束1: 数量一致性
```
订单数量 >= 已发货数量 >= 已开票数量
SaleOrderItem.quantity >= shippedQuantity >= invoicedQuantity
```

### 约束2: 金额一致性
```
订单金额 >= 已开票金额
SaleOrderItem.amount >= invoicedAmount

主表金额 = 明细汇总金额
SaleOrder.totalAmount = SUM(SaleOrderItem.amount)
```

### 约束3: 核销一致性
```
收款单核销金额 = 应收单核销金额
FinArReceiptOrder.appliedAmount = SUM(核销金额 by receiptId)
FinArReceivable.paidAmount = SUM(核销金额 by receivableId)
```

### 约束4: 状态一致性
```
订单状态与明细状态保持一致
应收状态与核销状态保持一致
收款状态与核销状态保持一致
```

## 📋 数据传递检查清单

### ✅ 已实现的传递
- [x] 销售订单 → 应收发票 (基础信息)
- [x] 收款单 ↔ 应收发票 (核销关联)
- [x] 订单明细金额计算和汇总
- [x] 核销状态同步更新

### ⚠️ 部分实现的传递
- [~] 销售订单主表金额汇总 (逻辑完整，字段缺失)
- [~] 收款单来源关联 (逻辑完整，字段缺失)
- [~] 核销经办人信息 (逻辑完整，字段缺失)

### ❌ 缺失的传递
- [ ] 销售订单 → 出库单
- [ ] 出库单 → 应收发票
- [ ] 应收发票明细传递
- [ ] 数量状态自动更新

## 🔮 优化建议

### 短期优化 (1-2周)
1. 完善现有数据传递的TODO项实现
2. 添加数据一致性校验机制
3. 编写数据传递单元测试

### 中期优化 (1个月)
1. 设计出库单业务流程
2. 创建应收发票明细表
3. 实现完整的数量状态更新

### 长期优化 (3个月)
1. 建立完整的业务流程闭环
2. 实现自动化的数据校验
3. 完善业务流程监控

---

**梳理完成时间**: 2025-06-24  
**梳理人员**: Augment Agent  
**下一步**: 执行数据衔接验证
