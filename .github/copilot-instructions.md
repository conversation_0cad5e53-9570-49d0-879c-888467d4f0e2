* * *

### **基于ruoyi-plus-vben5的前端开发规范（Vue3 + TypeScript + Vben）**

* * *

#### **一、技术栈规范（关键变更）**

| 分类 | 技术选型 |  
| --- | --- |  
| **核心框架** | Vue3 + Composition API + TypeScript |  
| **构建工具** | Vite 5.x |  
| **状态管理** | Pinia 3.x（内置集成） |  
| **UI组件库** | ⚡️ Ant Design Vue 3.x + Less 定制主题 |  
| **路由管理** | Vue Router 4.x（⚡️ 后端动态路由模式） |  
| **HTTP客户端** | ⚡️ Axios + Vben智能请求封装 |  
| **工具库** | ⚡️ Vben Hooks + Utility 工具集 |  
| **代码规范** | ESLint + Prettier + Stylelint（⚡️ Vben预设规则） |

* * *

#### **二、项目结构规范（Vben风格）**

```markdown  
src/  
├─ api/ # API模块化接口  
├─ assets/ # 静态资源  
├─ components/ # 公共组件  
├─ design/ # ⚡️ 样式/主题配置  
├─ enums/ # 枚举常量  
├─ hooks/ # 自定义Hook（优先使用Vben Hooks）  
├─ layouts/ # 布局组件  
├─ router/ # ⚡️ 路由配置（后端动态路由模式）  
├─ store/ # Pinia状态管理  
├─ utils/ # 工具函数  
├─ views/ # 页面组件（按模块划分）  
│ └─ sys/ # ⚡️ 系统管理模块（示例）  
└─ App.vue # 根组件  
```

* * *

#### **三、组件开发规范**

##### **1. 组件分类（Vben推荐）**

```typescript  
// 基础组件（/components/basic）  
defineProps<{  
  /** 按钮危险类型 */  
  danger?: boolean  
  /** 按钮类型 */  
  type?: 'primary' | 'dashed'  
}>()

// 业务组件（/views/sys/user/components）  
const emit = defineEmits<{  
  (e: 'submit', form: UserForm): void  
  (e: 'open-modal'): void ⚡️ // Vben事件命名规范  
}>()  
```

##### **2. 组合式API规范**

```vue  
<script setup lang="ts">  
// ⚡️ 推荐使用Vben Hooks  
import { useModal } from '@gx-design-vue/pro-modal'  
import { useTable } from '@gx-design-vue/pro-table'

// 标准顺序：1.Props 2.Emit 3.Hooks 4.Store 5.State  
const [registerModal, { openModal }] = useModal() // 弹窗Hook  
const [registerTable, { reload }] = useTable() // 表格Hook

// 数据获取使用async/await + Vben请求封装  
const userList = ref<UserItem[]>([])  
const fetchData = async () => {  
  userList.value = await getUserListApi(params)  
}  
</script>  
```

* * *

#### **四、状态管理规范（Pinia优化）**

##### **1. Store定义**

```typescript  
// store/modules/user.ts  
export const useUserStore = defineStore('user', {  
  state: (): UserState => ({  
    token: '',  
    userInfo: null  
  }),  
  actions: {  
    async login(params: LoginForm) {  
      // ⚡️ 使用Vben的请求封装  
      const data = await loginApi(params)  
      this.token = data.token  
      // 使用Vben的持久化存储  
      storage.set('TOKEN', data.token)  
    }  
  }  
})  
```

* * *

#### **五、API服务层规范**

##### **1. 请求封装（Vben风格）**

```typescript  
// ⚡️ 使用Vben的createRequest配置  
const { request } = createRequest({  
  // 请求配置  
  baseURL: '/api',  
  // 错误处理策略  
  errorMessageMode: 'modal',  
  // 响应数据处理  
  transformResponse: (res) => {  
    return res.data  
  }  
})

// api/system/user.ts  
export const getUserList = (params: ListParams) =>  
  request.get<ListResult<UserItem>>('/user/list', { params })  
```

* * *

#### **六、权限控制规范**

##### **1. 路由守卫（Vben动态路由）**

```typescript  
// ⚡️ 使用Vben的路由守卫逻辑  
router.beforeEach(async (to) => {  
  const userStore = useUserStore()

&nbsp; if (userStore.token) {  
    if (to.path === '/login') return '/'  
    // 动态路由处理（已集成在Vben中）  
    if (!userStore.isDynamicAddedRoute) {  
      await userStore.buildRoutesAction()  
      return to.fullPath  
    }  
  }  
})  
```

##### **2. 按钮级权限**

```vue  
<template>  
  <a-button  
    v-auth="'sys:user:add'" ⚡️ // 使用Vben的v-auth指令  
    @click="handleAdd"  
  >  
    新增用户  
  </a-button>  
</template>  
```

* * *

#### **七、样式规范（Ant Design风格）**

##### **1. Less主题定制**

```less  
// ⚡️ design/config.less  
@primary-color: #1890ff; // 全局主色  
@layout-header-background: #001529; // 布局背景

// 组件样式覆盖  
.ant-btn {  
  border-radius: 4px;  
}  
```

##### **2. 样式作用域**

```vue  
<style lang="less" scoped>  
.user-table {  
  :deep(.ant-table-row) { ⚡️ // Ant Design类名  
    td { padding: 12px; }  
  }  
}  
</style>  
```

* * *

#### **八、性能优化（Vben最佳实践）**

##### **1. 组件级优化**

```vue  
<!-- ⚡️ 使用Vben的懒加载组件 -->  
<BasicModal @register="registerModal" />

<!-- 图片使用Vben的预览组件 -->  
<ImagePreview :src="imgUrl" />  
```

##### **2. 打包优化配置**

```typescript  
// ⚡️ vite.config.ts 使用Vben预设配置  
export default defineConfig({  
  build: {  
    chunkSizeWarningLimit: 2000,  
    rollupOptions: {  
      output: {  
        manualChunks: {  
          vue: ['vue', 'pinia', 'vue-router'],  
          antv: ['@antv/x6']  
        }  
      }  
    }  
  }  
})  
```

* * *

#### **九、测试规范（Vben推荐）**

##### **1. 组件测试**

```typescript  
// ⚡️ 使用Vben的测试工具链  
import { mount } from '@vue/test-utils'

test('renders modal correctly', async () => {  
  const wrapper = mount(ProModal)  
  expect(wrapper.find('.ant-modal').exists()).toBe(true)  
})  
```

* * *

#### **十、代码提交规范（统一）**

```bash  
# 结合Vben提交规范  
git commit -m "feat(sys): 新增用户管理CRUD功能  
- 使用ProTable组件实现列表  
- 集成Vben权限控制  
- 优化表单验证逻辑"  
```

* * *

**ruoyi-plus-vben5 特别规范**：

1. **必须使用Vben提供的Pro组件**（ProTable、ProForm等）
2. **全局状态优先使用Vben Hooks**（如useModal、useLoading）
3. **主题定制必须通过design/目录配置**
4. **复杂表单必须使用ProForm组件**
5. **字典数据使用useDictStore管理**
6. **路由必须配置meta.permission实现自动鉴权**

> 该规范结合了RuoYi业务特性与Vben Admin的最佳实践，请确保开发过程中遵循框架约定式开发模式。
